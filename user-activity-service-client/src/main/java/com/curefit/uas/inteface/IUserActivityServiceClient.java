package com.curefit.uas.inteface;

import com.curefit.uas.enums.UserImageTag;
import com.curefit.uas.enums.streaks.StreakDayType;
import com.curefit.uas.pojo.M1JourneyTask;
import com.curefit.uas.pojo.entries.UserImageEntry;
import com.curefit.uas.requests.UserActivityStatsRequest;
import com.curefit.uas.responses.M1JourneyResponse;
import com.curefit.uas.responses.StreakResponse;
import com.curefit.uas.responses.UserActivityStatsResponse;

import java.util.List;
import java.util.Map;

public interface IUserActivityServiceClient {


    M1JourneyResponse getM1JourneyWidgetInfo(long userId, boolean showAll);

    M1JourneyTask completeStage(long userId, String state, boolean isDummy);

    UserActivityStatsResponse fetchActivityStatsForUser(UserActivityStatsRequest requestObject);
    StreakResponse getUserStreakDetails(String userId, String requestPeriodEndDate, boolean fetchDateToActivityDetailsMapForEndDatesWeek, boolean fetchEligibleActivitiesWithEndDateScore);
    Map<String, StreakDayType> getStreakDetailsForTimePeriod(String userId, String startDateString, String endDateString);
    List<UserImageEntry> getUserImageEntries(String userId, UserImageTag tag);
    UserImageEntry saveUserImageEntry(UserImageEntry userImageEntry);
};
