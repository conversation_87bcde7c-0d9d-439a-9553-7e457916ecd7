package com.curefit.uas.impl;

import com.curefit.uas.AbstractHttpClient;
import com.curefit.uas.enums.UserImageTag;
import com.curefit.uas.enums.streaks.StreakDayType;
import com.curefit.uas.inteface.IUserActivityServiceClient;
import com.curefit.uas.pojo.M1JourneyTask;
import com.curefit.uas.pojo.entries.UserImageEntry;
import com.curefit.uas.requests.UserActivityStatsRequest;
import com.curefit.uas.responses.M1JourneyResponse;
import com.curefit.uas.responses.StreakResponse;
import com.curefit.uas.responses.UserActivityStatsResponse;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableMap;
import io.sentry.protocol.User;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class UserActivityServiceClient extends AbstractHttpClient implements IUserActivityServiceClient {

        private String baseUrl;
        private Map<String, String> headers;

        UserActivityServiceClient(@Value("${user-activity-service.url}") String baseUrl) {
                this.baseUrl = baseUrl;
                this.headers = new HashMap<>();
        }

        public M1JourneyResponse getM1JourneyWidgetInfo(long userId, boolean showAll) {
                Map<String, String> params = ImmutableMap.of("userId", Long.toString(userId), "showAll", Boolean.toString(showAll));
                M1JourneyResponse response = get(this.baseUrl + "/m1Journey/getWidgetInfo", params, this.headers, new TypeReference<M1JourneyResponse>() {});
                return response;
        }

        public M1JourneyTask completeStage(long userId, String state, boolean isDummy) {
                Map<String, String> params = ImmutableMap.of("userId", Long.toString(userId), "taskName", state, "isDummy", Boolean.toString(isDummy));
                return (M1JourneyTask)post(this.baseUrl + "/m1Journey/completeStage", params, this.headers, new TypeReference<M1JourneyTask>() {});
        }


        public UserActivityStatsResponse fetchActivityStatsForUser(UserActivityStatsRequest requestObject){
                Map<String, String> params = ImmutableMap.of("userId", requestObject.getUserId(), "activityType", requestObject.getActivityType().toString(), "activityId", requestObject.getActivityId(), "userActivityValue", requestObject.getUserActivityValue().toString());
                return (UserActivityStatsResponse)post(this.baseUrl+ "/user-activity-stats/user", params, this.headers, new TypeReference<UserActivityStatsResponse>() {});
        }
        
        public StreakResponse getUserStreakDetails(String userId, String requestPeriodEndDate, boolean fetchDateToDayTypeMapForLast2Weeks, boolean fetchEligibleActivitiesWithEndDateScore) {
                Map<String, String> params = ImmutableMap.of(
                        "fetchDateToDayTypeMapForLast2Weeks", String.valueOf(fetchDateToDayTypeMapForLast2Weeks),
                        "fetchEligibleActivitiesWithEndDateScore", String.valueOf(fetchEligibleActivitiesWithEndDateScore),
                        "requestPeriodEndDate", requestPeriodEndDate
                );
                return (StreakResponse)get(this.baseUrl + "/user-streaks/getCurrentStreak/" + userId, params, this.headers, new TypeReference<StreakResponse>() {});
        }
        public Map<String, StreakDayType> getStreakDetailsForTimePeriod(String userId, String startDateString, String endDateString) {
                Map<String, String> params = ImmutableMap.of(
                        "startDate", startDateString,
                        "endDate", endDateString
                );
                return (Map<String, StreakDayType>)get(this.baseUrl + "/user-streaks/getStreakDetailsForTimePeriod/" + userId, params, this.headers, new TypeReference<Map<String, StreakDayType>>() {});
        }

        public List<UserImageEntry> getUserImageEntries(String userId, UserImageTag tag) {
                Map<String, String> params = ImmutableMap.of(
                        "userId", userId,
                        "tag", tag.toString()
                );
                return (List<UserImageEntry>)get(this.baseUrl + "/userImage/", params, this.headers, new TypeReference<List<UserImageEntry>>() {});
        }

        public UserImageEntry saveUserImageEntry(UserImageEntry userImageEntry) {
                return (UserImageEntry)post(this.baseUrl + "/userImage/", userImageEntry, this.headers, new TypeReference<UserImageEntry>() {});
        }

}
