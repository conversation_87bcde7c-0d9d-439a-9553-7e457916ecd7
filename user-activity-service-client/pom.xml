<?xml version="1.0" encoding="UTF-8"?>
<project
	xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>user-activity-service</artifactId>
		<groupId>com.curefit</groupId>
		<version>2.4.14-PT5</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<artifactId>user-activity-service-client</artifactId>

	<dependencies>
		<dependency>
			<groupId>com.curefit</groupId>
			<artifactId>cf-commons-client</artifactId>
			<version>${curefit.common-sf.version}</version>
		</dependency>
        <dependency>
            <groupId>com.curefit</groupId>
            <artifactId>user-activity-service-commons</artifactId>
            <version>${project.parent.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.curefit</groupId>
            <artifactId>logging-common</artifactId>
			<version>${logging-service.version}</version>
            <scope>compile</scope>
        </dependency>

		<dependency>
			<groupId>io.github.resilience4j</groupId>
			<artifactId>resilience4j-spring-boot2</artifactId>
			<version>${resilience4j.version}</version>
		</dependency>
		<dependency>
			<groupId>io.github.resilience4j</groupId>
			<artifactId>resilience4j-circuitbreaker</artifactId>
			<version>${resilience4j.version}</version>
		</dependency>
		<dependency>
			<groupId>io.github.resilience4j</groupId>
			<artifactId>resilience4j-bulkhead</artifactId>
			<version>${resilience4j.version}</version>
		</dependency>
		<dependency>
			<groupId>io.github.resilience4j</groupId>
			<artifactId>resilience4j-ratelimiter</artifactId>
			<version>${resilience4j.version}</version>
		</dependency>
		<dependency>
			<groupId>io.github.resilience4j</groupId>
			<artifactId>resilience4j-retry</artifactId>
			<version>${resilience4j.version}</version>
		</dependency>
		<dependency>
			<groupId>io.github.resilience4j</groupId>
			<artifactId>resilience4j-annotations</artifactId>
			<version>${resilience4j.version}</version>
		</dependency>
	</dependencies>

	<repositories>
		<repository>
			<id>github</id>
			<url>https://maven.pkg.github.com/curefit/*</url>
			<snapshots>
				<enabled>true</enabled>
				<updatePolicy>never</updatePolicy>
			</snapshots>
		</repository>
	</repositories>
</project>
