//package com.curefit.uas.consumers.fitnessReport;
//
//import com.curefit.commons.messaging.consumer.BaseSqsConsumer;
//import com.curefit.uas.enums.ActivityType;
//import com.curefit.uas.pojo.fitnessReport.CultClassUpdateFitnessReportBody;
//import com.curefit.uas.services.fitnessReportService.UpdateFitnessReportServiceFactory;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Profile;
//import software.amazon.awssdk.regions.Region;
//import software.amazon.awssdk.services.sqs.model.Message;
//
//import java.util.List;
//
//@Configuration
//@Slf4j
//@Profile("disabled")
//public class CultClassFitnessReportSqsConsumer extends BaseSqsConsumer {
//    @Autowired
//    ObjectMapper objectMapper;
//
//    @Autowired
//    UpdateFitnessReportServiceFactory updateFitnessReportServiceFactory;
//    
//    public CultClassFitnessReportSqsConsumer(@Value("${consumer.cult-class-update-fitness-report.queueUrl}") String queueUrl) {
//        super(queueUrl, Region.AP_SOUTH_1, 1L, 10);
//    }
//
//    @Override
//    public List<Boolean> process(List<Message> list) {
//        return list.stream().map(msg -> {
//            try {
//                return processMsg(msg);
//            } catch (Exception ex) {
//                rollbarService.error(ex, "Error while handling update fitness report event for msg = " + msg.messageId());
//                return false;
//            }
//        }).toList();
//    }
//    private Boolean processMsg(Message message) throws Exception {
//        CultClassUpdateFitnessReportBody msgBody = objectMapper.readValue(message.body(), CultClassUpdateFitnessReportBody.class);
//        return updateFitnessReportServiceFactory.updateFitnessReport(ActivityType.CULT_CLASS, msgBody);
//    }
//}