package com.curefit.uas.consumers.fitnessReport;

import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.messaging.consumer.BaseSqsConsumer;
import com.curefit.uas.enums.ActivityType;
import com.curefit.uas.pojo.fitnessReport.FitnessReportEntry;
import com.curefit.uas.services.fitnessReportService.UpdateFitnessReportServiceFactory;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sqs.model.Message;
import software.amazon.awssdk.services.sqs.model.MessageAttributeValue;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

//@Component
@Slf4j
@Getter
public class UpdateFitnessReportSqsConsumer extends BaseSqsConsumer {
//    @Autowired
    private final ObjectMapper objectMapper;
//    @Autowired
    private final UpdateFitnessReportServiceFactory updateFitnessReportServiceFactory;
    
//    @Autowired
    private final RollbarService rollbarService;
    
//    @Value("${consumer.update-fitness-report.parallelProcessingBatchSize}")
    private final int parallelProcessingBatchSize;
    public final String consumerId;
    
    static final String ACTIVITY_TYPE = "ACTIVITY_TYPE";
    
    public UpdateFitnessReportSqsConsumer(String consumerId, String queueName, int batchSize, int parallelProcessingBatchSize, ObjectMapper objectMapper, UpdateFitnessReportServiceFactory updateFitnessReportServiceFactory, RollbarService rollbarService) {
        super(queueName, Region.AP_SOUTH_1, 1L, batchSize);
        this.objectMapper = objectMapper;
        this.updateFitnessReportServiceFactory = updateFitnessReportServiceFactory;
        this.rollbarService = rollbarService;
        this.parallelProcessingBatchSize = parallelProcessingBatchSize;
        this.consumerId = consumerId;
        log.info("UpdateFitnessReportSqsConsumer started with Configuration: consumerId={}, queueName={}, batchSize={}, parallelProcessingBatchSize={}",
                consumerId, queueName, batchSize, parallelProcessingBatchSize);
    }
    
//    public UpdateFitnessReportSqsConsumer(@Value("${consumer.update-fitness-report.queueUrl}") String queueName, @Value("${consumer.update-fitness-report.batchSize}") int batchSize) {
//        super(queueName, Region.AP_SOUTH_1, 1L, batchSize);
//        log.info("UpdateFitnessReportSqsConsumer started with Configuration " + queueName);
//    }
    
    @Override
    public List<Boolean> process(List<Message> list) {
        List<List<Message>> listOfLists = new ArrayList<>();
        int size = list.size();
        for (int i = 0; i < size; i += this.parallelProcessingBatchSize) {
            listOfLists.add(new ArrayList<>(list.subList(i, Math.min(size, i + this.parallelProcessingBatchSize))));
        }
        List<CompletableFuture<Boolean>> futures = list.stream().map(msg -> CompletableFuture.supplyAsync(() -> {
                String logTag = this.consumerId + "::msgId=" + msg.messageId() + "::uuid=" + UUID.randomUUID() + " ";
                try {
                    log.info("{}received msg with attributes = {} body = {}", logTag, msg.messageAttributes(), msg.body());
                    FitnessReportEntry res = processMsg(msg, logTag);
//                    log.info("{}processed msg with result = {}", logTag, res);
                    return true;
                } catch (Exception ex) {
                    this.rollbarService.error(ex, logTag + "Error while handling update fitness report event for msg = " + msg.messageId());
                    return false;
                }
            }
        )).toList();
        
        return futures.stream().map(future -> {
            try {
                return future.get();
            } catch(Exception ex) {
                this.rollbarService.error(ex, "Error while handling fitness report event");
                return false;
            }
        }).toList();
    }
    private FitnessReportEntry processMsg(Message message, String logTag) throws Exception {
        Map<String, MessageAttributeValue> messageAttributeValueMap =  message.messageAttributes();
        ActivityType activityType = ActivityType.valueOf(messageAttributeValueMap.get(ACTIVITY_TYPE).stringValue());
        return this.updateFitnessReportServiceFactory.updateFitnessReport(activityType, message.body(), logTag);
    }
}
