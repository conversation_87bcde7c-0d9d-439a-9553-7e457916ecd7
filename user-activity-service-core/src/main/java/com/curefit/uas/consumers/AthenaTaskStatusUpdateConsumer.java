package com.curefit.uas.consumers;

import com.curefit.athena.service.entry.AthenaTaskEntry;
import com.curefit.athena.service.enums.TaskStatus;
import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.messaging.consumer.BaseSqsConsumer;
import com.curefit.uas.services.athena.AthenaNotificationHandlerFactory;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sqs.model.Message;
import software.amazon.awssdk.services.sqs.model.MessageAttributeValue;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Configuration
@Slf4j
public class AthenaTaskStatusUpdateConsumer extends BaseSqsConsumer {

    @Autowired
    ObjectMapper objectMapper;
    
    @Autowired
    AthenaNotificationHandlerFactory handlerFactory;

    static final String STATUS_KEY = "status";

    public AthenaTaskStatusUpdateConsumer(@Value("${consumer.athena-notification.queueUrl}") String queueName) {
        super(queueName, Region.AP_SOUTH_1, 1L, 1);
    }

    @Override
    public List<Boolean> process(List<Message> list) {
        return list.stream().map(message -> {
            try {
                return process(message);
            } catch (Exception e) {
                log.error("Error while handling athena notification event ", e);
                rollbarService.error(e, "Error while handling athena notification event");
                return false;
            }
        }).collect(Collectors.toList());
    }

    public boolean process(Message message) throws JsonProcessingException, BaseException {
        Map<String, MessageAttributeValue> attributes = message.messageAttributes();
        AthenaTaskEntry athenaTaskEntry = objectMapper.readValue(message.body(), AthenaTaskEntry.class);
        TaskStatus taskStatus = Enum.valueOf(TaskStatus.class, attributes.get(STATUS_KEY).stringValue());
        try {
            String source = athenaTaskEntry.getSource();

            // Log source and message details for debugging
            log.info("ASR: Processing Athena notification - source: '{}', taskId: {}, status: {}, attributes: {}",
                    source, athenaTaskEntry.getId(), taskStatus, attributes);

            // Use factory to get appropriate handler based on source
            return handlerFactory.getHandler(source)
                    .handleNotification(athenaTaskEntry, taskStatus);
        } catch (Exception e) {
            rollbarService.error(e, "Error processing Athena task notification for source: " + athenaTaskEntry.getSource() + " and taskId " + athenaTaskEntry.getId());
            throw e;
        }
    }
}
