package com.curefit.uas.consumers;

import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.messaging.consumer.BaseSqsConsumer;
import com.curefit.logging.models.ActivityTypeDS;
import com.curefit.uas.cache.UASLockService;
import com.curefit.uas.entities.streaks.StreakEntity;
import com.curefit.uas.pojo.streak.activityDetails.BaseActivityDetails;
import com.curefit.uas.services.streak.ActivityStreakEvaluatorService;
import com.curefit.uas.services.streak.StreakService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sqs.model.Message;

import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Getter
public class ActivityStoreSqsConsumer extends BaseSqsConsumer {
    private final ObjectMapper objectMapper;
    private final RollbarService rollbarService;
    private final int parallelProcessingBatchSize;
    public final String consumerId;
    private final StreakService streakService;
    private final UASLockService lockService;
    private final ActivityStreakEvaluatorService activityStreakEvaluatorService;
    
    static final String ACTIVITY_TYPE = "ACTIVITY_TYPE";
    static final String USER_ID = "USER_ID";
    
    public ActivityStoreSqsConsumer(String consumerId, String queueName, int batchSize, int parallelProcessingBatchSize,
                                    StreakService streakService,
                                    ObjectMapper objectMapper,
                                    RollbarService rollbarService, UASLockService lockService, ActivityStreakEvaluatorService activityStreakEvaluatorService) {
        super(queueName, Region.AP_SOUTH_1, 1L, batchSize);
        this.streakService = streakService;
        this.objectMapper = objectMapper;
        this.rollbarService = rollbarService;
        this.parallelProcessingBatchSize = parallelProcessingBatchSize;
        this.consumerId = consumerId;
        this.lockService = lockService;
        this.activityStreakEvaluatorService = activityStreakEvaluatorService;
        log.info("ActivityStoreSqsConsumer started with Configuration: consumerId={}, queueName={}, batchSize={}, parallelProcessingBatchSize={}",
                consumerId, queueName, batchSize, parallelProcessingBatchSize);
    }
    
    @Override
    public List<Boolean> process(List<Message> list) {
        List<CompletableFuture<Boolean>> futures = list.stream().map(msg -> CompletableFuture.supplyAsync(() -> {
                String activityTypeString = msg.messageAttributes().get(ACTIVITY_TYPE).stringValue();
                String userId = msg.messageAttributes().get(USER_ID).stringValue();
                try {
                    ActivityTypeDS activityTypeDS = ActivityTypeDS.valueOf(activityTypeString);
                    BaseActivityDetails currentActivityDetails = activityStreakEvaluatorService.getActivityDetailsFromString(activityTypeDS, msg.body());
                    log.info("{} Processing Message with id = {} and userId = {} and activityIdempotenceKey = {}", this.consumerId, msg.messageId(), userId, currentActivityDetails.getIdempotenceKey()); // Log start
                    StreakEntity streakEntity = this.streakService.processActivityWithLock(userId, currentActivityDetails, activityTypeDS).get();
                    return true;
                } catch(Exception ex) {
                    this.rollbarService.error(ex, "Error while handling streak activity event for msg = " + msg.messageId());
                    return false;
                }
            }
        )).toList();
        return futures.stream().map(future -> {
            try {
                return future.get();
            } catch(Exception ex) {
                this.rollbarService.error(ex, "Error while handling streak activity event");
                return false;
            }
        }).toList();
    }
}
