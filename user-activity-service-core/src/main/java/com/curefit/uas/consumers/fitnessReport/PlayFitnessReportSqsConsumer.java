//package com.curefit.uas.consumers.fitnessReport;
//
//import com.curefit.commons.messaging.consumer.BaseSqsConsumer;
//import com.curefit.uas.enums.ActivityType;
//import com.curefit.uas.pojo.fitnessReport.PlayUpdateFitnessReportBody;
//import com.curefit.uas.services.fitnessReportService.UpdateFitnessReportServiceFactory;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Profile;
//import software.amazon.awssdk.regions.Region;
//import software.amazon.awssdk.services.sqs.model.Message;
//import software.amazon.awssdk.services.sqs.model.MessageAttributeValue;
//
//import java.util.List;
//import java.util.Map;
//
////@Component
//@Configuration
//@Slf4j
//@Profile("disabled")
//public class PlayFitnessReportSqsConsumer extends BaseSqsConsumer {
//    @Autowired
//    ObjectMapper objectMapper;
//
//    @Autowired
//    UpdateFitnessReportServiceFactory updateFitnessReportServiceFactory;
//
//    public PlayFitnessReportSqsConsumer(@Value("${consumer.play-update-fitness-report.queueUrl}") String queueName) {
//        super(queueName, Region.AP_SOUTH_1, 1L, 10);
//    }
//
//    @Override
//    public List<Boolean> process(List<Message> list) {
//        return list.stream().map(msg -> {
//            try {
//                boolean res = processMsg(msg);
//                log.info("Update fitness report event for msg id = {} had result = {}", msg.messageId(), res);
//                return res;
//            } catch (Exception ex) {
//                rollbarService.error(ex, "Error while handling update fitness report event for msg = " + msg.messageId());
//                return false;
//            }
//        }).toList();
//    }
//    private Boolean processMsg(Message message) throws Exception {
//        Map<String, MessageAttributeValue> messageAttributeValueMap =  message.messageAttributes();
//        PlayUpdateFitnessReportBody playUpdateFitnessReportBody = objectMapper.readValue(message.body(), PlayUpdateFitnessReportBody.class);
//        return updateFitnessReportServiceFactory.updateFitnessReport(ActivityType.PLAY, playUpdateFitnessReportBody);
//    }
//}
