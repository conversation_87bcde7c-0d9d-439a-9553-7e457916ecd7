package com.curefit.uas.consumers;

import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.messaging.consumer.BaseSqsConsumer;
import com.curefit.uas.entities.streaks.StreakPauseEntity;
import com.curefit.uas.services.streak.StreakService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sqs.model.Message;

import java.util.List;

@Slf4j
@Component
@FieldDefaults(level = AccessLevel.PRIVATE)
@ConditionalOnProperty(prefix = "consumer.membership-updates", name = "enabled", havingValue = "true", matchIfMissing = false)
public class MembershipUpdatesSqsConsumer extends BaseSqsConsumer {
    @Autowired
    ObjectMapper objectMapper;
    @Autowired
    RollbarService rollbarService;
    @Autowired
    StreakService streakService;
    
    
    static final String EVENT_TYPE = "EVENT_TYPE";
    static final String USER_ID = "USER_ID";
    
    public MembershipUpdatesSqsConsumer(
            @Value("${consumer.membership-updates.queueUrl}") String membershipUpdatesConsumerQueueUrl,
            @Value("${consumer.membership-updates.batchSize}") Integer membershipUpdatesConsumerBatchSize) {
        super(membershipUpdatesConsumerQueueUrl, Region.AP_SOUTH_1, 1L, membershipUpdatesConsumerBatchSize);
        log.info("MembershipUpdatesSqsConsumer started with Configuration: queueName={}, batchSize={}", membershipUpdatesConsumerQueueUrl, membershipUpdatesConsumerBatchSize);
    }
    
    @Override
    public List<Boolean> process(List<Message> list) {
        return list.stream().map(msg -> {
            try {
                String eventType = msg.messageAttributes().get(EVENT_TYPE).stringValue();
                String userId = msg.messageAttributes().get(USER_ID).stringValue();
                log.info("MembershipUpdatesSqsConsumer Message Received with id = {} and userId = {} and eventType={}", msg.messageId(), userId, eventType); // Log start
                
                StreakPauseEntity streakPauseEntity = streakService.consumeMembershipPauseWithLock(eventType, userId).orElse(null);
                if (streakPauseEntity == null) {
                    log.error("MembershipUpdatesSqsConsumer Message Not Processed with id = {} and userId = {} and eventType={}", msg.messageId(), userId, eventType);
                } else {
                    log.info("MembershipUpdatesSqsConsumer Message Streak Paused  with id = {} and userId = {} and eventType={} streakPauseEntity.getId={}", msg.messageId(), userId, eventType, streakPauseEntity.getId());
                }
                return true;
            } catch(Exception ex) {
                rollbarService.error(ex, "Error while handling membership updates event for msgId = " + msg.messageId());
                return false;
            }
        }).toList();
    }
}
