package com.curefit.uas.entities;

import com.curefit.commons.sf.model.BaseMongoEntity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * Entity for storing Achievement Showcase data in MongoDB
 * Based on the exact contract format specified
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
@Document(collection = "achievement_showcase_reports")
public class AchievementShowcaseEntity extends BaseMongoEntity {
    
    @Indexed
    @JsonProperty("user_id")
    Long userId;

    @JsonProperty("classes_attended")
    Integer classesAttended;
    @JsonProperty("calories")
    Integer calories;
    @JsonProperty("total_minutes_worked")
    Integer totalMinutesWorked;
    @JsonProperty("weeks_active")
    Integer weeksActive;

    @JsonProperty("favorate_format_1")
    String favorateFormat1;
    @JsonProperty("favorate_format_1_classes")
    Integer favorateFormat1Classes;
    @JsonProperty("formats")
    Integer formats;
    @JsonProperty("favorate_format_2")
    String favorateFormat2;
    @JsonProperty("favorate_format_3")
    String favorateFormat3;

    @JsonProperty("squad_friends_count")
    Integer squadFriendsCount;

    @JsonProperty("trainer_name")
    String trainerName;
    @JsonProperty("trainer_classes")
    Integer trainerClasses;

    @JsonProperty("pack_utilisation_count")
    Float packUtilisationCount;
    @JsonProperty("country_percentile")
    Float countryPercentile;

    @JsonProperty("cities_count")
    Integer citiesCount;
    @JsonProperty("favourite_city_1")
    String favouriteCity1;
    @JsonProperty("favourite_city_2")
    String favouriteCity2;
    @JsonProperty("favourite_city_3")
    String favouriteCity3;
    @JsonProperty("centers_visited")
    Integer centersVisited;
    
    // Metadata
    @Indexed
    String reportDate; // YYYY-MM-DD format
    String athenaTaskId;
    String querySource;
    Long processedAt;
}
