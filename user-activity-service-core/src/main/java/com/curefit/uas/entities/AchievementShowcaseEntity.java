package com.curefit.uas.entities;

import com.curefit.commons.sf.model.BaseMongoEntity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * Entity for storing Achievement Showcase data in MongoDB
 * Based on the exact contract format specified
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
@Document(collection = "achievement_showcase_reports")
public class AchievementShowcaseEntity extends BaseMongoEntity {
    
    @Indexed
    Long userId;
    
    Integer classesAttended;
    Integer calories;
    Integer totalMinutesWorked;
    Integer weeksActive;
    
    String favorateFormat1;
    Integer favorateFormat1Classes;
    Integer formats;
    String favorateFormat2;
    String favorateFormat3;
    
    Integer squadFriendsCount;
    
    String trainerName;
    Integer trainerClasses;
    
    Float packUtilisationCount;
    Float countryPercentile;
    
    Integer citiesCount;
    String favouriteCity1;
    String favouriteCity2;
    String favouriteCity3;
    Integer centersVisited;
    
    // Metadata
    @Indexed
    String reportDate; // YYYY-MM-DD format
    String athenaTaskId;
    String querySource;
    Long processedAt;
}
