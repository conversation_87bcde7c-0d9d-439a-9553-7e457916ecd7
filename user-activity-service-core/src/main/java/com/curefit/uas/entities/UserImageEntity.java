package com.curefit.uas.entities;

import com.curefit.commons.sf.model.BaseMongoEntity;
import com.curefit.uas.enums.UserImageTag;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.sql.Timestamp;
import java.util.Date;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@Document("user_images")
public class UserImageEntity extends BaseMongoEntity {
    @NotNull
    Long userId;

    @NotNull
    String imgUrl;

    @Pattern(regexp = "\\d{4}-\\d{2}-\\d{2}", message = "Date must be in the format YYYY-MM-DD")
    @NotNull
    String date;

    @NotNull
    UserImageTag tag;

    String caption;

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    Timestamp deletedAt;
}
