package com.curefit.uas.controllers;

import com.curefit.uas.entities.UserImageEntity;
import com.curefit.uas.enums.UserImageTag;
import com.curefit.uas.mapper.CommonMapper;
import com.curefit.uas.pojo.entries.UserImageEntry;
import com.curefit.uas.repository.mongo.UserImageRepository;
import com.curefit.uas.responses.FitnessReportResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/userImage")
@RequiredArgsConstructor
@Validated
@Slf4j
public class UserImageController {

    private final UserImageRepository userImageRepository;
    private final CommonMapper commonMapper;

    @GetMapping("/")
    public List<UserImageEntry> getLatestReportForUserId(@RequestParam("userId") Long userId, @RequestParam UserImageTag tag) {
        log.info("ptDebugUAS:1");
        return userImageRepository.findAllByUserIdAndTag(userId, tag);
    }

    @PostMapping("/")
    public UserImageEntry uploadUserImage(@RequestBody @Valid UserImageEntry userImageEntry) {
        return commonMapper.transform(userImageRepository.save(commonMapper.transform(userImageEntry)));
    }

}
