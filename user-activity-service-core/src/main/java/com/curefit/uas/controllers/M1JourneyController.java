package com.curefit.uas.controllers;

import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.uas.cache.CloseableLock;
import com.curefit.uas.cache.UASLockService;
import com.curefit.uas.pojo.M1JourneyTask;
import com.curefit.uas.requests.CompleteStateRequest;
import com.curefit.uas.responses.M1JourneyResponse;
import com.curefit.uas.services.M1JourneyService;
import com.rollbar.notifier.Rollbar;
import lombok.extern.slf4j.Slf4j;
import org.quartz.impl.jdbcjobstore.LockException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.IOException;

@Slf4j
@RestController
@RequestMapping("/m1Journey")
public class M1JourneyController{

    @Autowired
    M1JourneyService service;

    @Autowired
    RollbarService rollbar;

    @Autowired
    private UASLockService lockService;

    @GetMapping("/getWidgetInfo")
    public M1JourneyResponse getWidgetInfo(@RequestParam("userId") String userId,
                                           @RequestParam("showAll") Boolean showAll) {
        try {
            return service.getWidgetInfo(userId, showAll);
        } catch (Exception e) {
            String error = "Error in getWidgetInfo" + e.getMessage();
            log.error(error);
            rollbar.error(e, error);
            throw e;
        }

    }

    @PostMapping("/completeStage")
    public M1JourneyTask completeStage(@Valid @RequestBody CompleteStateRequest completeStateRequest) throws LockException, IOException {
        log.info("updateState USER ID:" + completeStateRequest.getUserId());
        log.info("updateState tostate :" + completeStateRequest.getTaskName());
        try(CloseableLock lock = lockService.acquire(completeStateRequest.getUserId() + completeStateRequest.getTaskName())) {
            return service.completeUserState(completeStateRequest.getUserId(), completeStateRequest.getTaskName(), completeStateRequest.getIsDummy());
        } catch (Exception e) {
            String error = "Error in completeStage" + e.getMessage();
            log.error(error);
            rollbar.error(e, error);
            throw e;
        }
    }
}
