package com.curefit.uas.controllers;

import com.curefit.commons.sf.controller.BaseController;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.uas.entities.TaskEntity;
import com.curefit.uas.pojo.entries.Task;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/task")
public class TaskController extends BaseController<TaskEntity, Task> {

    public TaskController(BaseMySQLService<TaskEntity, Task> baseMySQLService) {
        super(baseMySQLService);
    }


}
