package com.curefit.uas.controllers;

import com.curefit.uas.entities.streaks.StreakConfigEntity;
import com.curefit.uas.entities.streaks.StreakEntity;
import com.curefit.uas.entities.streaks.StreakPseudoActivityEntity;
import com.curefit.uas.enums.streaks.StreakDayType;
import com.curefit.uas.enums.streaks.StreakPauseType;
import com.curefit.uas.enums.streaks.StreakRestDaysResetInterval;
import com.curefit.uas.pojo.StreakActivityRewardConfig;
import com.curefit.uas.repository.mongo.streaks.IStreakPseudoActivityRepository;
import com.curefit.uas.responses.StreakResponse;
import com.curefit.uas.services.longRunningTasks.streakBreak.ExpiredStreaksProcessingTaskManager;
import com.curefit.uas.services.streak.StreakService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

@Slf4j
@RestController
@RequestMapping(value = "/user-streaks")
@Validated
public class StreakController {
    @Autowired
    StreakService streakService;
    @Autowired
    ExpiredStreaksProcessingTaskManager expiredStreaksProcessingTaskManager;
    @Autowired
    IStreakPseudoActivityRepository streakPseudoActivityRepository;
    
    @GetMapping("/getCurrentStreak/{userId}")
    public StreakResponse getCurrentStreak(@PathVariable String userId,
                                           @RequestParam(required = false) boolean fetchDateToDayTypeMapForLast2Weeks,
                                           @RequestParam(required = false) boolean fetchEligibleActivitiesWithEndDateScore) throws ExecutionException, InterruptedException {
        return streakService.getUserCurrentStreak(userId, fetchDateToDayTypeMapForLast2Weeks, fetchEligibleActivitiesWithEndDateScore);
    }
    
    @GetMapping("/getStreakDetailsForTimePeriod/{userId}")
    public Map<String, StreakDayType> getStreakDetailsForTimePeriod(@PathVariable String userId,
                                                                    @RequestParam String startDate,
                                                                    @RequestParam String endDate) {
        return streakService.getStreakDetailsForTimePeriod(userId, startDate, endDate);
    }
    
    @DeleteMapping("/triggerExpiredStreaksUpdate")
    public void triggerExpiredStreaksUpdate(@RequestParam(defaultValue = "500") Integer pageSize, @RequestParam(required = false) String streakResetDate) {
        if (StringUtils.isBlank(streakResetDate)) streakResetDate = LocalDate.now().toString();
        expiredStreaksProcessingTaskManager.submitExpiredStreaksProcessingTask(pageSize, streakResetDate);
    }
    
    @PostMapping("/recomputeStreakForUserStartingFromDate")
    public StreakEntity recomputeStreakForUserStartingFromDate(@RequestParam String userId, @RequestParam(required = false) String requestedRecomputeDate) throws Exception {
        return streakService.recomputeStreakForUserStartingFromDate(userId, requestedRecomputeDate, true);
    }
    
    @PostMapping("/createStreakConfig")
    public StreakConfigEntity createStreakConfigEntity(
            @RequestParam String configId,
            @RequestParam String configName,
            @RequestParam String configDescription,
            @RequestParam(required = false)  StreakPauseType pauseType,
            @RequestParam Integer restDaysQuota,
            @RequestParam(required = false) StreakRestDaysResetInterval restDaysResetInterval,
            @RequestBody Map<String, List<StreakActivityRewardConfig>> activityRewardConfigMap) {
        return streakService.createStreakConfigEntity(
                configId, configName, configDescription, pauseType, restDaysQuota, restDaysResetInterval, activityRewardConfigMap);
    }
    
    @GetMapping("/getStreakConfig/{configId}")
    public StreakConfigEntity getStreakConfigByConfigId(@PathVariable String configId) {
        return streakService.getStreakConfigByConfigId(configId);
    }
    
    @PostMapping("/createStreakPseudoActivity")
    public StreakPseudoActivityEntity createStreakPseudoActivity(@RequestBody StreakPseudoActivityEntity streakPseudoActivityEntity) {
        return streakPseudoActivityRepository.save(streakPseudoActivityEntity);
    }
    
    @GetMapping("/getStreakPseudoActivities/{userId}")
    public List<StreakPseudoActivityEntity> getStreakPseudoActivities(
            @PathVariable String userId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate
            ) {
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            return streakPseudoActivityRepository.findAllByUserId(userId);
        }
        return streakPseudoActivityRepository.findAllByUserIdAndDateInRange(userId, startDate, endDate);
    }
    
    // Mozart- https://mozart-ui.curefit.co/#/jobConfig/824
    @PostMapping("/inAppPn/sendNewStreakFirstRestDayConsumption/{segmentId}")
    void sendNewStreakFirstRestDayConsumption(@PathVariable @NotBlank String segmentId,
                                          @RequestParam(value = "batchOffset", defaultValue = "0") Integer batchOffset,
                                          @RequestParam(value = "batchSize", defaultValue = "100") Integer batchSize
    ) {
        streakService.sendNewStreakFirstRestDayConsumption(segmentId, batchOffset, batchSize);
    }
}
