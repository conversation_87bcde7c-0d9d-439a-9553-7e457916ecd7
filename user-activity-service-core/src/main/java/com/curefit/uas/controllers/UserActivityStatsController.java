package com.curefit.uas.controllers;

import com.curefit.commons.sf.controller.BaseController;
import com.curefit.uas.entities.UserActivityStatsEntity;
import com.curefit.uas.pojo.entries.UserActivityStats;
import com.curefit.uas.requests.UserActivityStatsRequest;
import com.curefit.uas.responses.UserActivityStatsResponse;
import com.curefit.uas.services.UserActivityStatsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping(value = "/user-activity-stats")
public class UserActivityStatsController extends BaseController<UserActivityStatsEntity, UserActivityStats> {

    UserActivityStatsService service;
    public UserActivityStatsController(UserActivityStatsService service) {
        super(service);
        this.service = service;
    }

    @RequestMapping(method = RequestMethod.POST, value = "/recompute")
    public void recomputeStats() {
        service.recomputeAllStats();
    }


    @RequestMapping(method = RequestMethod.POST, value = "/user")
    public UserActivityStatsResponse fetchActivityStatsForUser(@RequestBody UserActivityStatsRequest request) {
        return service.fetchActivityStatsForUser(request);
    }
}