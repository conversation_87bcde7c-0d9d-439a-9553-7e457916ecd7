package com.curefit.uas.controllers;

import com.curefit.commons.sf.controller.BaseController;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.uas.entities.UserTaskMappingEntity;
import com.curefit.uas.pojo.entries.UserTaskMapping;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/userTask")
public class UserTaskMappingController extends BaseController<UserTaskMappingEntity, UserTaskMapping> {

    public UserTaskMappingController(BaseMySQLService<UserTaskMappingEntity, UserTaskMapping> baseMySQLService) {
        super(baseMySQLService);
    }
}
