package com.curefit.uas.controllers;

import com.curefit.commons.sf.controller.BaseController;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.uas.entities.StateEntity;
import com.curefit.uas.pojo.entries.State;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/state")
public class StateController extends BaseController<StateEntity, State> {

    public StateController(BaseMySQLService<StateEntity, State> baseMySQLService) {
        super(baseMySQLService);
    }
}
