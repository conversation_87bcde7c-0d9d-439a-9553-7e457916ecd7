package com.curefit.uas.controllers;

import com.curefit.common.data.exception.BaseException;
import com.curefit.logging.models.ActivityStoreTransient;
import com.curefit.logging.models.request.ActivityStoreTransientSearchRequest;
import com.curefit.logging.models.response.ActivityResponse;
import com.curefit.uas.logging.service.UASLoggingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping(value = "/wearable")
public class WearableController {

    @Autowired
    private UASLoggingService uasLoggingService;

    @PostMapping("/sync")
    public String sync() {
        return "hello";
    }

    @PostMapping("/transient/search")
    public List<ActivityStoreTransient> getActivityStoreTransient(@RequestBody ActivityStoreTransientSearchRequest searchRequest) throws BaseException, IOException {
        return uasLoggingService.fetchActivityStoreTransient(searchRequest);
    }


    @PostMapping("/transient/upsert")
    public List<ActivityStoreTransient> upsertActivityStoreTransient(@RequestBody Map<String, Object> inputJson) throws Exception {
        log.info("WEARABLE: received upsert request for user transient activity data {}", inputJson);
        return uasLoggingService.upsertActivityStoreTransient(inputJson);
    }

    @PutMapping("/aggregated/log")
    public ActivityResponse logAggregatedMetrics(@RequestBody Map<String, Object> inputJson) throws Exception {
        log.info("Received aggregated health metrics log request: {}", inputJson);
        return uasLoggingService.processAndLogAggregatedMetrics(inputJson);
    }

    @GetMapping("/score/{classId}")
    public Map<String, Integer> getUserScoresForClass(@PathVariable String classId) throws Exception {
        log.info("Received request to fetch latest user score for class: {}", classId);
        return uasLoggingService.getUserScoresForClass(classId);
    }

    @PostMapping("/user/alias")
    public void publishUserAlias(@RequestBody Map<String, String> inputJson) throws Exception {
        log.info("Received request to publish alias: {}", inputJson);
        uasLoggingService.publishUserAliasEvent(inputJson);
    }

}