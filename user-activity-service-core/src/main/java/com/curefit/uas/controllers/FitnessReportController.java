package com.curefit.uas.controllers;

import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.cult.models.FitnessReportDetails;
import com.curefit.uas.pojo.fitnessReport.FitnessReportEntry;
import com.curefit.uas.requests.UpdateFitnessReportBody;
import com.curefit.uas.responses.FitnessReportResponse;
import com.curefit.uas.responses.FitnessReportSummaryResponse;
import com.curefit.uas.responses.FitnessReportTotalClassesAttendedResponse;
import com.curefit.uas.services.fitnessReportMigration.FitnessReportMigrationTaskManager;
import com.curefit.uas.services.fitnessReportService.FitnessReportService;
import com.curefit.uas.services.fitnessReportService.FitnessReportTransientService;
import com.curefit.uas.utils.TimeUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Pattern;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

@RestController
@RequestMapping("/fitnessReport")
@RequiredArgsConstructor
@Validated
public class FitnessReportController {
    final FitnessReportService fitnessReportService;
    final RollbarService rollbarService;
    private final FitnessReportMigrationTaskManager fitnessReportMigrationTaskManager;
    final FitnessReportTransientService fitnessReportTransientService;
    
    // GET /v1/fitnessReport
    @GetMapping("getWeeklyAugmentedResponseForUserAndWeekDate/{userId}/{weekDate}")
    public FitnessReportResponse getWeeklyAugmentedResponseForUserAndWeekDate(@PathVariable("userId") Long userId,
                                                                              @PathVariable("weekDate") @Pattern(regexp = TimeUtil.regexpForDate) String weekDate,
                                                                              @RequestParam(required = false, defaultValue = "false") Boolean updateUserMetrics
    ) throws ExecutionException, InterruptedException {
        if (updateUserMetrics) rollbarService.log("Fitness Report is trying to be updated in a get call");
        return fitnessReportTransientService.getCachedWeeklyAugmentedResponseForUserAndWeekDate(userId, weekDate);
    }
    
    // GET /v1/fitnessReport/bulk
    @GetMapping("getBulkResponseForUserIdsAndWeekDate/{weekDate}")
    public  Map<String, FitnessReportResponse> getBulkResponseForUserIdsAndWeekDate(@PathVariable("weekDate") @Pattern(regexp = TimeUtil.regexpForDate) String weekDate,
                                                                              @RequestParam("commaSeparatedUserIds") String commaSeparatedUserIds) {
        return fitnessReportTransientService.getBulkResponseForUserIdsAndWeekDate(weekDate, commaSeparatedUserIds);
    }
    
    // GET /v1/fitnessReport/latest
    @GetMapping("getLatestReportForUserId/{userId}")
    public FitnessReportResponse getLatestReportForUserId(@PathVariable("userId") Long userId) throws ExecutionException, InterruptedException {
        return fitnessReportTransientService.getLatestReportForUserId(userId);
    }
    // GET /v1/fitnessReport/summary
    @GetMapping("/summary/{userId}")
    public FitnessReportSummaryResponse getFitnessReportSummary(
            @PathVariable("userId") Long userId,
            @RequestParam("startDate") @Pattern(regexp = TimeUtil.regexpForDate) String startDate,
             @RequestParam(value = "limit", defaultValue = "10") int limit,
             @RequestParam(value = "offset", defaultValue = "0") int offset
    ) throws ExecutionException, InterruptedException {
        return fitnessReportTransientService.getFitnessReportSummary(userId, startDate, limit, offset);
    }
    
    // POST /v1/fitnessReport/totalClassesAttended/latest/userIds
    @PostMapping("/getTotalClassesAttendedForLatestFitnessReportForUserIds")
    public FitnessReportTotalClassesAttendedResponse getTotalClassesAttendedForLatestFitnessReportForUserIds(@RequestParam(defaultValue = "6", required = false) Integer pastNumberOfMonthsToQuery, @RequestBody List<Long> userIds) {
        return fitnessReportTransientService.getTotalClassesAttendedForLatestFitnessReportForUserIds(pastNumberOfMonthsToQuery, userIds);
    }
    
    @GetMapping("/previousNEntries")
    public List<FitnessReportEntry> getPreviousNFitnessReportsSortedNewToOld(@RequestParam("userId") String userId, @RequestParam("weekMonday") @Pattern(regexp = TimeUtil.regexpForDate) String weekMonday, @RequestParam("limit") int limit) {
        List<FitnessReportEntry> response = fitnessReportService.getPreviousNFitnessReportsSortedNewToOld(Long.valueOf(userId), weekMonday, limit);
        return response;
    }
    
    
    //ToDo:: Check if this id is mysql id now
    @PutMapping("updateFitnessReportById/{id}")
    public FitnessReportResponse updateFitnessReportById(@PathVariable("id") String id, @RequestBody UpdateFitnessReportBody requestBody) {
        FitnessReportResponse response = fitnessReportService.updateFitnessReportById(id, requestBody);
        return response;
    }
    
    @GetMapping("/getBatchedFitnessReportForMigration")
    public List<FitnessReportDetails> getBatchedFitnessReportForMigration(@RequestParam Long startingEntityId, @RequestParam Long endingEntityId) {
        try {
            return fitnessReportService.getBatchedFitnessReportForMigration(startingEntityId, endingEntityId).get();
        } catch(Exception e) {
            rollbarService.error(e, "Error in getBatchedFitnessReportForMigration");
            return new ArrayList<>();
        }
    }
    
    @PostMapping("/migrateFitnessReportData")
    public int migrateFitnessReportData(@RequestBody Map<String, Integer> requestBody) {
        return fitnessReportService.migrateFitnessReportData(requestBody);
    }
    
    @PostMapping("/submitFitnessReportMigrationTask")
    public void submitFitnessReportMigrationTask(@RequestParam Integer iterations, @RequestParam Integer startingId, @RequestParam Integer totalEntriesToMigrate, @RequestParam Integer batchSize, @RequestParam(defaultValue = "true") Boolean ignoreExistingData) {
        fitnessReportMigrationTaskManager.submitFitnessReportMigrationTask(iterations, startingId.longValue(), totalEntriesToMigrate.longValue(), batchSize.longValue(), ignoreExistingData);
    }
}
