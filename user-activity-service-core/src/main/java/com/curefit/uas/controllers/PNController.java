package com.curefit.uas.controllers;

import com.curefit.common.data.exception.BaseException;
import com.curefit.uas.services.metabase.AthenaService;
import com.curefit.uas.services.metabase.PNService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping(value = "/pn")
@Validated
public class PNController {
    @Autowired
    PNService pnService;
    @Autowired
    AthenaService athenaService;
    
    // Note:: Pass mozartJobConfigId as null if you do not want to use mozart job config
    @PostMapping("createAthenaTaskForMetabaseCard/{cardId}")
    public Boolean createAthenaTaskForMetabaseCard(@PathVariable @NotBlank String cardId, @RequestParam(required = false) String mozartJobConfigId, @RequestParam(required = false) String querySource) throws BaseException {

        // Log Achievement Showcase Report requests specifically
        if ("UAS:achievement-showcase-report".equals(querySource)) {
            log.info("ASR: Endpoint hit - Creating Athena task for Achievement Showcase Report with cardId: {}, mozartJobConfigId: {}, querySource: {}",
                    cardId, mozartJobConfigId, querySource);
        } else {
            log.info("ASR: Creating Athena task for cardId: {}, mozartJobConfigId: {}, querySource: {}",
                    cardId, mozartJobConfigId, querySource);
        }

        Boolean result = athenaService.createAthenaTaskForMetabaseCard(cardId, mozartJobConfigId, querySource);

        if ("UAS:achievement-showcase-report".equals(querySource)) {
            log.info("ASR: Endpoint response - Athena task creation result: {} for cardId: {}", result, cardId);
        }

        return result;
    }
    
    @PostMapping("sendPNForMetabaseCard")
    public void sendPNForMetabaseCard(@RequestBody Map<String, Object> requestBody) {
        pnService.sendPNForMetabaseCard(requestBody);
    }
    
//    @PostMapping("sendPNForMetabaseCard/{cardId}")
//    public void sendPNForMetabaseCard(@PathVariable @NotBlank String cardId) {
//        pnService.sendPNForMetabaseCard(cardId);
//    }
}
