package com.curefit.uas.controllers;

import com.curefit.uas.entities.AchievementShowcaseEntity;
import com.curefit.uas.services.AchievementShowcaseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;
import java.util.Map;

/**
 * Controller for Achievement Showcase operations
 */
@Slf4j
@RestController
@RequestMapping("/achievement-showcase")
@Validated
@RequiredArgsConstructor
public class AchievementShowcaseController {

    private final AchievementShowcaseService achievementShowcaseService;

    /**
     * Manual endpoint to dump Achievement Showcase data to database
     */
    @PostMapping("/dump")
    public ResponseEntity<Map<String, Object>> dumpDataToDatabase(
            @RequestBody List<Map<String, Object>> rawData,
            @RequestParam(defaultValue = "manual-dump") String taskId) {

        log.info("ASR: Manual dump endpoint hit with {} records, taskId: {}", rawData.size(), taskId);

        int savedCount = achievementShowcaseService.dumpDataToDatabase(rawData, taskId);

        Map<String, Object> response = Map.of(
            "success", true,
            "recordsReceived", rawData.size(),
            "recordsSaved", savedCount,
            "taskId", taskId
        );

        log.info("ASR: Manual dump completed - received: {}, saved: {}", rawData.size(), savedCount);
        return ResponseEntity.ok(response);
    }

    /**
     * Get Achievement Showcase data for a specific user
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<List<AchievementShowcaseEntity>> getDataForUser(
            @PathVariable @NotNull Long userId,
            @RequestParam(defaultValue = "10") @Min(1) int limit,
            @RequestParam(defaultValue = "0") @Min(0) int offset) {

        log.info("ASR: Fetching data for userId: {}, limit: {}, offset: {}", userId, limit, offset);

        List<AchievementShowcaseEntity> data = achievementShowcaseService.getDataForUser(userId, limit, offset);
        return ResponseEntity.ok(data);
    }

    /**
     * Get Achievement Showcase data for a specific date
     */
    @GetMapping("/date/{reportDate}")
    public ResponseEntity<List<AchievementShowcaseEntity>> getDataForDate(
            @PathVariable @Pattern(regexp = "\\d{4}-\\d{2}-\\d{2}", message = "Date must be in YYYY-MM-DD format") String reportDate,
            @RequestParam(defaultValue = "100") @Min(1) int limit,
            @RequestParam(defaultValue = "0") @Min(0) int offset) {

        log.info("ASR: Fetching data for date: {}, limit: {}, offset: {}", reportDate, limit, offset);

        List<AchievementShowcaseEntity> data = achievementShowcaseService.getDataForDate(reportDate, limit, offset);
        return ResponseEntity.ok(data);
    }

    /**
     * Get count of records for a specific date
     */
    @GetMapping("/count/date/{reportDate}")
    public ResponseEntity<Map<String, Object>> getCountForDate(
            @PathVariable @Pattern(regexp = "\\d{4}-\\d{2}-\\d{2}", message = "Date must be in YYYY-MM-DD format") String reportDate) {

        log.info("ASR: Getting count for date: {}", reportDate);

        long count = achievementShowcaseService.getCountForDate(reportDate);

        Map<String, Object> response = Map.of(
            "reportDate", reportDate,
            "count", count
        );

        return ResponseEntity.ok(response);
    }

    /**
     * Get count of records for today
     */
    @GetMapping("/count/today")
    public ResponseEntity<Map<String, Object>> getCountForToday() {
        String today = java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ISO_LOCAL_DATE);

        log.info("ASR: Getting count for today: {}", today);

        long count = achievementShowcaseService.getCountForDate(today);

        Map<String, Object> response = Map.of(
            "reportDate", today,
            "count", count
        );

        return ResponseEntity.ok(response);
    }
}
