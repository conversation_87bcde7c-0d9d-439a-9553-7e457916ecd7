package com.curefit.uas.controllers;

import com.curefit.uas.entities.AchievementShowcaseEntity;
import com.curefit.uas.services.AchievementShowcaseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Controller for Achievement Showcase operations
 */
@Slf4j
@RestController
@RequestMapping("/achievement-showcase")
@RequiredArgsConstructor
public class AchievementShowcaseController {

    private final AchievementShowcaseService achievementShowcaseService;

    /**
     * Dump Achievement Showcase data to database
     */
    @PostMapping("/dump")
    public ResponseEntity<Map<String, Object>> dumpData(
            @RequestBody List<Map<String, Object>> rawData,
            @RequestParam(defaultValue = "manual-dump") String taskId) {
        
        log.info("ASR: Dump endpoint hit with {} records", rawData.size());
        
        int savedCount = achievementShowcaseService.dumpDataToDatabase(rawData, taskId);
        
        return ResponseEntity.ok(Map.of(
            "success", true,
            "recordsSaved", savedCount
        ));
    }

    /**
     * Get data for user
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<List<AchievementShowcaseEntity>> getUserData(@PathVariable Long userId) {
        return ResponseEntity.ok(achievementShowcaseService.getDataForUser(userId, 10, 0));
    }

    /**
     * Get count for today
     */
    @GetMapping("/count/today")
    public ResponseEntity<Long> getTodayCount() {
        String today = java.time.LocalDate.now().toString();
        return ResponseEntity.ok(achievementShowcaseService.getCountForDate(today));
    }
}
