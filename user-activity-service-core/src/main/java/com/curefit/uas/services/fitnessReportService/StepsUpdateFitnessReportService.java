package com.curefit.uas.services.fitnessReportService;

import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.lock.LockService;
import com.curefit.cult.models.ClassDetails;
import com.curefit.cult.models.WorkoutDetails;
import com.curefit.gymfit.client.GymfitClient;
import com.curefit.hercules.client.HerculesService;
import com.curefit.metricservice.client.MetricClient;
import com.curefit.rashi.client.RashiClient;
import com.curefit.rashi.client.UserAttributesClient;
import com.curefit.segmentation.client.rest.SegmentationClient;
import com.curefit.segmentation.client.rest.UserSegmentClient;
import com.curefit.uas.constants.LockKey;
import com.curefit.uas.entities.FitnessReportEntity;
import com.curefit.uas.enums.ActivityType;
import com.curefit.uas.mapper.FitnessReportMapper;
import com.curefit.uas.pojo.fitnessReport.FitnessReportEntry;
import com.curefit.uas.pojo.fitnessReport.FitnessReportInterval;
import com.curefit.uas.pojo.fitnessReport.StepsData;
import com.curefit.uas.pojo.fitnessReport.StepsUpdateFitnessReportBody;
import com.curefit.uas.publisher.FitnessReportHabitAndMilestonesPublisher;
import com.curefit.uas.repository.mongo.FitnessReportRepository;
import com.curefit.uas.services.ConfigStoreWrapperService;
import com.curefit.uas.utils.FitnessReportConstants;
import com.curefit.uas.utils.TimeUtil;
import com.curefit.ufs.services.UfsService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

@Service
@Slf4j
class StepsUpdateFitnessReportService extends BaseUpdateFitnessReportService implements UpdateFitnessReportService {
    public StepsUpdateFitnessReportService(ObjectMapper objectMapper, FitnessReportMapper fitnessReportMapper, FitnessReportHabitAndMilestonesPublisher fitnessReportHabitAndMilestonesPublisher, HerculesService herculesService, ConfigStoreWrapperService configStore, RollbarService rollbarService, MetricClient metricClient, UfsService ufsService, GymfitClient gymfitClient, LockService lockService, UserSegmentClient userSegmentClient, SegmentationClient segmentationClient, UserAttributesClient userAttributesClient, RashiClient rashiClient, FitnessReportRepository fitnessReportRepository, UpdateFitnessReportHelperService updateFitnessReportHelperService) {
        super(objectMapper, fitnessReportMapper, fitnessReportHabitAndMilestonesPublisher, herculesService, configStore, rollbarService, metricClient, ufsService, gymfitClient, lockService, userSegmentClient, segmentationClient, userAttributesClient, rashiClient, fitnessReportRepository, updateFitnessReportHelperService);
    }
    
    @Override
    public boolean isEligibleActivityType(ActivityType activityType) {
        return ActivityType.STEPS.equals(activityType);
    }
    
    @Override
    public FitnessReportEntry processClass(String sqsMsgBodyString, String logTag) throws Exception {
        StepsUpdateFitnessReportBody stepsUpdateFitnessReportBody = objectMapper.readValue(sqsMsgBodyString, StepsUpdateFitnessReportBody.class);
        Long userId = stepsUpdateFitnessReportBody.getUserId();
        List<StepsData> stepsData = stepsUpdateFitnessReportBody.getData();
        List<String> locks = new ArrayList<>();
        List<FitnessReportEntity> reports = new ArrayList<>();
        List<String> lockStrings = new ArrayList<>();
        try {
            stepsData.sort(Comparator.comparing(StepsData::getDate));
            String mondayOfWeek = TimeUtil.getMondayOfRelativeWeek(stepsData.get(0).getDate(), 0);
            reports = fitnessReportRepository.findAfterDate(userId, mondayOfWeek);
            FitnessReportEntity previousReport = fitnessReportRepository.findLastEntryBeforeDateForUserId(userId, mondayOfWeek);
            reports.add(0, previousReport);
            
            for (StepsData data: stepsData) {
                String date = data.getDate();
                Integer steps = data.getSteps();
                if (steps > FitnessReportConstants.MIN_STEPS_FOR_FITNESS_REPORT_ACTIVITY) {
                    String currentWeekMonday = TimeUtil.getMondayOfRelativeWeek(date, 0);
                    String currentWeekSunday = TimeUtil.getSundayOfRelativeWeek(date, 0);
                    FitnessReportEntity currentWeekReport = reports.stream().filter(x -> x.getStartDate().equals(currentWeekMonday)).findFirst().orElse(null);
                    int currentWeekReportIndex = reports.indexOf(currentWeekReport);
                    if (currentWeekReport != null && currentWeekReport.getClassList().contains(FitnessReportUtils.getStepsDayKey(date))) {
                        // steps already processed for this day, ignore
                        log.info("Steps already processed for user = {} for this date = {}", userId, date);
                        continue;
                    }
                    String lockString = LockKey.FITNESS_REPORT_USER_INTERVAL_START_DATE.getValue() + userId + "_" + FitnessReportInterval.WEEKLY.toString() + "_" + currentWeekMonday;
                    if (!lockStrings.contains(lockString)) {
                        locks.add(lockService.acquire(lockString).get());
                        lockStrings.add(lockString);
                    }
                    ClassDetails classDetails = new ClassDetails();
                    classDetails.setId(FitnessReportUtils.getStepsDayKey(date));
                    classDetails.setDate(date);
                    classDetails.setTimezone(FitnessReportConstants.INDIA_TIMEZONE);
                    WorkoutDetails workoutDetails = new WorkoutDetails();
                    workoutDetails.setId(FitnessReportConstants.FITNESS_REPORT_STEPS_ID);
                    workoutDetails.setName(FitnessReportConstants.FITNESS_REPORT_STEPS_NAME);
                    FitnessReportEntity lastReport = currentWeekReportIndex >= 0 ? reports.get(currentWeekReportIndex - 1) : null;
                    FitnessReportEntity reportData = generateReportData(userId, classDetails, currentWeekReport, reports,
                            workoutDetails, null, lastReport);
                    if (currentWeekReport == null) {
                        reportData.setUserId(userId);
                        reportData.setStartDate(currentWeekMonday);
                        reportData.setEndDate(currentWeekSunday);
                        reportData.setInterval(FitnessReportInterval.WEEKLY);
                        int insertIndex = FitnessReportUtils.insertEntryInOrder(reports, reportData);
                        updateRelativeRecords(reports, Math.max(0, insertIndex - 1));
                    } else {
                        reports.set(currentWeekReportIndex, FitnessReportUtils.partialUpdateDataAndReturnDeepCopy(reports.get(currentWeekReportIndex), reportData));
                        updateRelativeRecords(reports, Math.max(0, currentWeekReportIndex - 1));
                    }
                }
            }
            log.info("StepsUpdateFitnessReportService::processSteps reports updated - {}", reports);
            bulkSaveReports(reports, userId);
            log.info("StepsUpdateFitnessReportService::processSteps reports save");
            
        } catch(Exception e) {
            rollbarService.error(e, logTag + "Error in processing steps update fitness report");
        } finally {
            int locksLeft = locks.size();
            for (int i = 0; i<locks.size(); i++) {
                try {
                    lockService.release(lockStrings.get(i), locks.get(i));
                    locksLeft--;
                } catch(IllegalAccessException e) {
                    rollbarService.error(e, "Error in releasing lock for steps update fitness report");
                }
            }
            log.info("StepsUpdateFitnessReportService::processSteps locks left to relase - {}", locksLeft);
        }
        return fitnessReportMapper.map(fitnessReportRepository.findTopByUserIdOrderByIdDesc(userId));
    }
    private void updateRelativeRecords(List<FitnessReportEntity> sortedEntries, int entryIndex) {
        for (int i = entryIndex + 1; i < sortedEntries.size(); i++) {
            sortedEntries.get(i).setTotalClassesAttended(sortedEntries.get(i - 1).getTotalClassesAttended() + sortedEntries.get(i).getClassesAttended());
            sortedEntries.get(i).setMaxClassesAttended(Math.max(sortedEntries.get(i).getMaxClassesAttended(), sortedEntries.get(i - 1).getMaxClassesAttended()));
            
            String prevMonday = TimeUtil.getMondayOfPreviousWeek(sortedEntries.get(i).getStartDate());
            sortedEntries.get(i).setStreak(sortedEntries.get(i - 1).getStartDate().equals(prevMonday) ? sortedEntries.get(i - 1).getStreak() + 1 : 1);
        }
    }
    private void bulkSaveReports(List<FitnessReportEntity> reports, Long userId) {
        for (FitnessReportEntity report: reports) {
            fitnessReportRepository.save(report);
        }
        if (CollectionUtils.isEmpty(reports)) return;
        FitnessReportEntity latestReport = reports.get(reports.size() - 1);
        try {
            publishFitnessReportUpdateRashiEvent(userId, latestReport.getClassesAttended() != null ? latestReport.getClassesAttended() : 0,
                    latestReport.getStartDate(), latestReport.getStartDate(), latestReport.getEndDate());
        } catch(Exception e) {
            log.error("Error while updating user attributes for fitness report for user: {} due to ", latestReport.getUserId(), e);
        }
    }
}
