package com.curefit.uas.services.fitnessReportService;

import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.store.KeyValueStore;
import com.curefit.cult.models.FitnessReportDetails;
import com.curefit.cult.models.WorkoutCount;
import com.curefit.cult.services.impl.CultServiceImpl;
import com.curefit.uas.entities.FitnessReportEntity;
import com.curefit.uas.mapper.FitnessReportMapper;
import com.curefit.uas.pojo.fitnessReport.*;
import com.curefit.uas.repository.mongo.FitnessReportRepository;
import com.curefit.uas.requests.UpdateFitnessReportBody;
import com.curefit.uas.responses.FitnessReportResponse;
import com.curefit.uas.responses.FitnessReportSummaryResponse;
import com.curefit.uas.responses.FitnessReportTotalClassesAttendedResponse;
import com.curefit.uas.services.ConfigStoreWrapperService;
import com.curefit.uas.utils.FitnessReportConstants;
import com.curefit.uas.utils.ObjectUtils;
import com.curefit.uas.utils.TimeUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.LongStream;

@RequiredArgsConstructor
@Service
@Slf4j
@Validated
public class FitnessReportService {
    final FitnessReportCacheService fitnessReportCacheService;
    final FitnessReportRepository fitnessReportRepository;
    final UpdateFitnessReportHelperService updateFitnessReportHelperService;
    final FitnessReportMapper fitnessReportMapper;
    final ObjectMapper objectMapper;
    final RollbarService rollbarService;
    final ConfigStoreWrapperService configStoreWrapperService;
    KeyValueStore keyValueStore;
    
    @Autowired
    @Qualifier("defaultRedisKeyValueStore")
    public void setKeyValueStore(KeyValueStore keyValueStore) {
        this.keyValueStore = keyValueStore;
    }
    
    public FitnessReportResponse getCachedWeeklyAugmentedResponseForUserAndWeekDate(long userId, String dateInWeek) {
        String mondayOfWeek = TimeUtil.getMondayOfRelativeWeek(dateInWeek, 0);
        return fitnessReportCacheService.getCachedWeeklyAugmentedResponseForUserAndWeekMonday(userId, mondayOfWeek);
    }
    
    
    void augmentFitnessReportResponse(FitnessReportResponse fitnessReportResponse, long userId, String dateInWeek) {
        String weekStartMondayDate = TimeUtil.getMondayOfRelativeWeek(dateInWeek, 0);
        addLaunchWeekToFitnessReportResponse(fitnessReportResponse);
        addGraphDataToReport(fitnessReportResponse, userId, dateInWeek);
        fitnessReportResponse.setInfo(configStoreWrapperService.getFitnessReportInfoSection());
        fitnessReportResponse.setWeek(new FitnessReportWeek(weekStartMondayDate, TimeUtil.getSundayOfRelativeWeek(dateInWeek, 0)));
        FitnessReportEntity tempEntity = fitnessReportRepository.getPreviousActiveWeekFitnessReport(userId, weekStartMondayDate);
        if (tempEntity != null) {
            fitnessReportResponse.setPreviousActiveWeekStartDate(tempEntity.getStartDate());
        }
        tempEntity = fitnessReportRepository.getNextActiveWeekFitnessReport(userId, weekStartMondayDate);
        if (tempEntity != null) {
            fitnessReportResponse.setNextActiveWeekStartDate(tempEntity.getStartDate());
        }
    }
    
    // ToDo:: Confirm if the response type is correct and backward compatible
    public Map<String, FitnessReportResponse> getBulkResponseForUserIdsAndWeekDate(String weekDate, String commaSeparatedUserIds) {
        List<Long> userIds = Arrays.stream(commaSeparatedUserIds.split(",")).map(Long::valueOf).toList();
        String startDate = TimeUtil.getMondayOfRelativeWeek(weekDate, 0);
        
        
        Map<String, FitnessReportResponse> responseMap = new HashMap<>();
        List<FitnessReportEntity> fitnessReportEntities = fitnessReportRepository.findByUserIdsAndStartDate(userIds, startDate);
        fitnessReportEntities.forEach(fitnessReportEntity -> responseMap.put(fitnessReportEntity.getUserId().toString(), new FitnessReportResponse(fitnessReportMapper.map(fitnessReportEntity))));
        return responseMap;
    }
    
    public FitnessReportResponse getLatestReportForUserId(Long userId) {
        Pageable pageable = PageRequest.of(0, 1, Sort.by(Sort.Order.desc("startDate")));
        List<FitnessReportEntity> fitnessReportEntities = fitnessReportRepository.findByUserId(userId, pageable);
        if (CollectionUtils.isNotEmpty(fitnessReportEntities)) {
            return new FitnessReportResponse(fitnessReportMapper.map(fitnessReportEntities.get(0)));
        }
        return new FitnessReportResponse();
    }
    
    private List<FitnessReportEntity> getFitnessReportPaginated(
            Long userId,
            String startDate,
            int limit,
            int offset
    ) {
        String queryStartDateTo = TimeUtil.getDayRelativeToDate(startDate, DayOfWeek.MONDAY.getValue(), -offset);
        String queryStartDateFrom = TimeUtil.getDayRelativeToDate(startDate, DayOfWeek.MONDAY.getValue(), -(offset + limit - 1));
        return fitnessReportRepository.findAllProjectionsForUserBetweenDatesInclusive(userId, queryStartDateFrom, queryStartDateTo, FitnessReportInterval.WEEKLY);
    }
    public FitnessReportSummaryResponse getFitnessReportSummary(Long userId, String startDate, int limit, int offset) throws ExecutionException, InterruptedException {
        List<FitnessReportEntity> fitnessReportList = getFitnessReportPaginated(userId, startDate, limit, offset);
        int currentIdx = 0;
        int availableReports = fitnessReportList.size();
        List<FitnessReportResponse> responseList = new ArrayList<>();
        
        for (int i = 0; i < limit; i++) {
            String currentStartDate = TimeUtil.getDayRelativeToDate(startDate, DayOfWeek.MONDAY.getValue(), - (offset + i));
            String currentEndDate = TimeUtil.getDayRelativeToDate(startDate, DayOfWeek.SUNDAY.getValue(), - (offset + i));
            
            LocalDate currentStartLocalDate = TimeUtil.getLocalDateFromString(currentStartDate);
            
            LocalDate fitnessReportStartDate = TimeUtil.getLocalDateFromString(fitnessReportList.get(currentIdx).getStartDate());
            
            if(currentIdx >= availableReports || fitnessReportStartDate.isBefore(currentStartLocalDate)) {
                responseList.add(FitnessReportResponse.builder()
                        .fitnessReport(null)
                        .week(FitnessReportWeek.builder().startDate(currentStartDate).endDate(currentEndDate).build())
                        .build());
            } else {
                responseList.add(FitnessReportResponse.builder()
                        .fitnessReport(fitnessReportMapper.map(fitnessReportList.get(currentIdx)))
                        .week(FitnessReportWeek.builder().startDate(currentStartDate).endDate(currentEndDate).build())
                        .build());
                currentIdx++;
            }
        }
        FitnessReportSummaryResponse response = new FitnessReportSummaryResponse();
        response.setData(responseList);
        response.setNextQueryParams(FitnessReportQueryParams.builder().startDate(startDate).limit(limit).offset(Math.max(0, offset + limit)).build());
        response.setPrevQueryParams(FitnessReportQueryParams.builder().startDate(startDate).limit(limit).offset(Math.max(0, offset - limit)).build());
        response.setLaunchWeek(FitnessReportWeek
                .builder()
                .startDate(FitnessReportConstants.FITNESS_REPORT_LAUNCH_WEEK_START_DATE)
                .endDate(FitnessReportConstants.FITNESS_REPORT_LAUNCH_WEEK_END_DATE)
                .build()
        );
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                CompletableFuture.runAsync(() -> addActiveWeekInfo(response, userId, startDate, offset)),
                CompletableFuture.runAsync(() -> addClassesAttendedHistoryToReport(response, userId, startDate)),
                CompletableFuture.runAsync(() -> removedWorkoutCountsId(response))
            );
        
        allFutures.get();
        return response;
    }
    
    public FitnessReportTotalClassesAttendedResponse getTotalClassesAttendedForLatestFitnessReportForUserIds(Integer pastNumberOfMonthsToQuery, List<Long> userIds) {
        Map<String, Integer> totalClassesAttendedMap = fitnessReportRepository.getTotalClassesAttendedForLatestFitnessReportForUserIds(userIds, pastNumberOfMonthsToQuery);
        return new FitnessReportTotalClassesAttendedResponse(totalClassesAttendedMap);
    }
    
    private void addActiveWeekInfo(FitnessReportSummaryResponse response, Long userId, String startDate, int offset) {
        if (CollectionUtils.isEmpty(response.getData())) {
            response.setActiveWeek(0);
        }
        String queryStartDateTo = TimeUtil.getDayRelativeToDate(startDate, DayOfWeek.MONDAY.getValue(), -(offset));
        List<FitnessReportEntity> fitnessReports = fitnessReportRepository.findProjectedFieldsForAllForUserIdAndStartDateLessThanEqual(
                userId, queryStartDateTo, FitnessReportInterval.WEEKLY, PageRequest.of(0, 1, Sort.by(Sort.Order.desc("startDate"))));
        if (CollectionUtils.isEmpty(fitnessReports)) {
            response.setActiveWeek(0);
            response.setLastActiveWeekStartDate(null);
            response.setLastActiveWeekEndDate(null);
            return;
        }
        response.setLastActiveWeekStartDate(fitnessReports.get(0).getStartDate());
        response.setLastActiveWeekEndDate(fitnessReports.get(0).getEndDate());
        response.setActiveWeek(fitnessReports.get(0).getStartDate().equals(queryStartDateTo) ? fitnessReports.get(0).getStreak() : 0);
    }
    private void addClassesAttendedHistoryToReport(FitnessReportSummaryResponse response, Long userId, String startDate) {
        String weekMonday = TimeUtil.getMondayOfRelativeWeek(startDate, 0);
        List<FitnessReportEntity> lastSevenEntries = updateFitnessReportHelperService.getPreviousNEntitiesSortedNewToOld(userId, weekMonday, 7);
        response.setClassesAttendedHistory(getClassesAttendedHistory(lastSevenEntries, startDate));
    }
    private void removedWorkoutCountsId(FitnessReportSummaryResponse response) {
        if (response != null && response.getData() != null) {
            for (FitnessReportResponse data: response.getData()) {
                FitnessReportEntry fitnessReport = data.getFitnessReport();
                if (fitnessReport != null && fitnessReport.getWorkoutCounts() != null) {
                    for (WorkoutCount workoutCount: fitnessReport.getWorkoutCounts()) {
                        workoutCount.setId(null);
                    }
                }
            }
        }
    }
    
    public FitnessReportResponse updateFitnessReportById(String id, UpdateFitnessReportBody requestBody) {
        FitnessReportEntity entity = fitnessReportRepository.findById(id).orElse(null);
        assert entity != null;
        entity.setImageUrl(requestBody.getImageUrl());
        entity.setImageFilter(FitnessReportImageFilter.valueOf(requestBody.getImageFilter()));
        FitnessReportResponse fitnessReportResponse = new FitnessReportResponse(fitnessReportMapper.map(entity));
        augmentFitnessReportResponse(fitnessReportResponse, entity.getUserId(), entity.getStartDate());
        return fitnessReportResponse;
    }
    
    
    
    private void addLaunchWeekToFitnessReportResponse(FitnessReportResponse fitnessReportResponse) {
        fitnessReportResponse.setLaunchWeek(FitnessReportConstants.FITNESS_REPORT_LAUNCH_WEEK);
    }
    private void addGraphDataToReport(FitnessReportResponse fitnessReportResponse, Long userId, String startDate) {
        List<FitnessReportEntity> entities = updateFitnessReportHelperService.getPreviousNEntitiesSortedNewToOld(userId, startDate, 7);
        fitnessReportResponse.setWeightHistory(getWeightHistory(entities, startDate));
        fitnessReportResponse.setClassesAttendedHistory(getClassesAttendedHistory(entities, startDate));
    }
    private List<WeightHistory> getWeightHistory(List<FitnessReportEntity> entities, String startDate) {
        List<WeightHistory> weightHistory = new ArrayList<>();
        for (int i = -5; i <= 0; i++) {
            String entryStartDate = TimeUtil.getDayRelativeToDate(startDate, DayOfWeek.MONDAY.getValue(), i);
            String entryEndDate = TimeUtil.getSundayOfRelativeWeek(entryStartDate, 0);
            WeightHistory weightEntry = new WeightHistory(entryStartDate, entryEndDate, null, null);
            FitnessReportEntity entity = FitnessReportUtils.getLatestEntityBeforeOrOnGivenDate(entities, entryStartDate);
            if (entity != null) {
                weightEntry.setValue(entity.getWeight());
                weightEntry.setUpdatedEpoch(entity.getWeightUpdatedEpoch());
            }
            weightHistory.add(weightEntry);
        }
        return weightHistory;
    }
    private List<AttendedClassHistory> getClassesAttendedHistory(List<FitnessReportEntity> entities, String startDate) {
        List<AttendedClassHistory> classesAttendedHistory = new ArrayList<>();
        for (int i = -5; i <= 0; i++) {
            String entryStartDate = TimeUtil.getDayRelativeToDate(startDate, DayOfWeek.MONDAY.getValue(), i);
            String entryEndDate = TimeUtil.getSundayOfRelativeWeek(entryStartDate, 0);
//            Integer defaultValue = null;
//            if (entryStartDate.compareTo(FitnessReportConstants.FITNESS_REPORT_LAUNCH_WEEK.getStartDate()) > 0) {
//                defaultValue = 0;
//            }
            AttendedClassHistory attendedClassHistory = new AttendedClassHistory(entryStartDate, entryEndDate, 0);
            FitnessReportEntity entity = FitnessReportUtils.getMatchingEntryWithStartDate(entities, entryStartDate);
            if (entity != null) {
                attendedClassHistory.setValue(entity.getClassesAttended());
            }
            classesAttendedHistory.add(attendedClassHistory);
        }
        return classesAttendedHistory;
    }
    public List<FitnessReportEntry> getPreviousNFitnessReportsSortedNewToOld(Long userId, String weekMonday, int limit) {
        List<FitnessReportEntity> fitnessReportEntities = updateFitnessReportHelperService.getPreviousNEntitiesSortedNewToOld(userId, weekMonday, limit);
        if (CollectionUtils.isNotEmpty(fitnessReportEntities)) {
            return fitnessReportEntities.stream().map(fitnessReportMapper::map).toList();
        } else {
            return List.of();
        }
    }
    private final CultServiceImpl cultService;
    
    @Async
    public CompletableFuture<List<FitnessReportDetails>> getBatchedFitnessReportForMigration(Long startingEntityId, Long endingEntityId) {
        return cultService.getBatchedFitnessReportForMigration(startingEntityId, endingEntityId);
    }
    public int migrateFitnessReportData(Map<String, Integer> requestBody) {
        Long reqStartingEntityId = Long.valueOf(requestBody.get("startingEntityId")),
                totalEntriesToMigrate = Long.valueOf(requestBody.get("totalEntriesToMigrate")),
                batchSize = Long.valueOf(requestBody.get("batchSize"));
        Boolean ignoreExistingData= requestBody.get("ignoreExistingData").equals(1);
        return triggerFitnessReportMigration(reqStartingEntityId, totalEntriesToMigrate, batchSize, ignoreExistingData);
    }
    public int triggerFitnessReportMigration(Long reqStartingEntityId, Long totalEntriesToMigrate, Long batchSize, Boolean ignoreExistingData) {
        AtomicInteger count = new AtomicInteger(0);
        try {
            // Calculate the last entity ID to migrate
            long reqEndingEntityId = reqStartingEntityId + totalEntriesToMigrate - 1;
            long startingEntityId = reqStartingEntityId,
            endingEntityId = reqEndingEntityId;

            // Adjust endingEntityId and batchSize for the first batch
            if (batchSize < totalEntriesToMigrate) {
                endingEntityId = reqStartingEntityId + batchSize - 1;
            } else {
                batchSize = totalEntriesToMigrate;
            }

            long startTime = System.currentTimeMillis();
            // Process batches until all entries are migrated
            while (endingEntityId <= reqEndingEntityId) {
                long batchStartTime = System.currentTimeMillis();
                String uuid = UUID.randomUUID().toString();

                // Fetch a batch of FitnessReportDetails from MySQL
                List<FitnessReportDetails> mySqlDataList = getBatchedFitnessReportForMigration(startingEntityId, endingEntityId).get();
                List<Long> mySqlDataListIds = mySqlDataList.stream().map(FitnessReportDetails::getId).toList();
                List<Long> missingMySqlDataListIds = new ArrayList<>();

                // Track missing IDs if the batch is incomplete
                if (mySqlDataListIds.size() != batchSize) {
                    missingMySqlDataListIds = LongStream.rangeClosed(startingEntityId, endingEntityId)
                            .boxed()
                            .toList().stream().filter(x -> !mySqlDataListIds.contains(x)).toList();
                    String cachedVal = keyValueStore.get("missingMySqlDataListIds");
                    Set<Long> cachedMissingMySqlDataListIds = new HashSet<>();
                    if (cachedVal != null) {
                        cachedMissingMySqlDataListIds = objectMapper.readValue(cachedVal, new TypeReference<Set<Long>>() {});
                    }
                    cachedMissingMySqlDataListIds.addAll(missingMySqlDataListIds);
                    keyValueStore.set("missingMySqlDataListIds", objectMapper.writeValueAsString(cachedMissingMySqlDataListIds));
                }

                // If no data found, abort migration
                if (CollectionUtils.isEmpty(mySqlDataListIds)) return -1;

                log.info("Reconcile {} batchSize={} mySqlDataList_size={} missingMySqlDataListIds={}", uuid, batchSize, mySqlDataList.size(), missingMySqlDataListIds);

                // Fetch existing MongoDB entities for this batch
                List<FitnessReportEntity> existingFitnessReportEntities = fitnessReportRepository.findByMySqlIdBetweenInclusive(startingEntityId, endingEntityId);
                List<Long> existingMySqlIds = existingFitnessReportEntities.stream().map(FitnessReportEntity::getMySqlId).toList();

                // Map MySQL data to MongoDB entities, logging errors if mapping fails
                List<FitnessReportEntity> fitnessReportEntities = mySqlDataList.stream().map(x -> {
                    try {
                        return fitnessReportMapper.map(x);
                    } catch(Exception ex) {
                        rollbarService.error(ex, "Silent Error while mapping FitnessReportDetails to FitnessReportEntity in triggerFitnessReportMigration for id = {}" + x);
                        // Add failed ID to Redis
                        try {
                            String cachedVal = keyValueStore.get("mySqlIdsFailed");
                            final Set<Long> cachedMySqlIdsFailed = new HashSet<>();
                            if (cachedVal != null) {
                                cachedMySqlIdsFailed.addAll(objectMapper.readValue(cachedVal, new TypeReference<Set<Long>>() {}));
                            }
                            cachedMySqlIdsFailed.add(x.getId());
                            keyValueStore.set("mySqlIdsFailed", objectMapper.writeValueAsString(cachedMySqlIdsFailed));
                        } catch (Exception redisEx) {
                            rollbarService.error(redisEx, "Error while caching failed MySQL ID in Redis");
                        }
                        return null;
                    }
                }).filter(Objects::nonNull).toList();

                // Handle existing data: skip or update based on ignoreExistingData flag
                fitnessReportEntities = fitnessReportEntities.stream().map(x -> {
                    try {
                        if (existingMySqlIds.contains(x.getMySqlId())) {
                            FitnessReportEntity existingFitnessReportEntity = existingFitnessReportEntities.stream()
                                    .filter(y -> y.getMySqlId().equals(x.getMySqlId()))
                                    .findFirst().orElse(null);
                            if (ignoreExistingData) {
                                return null;
                            } else {
                                try {
                                    ObjectUtils.copyBaseMongoEntityProps(existingFitnessReportEntity, x);
                                } catch(IllegalAccessException e) {
                                    rollbarService.error(e, "Error while copyBaseMongoEntityProps in triggerFitnessReportMigration");
                                    // Add failed ID to Redis
                                    try {
                                        String cachedVal = keyValueStore.get("mySqlIdsFailed");
                                        final Set<Long> cachedMySqlIdsFailed = new HashSet<>();
                                        if (cachedVal != null) {
                                            cachedMySqlIdsFailed.addAll(objectMapper.readValue(cachedVal, new TypeReference<Set<Long>>() {}));
                                        }
                                        cachedMySqlIdsFailed.add(x.getMySqlId());
                                        keyValueStore.set("mySqlIdsFailed", objectMapper.writeValueAsString(cachedMySqlIdsFailed));
                                    } catch (Exception redisEx) {
                                        rollbarService.error(redisEx, "Error while caching failed MySQL ID in Redis");
                                    }
                                    return null;
                                }
                            }
                        }
                        return x;
                    } catch (Exception ex) {
                        rollbarService.error(ex, "Error processing entity in triggerFitnessReportMigration");
                        // Add failed ID to Redis
                        try {
                            String cachedVal = keyValueStore.get("mySqlIdsFailed");
                            final Set<Long> cachedMySqlIdsFailed = new HashSet<>();
                            if (cachedVal != null) {
                                cachedMySqlIdsFailed.addAll(objectMapper.readValue(cachedVal, new TypeReference<Set<Long>>() {}));
                            }
                            cachedMySqlIdsFailed.add(x.getMySqlId());
                            keyValueStore.set("mySqlIdsFailed", objectMapper.writeValueAsString(cachedMySqlIdsFailed));
                        } catch (Exception redisEx) {
                            rollbarService.error(redisEx, "Error while caching failed MySQL ID in Redis");
                        }
                        return null;
                    }
                }).filter(Objects::nonNull).toList();

                log.info("Reconcile {} batchSize={} fitnessReportEntities_size={}", uuid, batchSize, fitnessReportEntities.size());

                // Save the batch to MongoDB
                List<FitnessReportEntity> savedFitnessReportEntities;
                try {
                    savedFitnessReportEntities = fitnessReportRepository.saveAll(fitnessReportEntities);
                } catch (Exception ex) {
                    rollbarService.error(ex, "Error saving batch to MongoDB");
                    savedFitnessReportEntities = new ArrayList<>();
                    // Add all failed IDs to Redis
                    try {
                        String cachedVal = keyValueStore.get("mySqlIdsFailed");
                        final Set<Long> cachedMySqlIdsFailed = new HashSet<>();
                        if (cachedVal != null) {
                            cachedMySqlIdsFailed.addAll(objectMapper.readValue(cachedVal, new TypeReference<Set<Long>>() {}));
                        }
                        fitnessReportEntities.forEach(entity -> cachedMySqlIdsFailed.add(entity.getMySqlId()));
                        keyValueStore.set("mySqlIdsFailed", objectMapper.writeValueAsString(cachedMySqlIdsFailed));
                    } catch (Exception redisEx) {
                        rollbarService.error(redisEx, "Error while caching failed MySQL IDs in Redis");
                    }
                }

                // Update the count of successfully migrated entities
                count.getAndAdd(savedFitnessReportEntities.size());

                // Track failed saves if any
                if (savedFitnessReportEntities.size() != fitnessReportEntities.size() || fitnessReportEntities.size() != batchSize) {
                    List<Long> mySqlIdsFailed = new ArrayList<>(fitnessReportEntities.stream().map(FitnessReportEntity::getMySqlId).toList());
                    mySqlIdsFailed.removeAll(savedFitnessReportEntities.stream().map(FitnessReportEntity::getMySqlId).toList());
                    log.info("Reconcile error_ignore={} {} batchSize={} mySqlIdsFailed={}", mySqlDataListIds.size() == savedFitnessReportEntities.size(), uuid, batchSize, objectMapper.writeValueAsString(mySqlIdsFailed));

                    String cachedVal = keyValueStore.get("mySqlIdsFailed");
                    Set<Long> cachedMySqlIdsFailed = new HashSet<>();
                    if (cachedVal != null) {
                        cachedMySqlIdsFailed = objectMapper.readValue(cachedVal, new TypeReference<Set<Long>>() {});
                    }
                    cachedMySqlIdsFailed.addAll(mySqlIdsFailed);
                    keyValueStore.set("mySqlIdsFailed", objectMapper.writeValueAsString(cachedMySqlIdsFailed));
                }

                // Move to the next batch
                startingEntityId = endingEntityId + 1;
                endingEntityId = startingEntityId + batchSize - 1;
            }
        } catch (Exception ex) {
            String errorMsg = String.format("Exception occurred while migrateFitnessReportData for reqStartingEntityId=%s totalEntriesToMigrate=%d batchSize=%d after count=%d", reqStartingEntityId, totalEntriesToMigrate, batchSize, count.get());
            rollbarService.error(ex, errorMsg);
            log.error(errorMsg);
        }
        // Return the total number of successfully migrated entities
        return count.get();
    }
}
