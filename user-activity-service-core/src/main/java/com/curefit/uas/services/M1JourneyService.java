package com.curefit.uas.services;

import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.hamlet.models.request.HamletAllocationRequest;
import com.curefit.hamlet.service.impl.HamletService;
import com.curefit.hamlet.types.Bucket;
import com.curefit.segmentation.client.cache.SegmentationCacheClient;
import com.curefit.segmentation.client.pojo.SegmentSet;
import com.curefit.uas.pojo.M1JourneyTask;
import com.curefit.uas.pojo.entries.Task;
import com.curefit.uas.pojo.entries.UserTaskMapping;
import com.curefit.uas.responses.M1JourneyResponse;
import com.curefit.uas.types.TaskStatus;
import com.curefit.uas.types.TaskType;
import com.curefit.uas.utils.Constants;
import com.curefit.ufs.pojo.UserWodEntry;
import com.curefit.ufs.services.UfsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
@Slf4j
public class M1JourneyService {

    TaskService taskService;

    UserTaskMappingService userTaskMappingService;

    final SegmentationCacheClient segmentationClient;

    RollbarService rollbarService;

    UfsService userFitnessServiceClient;
    HamletService hamletService;

    @Autowired
    public M1JourneyService(SegmentationCacheClient segmentationClient,
                            UserTaskMappingService userTaskMappingService,
                            TaskService taskService,
                            RollbarService rollbarService,
                            UfsService userFitnessServiceClient,
    HamletService hamletService) {
        this.segmentationClient = segmentationClient;
        this.taskService = taskService;
        this.userTaskMappingService = userTaskMappingService;
        this.rollbarService = rollbarService;
        this.userFitnessServiceClient = userFitnessServiceClient;
        this.hamletService = hamletService;
    }


    public M1JourneyResponse getWidgetInfo(String userId, Boolean showAll) {

        String currentState = Constants.M1_JOURNEY_STATE;

        List<UserTaskMapping> userTasks = this.userTaskMappingService.getStatesForUser(Long.parseLong(userId), currentState);
        
        Map<Long, UserTaskMapping> userTaskMap = userTasks.stream().distinct().collect(Collectors.toMap(UserTaskMapping::getTaskId, userTaskMapping -> userTaskMapping));

        List<Task> allTasksInState = this.taskService.getTasksInState(currentState);
        
        SegmentSet<String> userSegments = segmentationClient.getUserSegments(userId);

        List<M1JourneyTask> tasksList = new ArrayList<>();

        List<M1JourneyTask> completedTasksList = new ArrayList<>();
        List<M1JourneyTask> incompleteTasksList = new ArrayList<>();


        allTasksInState.forEach(task -> {
            if (userTaskMap.containsKey(task.getId()) && userTaskMap.get(task.getId()) != null
                    && userTaskMap.get(task.getId()).getIsVisible()) {
                UserTaskMapping userTaskMapping = userTaskMap.get(task.getId());
                M1JourneyTask m1JourneyTask = new M1JourneyTask().builder()
                        .taskStatus(TaskStatus.valueOf(userTaskMapping.getStatus().name()))
                        .taskName(task.getTaskKey())
                        .taskType(TaskType.TASK)
                        .title(task.getTitle())
                        .action(task.getMetadata() != null ? task.getMetadata().getAction() : null)
                        .build();

                if (this.shouldUserSeeThisTask(task, userId, userSegments)) {
                    if(userTaskMapping.getStatus() == TaskStatus.INCOMPLETE)
                        incompleteTasksList.add(m1JourneyTask);
                    else if(userTaskMapping.getStatus() == TaskStatus.COMPLETED)
                        completedTasksList.add(m1JourneyTask);
                }
            } else if(task.getDefaultVisibility()) {
                M1JourneyTask m1JourneyTask = new M1JourneyTask().builder()
                        .taskStatus(TaskStatus.INCOMPLETE)
                        .taskName(task.getTaskKey())
                        .taskType(TaskType.TASK)
                        .title(task.getTitle())
                        .action(task.getMetadata() != null ? task.getMetadata().getAction() : null)
                        .build();

                if (this.shouldUserSeeThisTask(task, userId, userSegments)) {
                    incompleteTasksList.add(m1JourneyTask);
                }

            }
        });

        //Ensuring that only 5 tasks are shown in the widget
        if(incompleteTasksList.size() >= Constants.M1_JOURNEY_MAX_TASKS) {
            tasksList.addAll(incompleteTasksList.subList(0, Constants.M1_JOURNEY_MAX_TASKS));
        } else {
            Integer completedTasksStartIndex = Math.max(0, completedTasksList.size() - (Constants.M1_JOURNEY_MAX_TASKS - incompleteTasksList.size()));
            tasksList.addAll(completedTasksList.subList(completedTasksStartIndex, completedTasksList.size()));
            tasksList.addAll(incompleteTasksList);
        }

        tasksList.stream()
                .filter(getTask -> getTask.getTaskName().equals(Constants.M1_JOURNEY_SWP_TASK))
                .findAny()
                .ifPresent(m1JourneyTask -> {
                    updateSWPTask(Long.parseLong(userId), tasksList, tasksList.indexOf(m1JourneyTask));
                });

        return M1JourneyResponse.builder()
                .tasks(tasksList)
                .build();
    }

    private void updateSWPTask(Long userId, List<M1JourneyTask> tasksList, int index) {
        try {
            UserWodEntry userWodEntry = this.userFitnessServiceClient.fitnessPlan().getCurrentDayWod(userId.toString(), 1L).get();
            if(userWodEntry != null && userWodEntry.getWorkoutName() != null) {
                String updatedTaskName = tasksList.get(index).getTitle().replace("{{workout_name}}", userWodEntry.getWorkoutName());
                tasksList.get(index).setTitle(updatedTaskName);
            } else {
                tasksList.remove(index);
            }
        } catch (Exception e) {
            String errMsg = "Error while fetching SWP WOD for user: " + userId + " " + e.getMessage();
            log.error(errMsg);
            this.rollbarService.error(e, errMsg);
            tasksList.remove(index);
        }
    }

    private Boolean shouldUserSeeThisTask(Task task, String userId, SegmentSet<String> userSegments) {

        if(task.getVisibilityConditions() == null) {
            return true;
        }

        List<String> inCerebrumSegmentName = task.getVisibilityConditions().getInCerebrumSegmentName();
        List<String> notInCerebrumSegmentName = task.getVisibilityConditions().getNotInCerebrumSegmentName();
        List<String> cohortCerebrumSegmentName = task.getVisibilityConditions().getCohortCerebrumSegmentName();
        try {
            List<String> inHamletExperiment = task.getVisibilityConditions().getInHamletExperiment();
            if (inHamletExperiment != null && !inHamletExperiment.isEmpty()) {
                HamletAllocationRequest hamletAllocationRequest = new HamletAllocationRequest();
                hamletAllocationRequest.setUserId(String.valueOf(userId));
                hamletAllocationRequest.setExperimentIds(new HashSet<>(inHamletExperiment));
                boolean userBelongsToExperiment = inHamletExperiment.stream().anyMatch(x -> {
                    Bucket bucket = hamletService.getUserAllocations(hamletAllocationRequest).getBucketForExperiment(x);
                    return bucket != null && !bucket.isControlBucket();
                });
                if (!userBelongsToExperiment) return false;
            }
        } catch(Exception ex) {
            rollbarService.error(ex);
        }
        
        
        if (cohortCerebrumSegmentName != null) {
            if (cohortCerebrumSegmentName.isEmpty()) return false;
            
            for (String segment : cohortCerebrumSegmentName) {
                if (!userSegments.contains(segment)) {
                    return false;
                }
            }
        }

        if(inCerebrumSegmentName != null &&
                !inCerebrumSegmentName.isEmpty() && inCerebrumSegmentName.stream().noneMatch(userSegments::contains)) {
            return false;
        }

        if(notInCerebrumSegmentName != null &&
                !notInCerebrumSegmentName.isEmpty() && notInCerebrumSegmentName.stream().anyMatch(userSegments::contains)) {
            return false;
        }

        return true;
    }

    public M1JourneyTask completeUserState(Long userId, String state, Boolean isDummy) {

        String currentState = Constants.M1_JOURNEY_STATE;

        //get all tasks in current state
        Task task = this.taskService.getTaskByTaskKey(state);
        log.info("completeUserState: task: {} userId {}", task, userId);

        if(task == null) {
            return null;
        }

        M1JourneyTask m1JourneyTask = new M1JourneyTask().builder()
                .taskStatus(TaskStatus.COMPLETED)
                .taskName(task.getTaskKey())
                .taskType(TaskType.INFO)
                .title(task.getTitle())
                .action(task.getMetadata() != null ? task.getMetadata().getAction() : null)
                .build();

        if(isDummy) {
            return m1JourneyTask;
        }

        //get user task mapping for this task
        UserTaskMapping userTaskMapping = this.userTaskMappingService.getTaskForUser(userId, task.getId());
        log.info("completeUserState: userTaskMapping: {} userId {}", userTaskMapping, userId);

        if(userTaskMapping == null) {
            UserTaskMapping completedTask = new UserTaskMapping();
            completedTask.setTaskId(task.getId());
            completedTask.setState(currentState);
            completedTask.setUserId(userId);
            completedTask.setStatus(TaskStatus.COMPLETED);

            try {
                log.info("completeUserState: creating new task completedTask: {} userId {}", completedTask, userId);
                this.userTaskMappingService.create(completedTask);
                return m1JourneyTask;
            } catch (BaseException e) {
                String errMsg = "Unable to create UserTaskMapping for user " + userId + " and task " + task.getId() + "due to " + e.getMessage();
                log.error(errMsg);
                this.rollbarService.error(e, errMsg);
            }
        } else if(userTaskMapping.getStatus() != TaskStatus.COMPLETED) {
            userTaskMapping.setStatus(TaskStatus.COMPLETED);

            try {
                log.info("completeUserState: marking existing taskCompleted userTaskMapping: {} userId {}", userTaskMapping, userId);
                this.userTaskMappingService.update(userTaskMapping.getId(), userTaskMapping);
                return m1JourneyTask;
            } catch (BaseException e) {
                String errMsg = "Unable to update UserTaskMapping for user " + userId + " and userTask " + userTaskMapping.getId() + "due to " + e.getMessage();
                log.error(errMsg);
                this.rollbarService.error(e, errMsg);
            }
        } else {
            log.info("completeUserState: returning task without updates userTaskMapping: {} userId {}", userTaskMapping, userId);
            return m1JourneyTask;
        }

        return null;

    }
}
