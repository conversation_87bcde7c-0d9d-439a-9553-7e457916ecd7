package com.curefit.uas.services.athena;

import com.curefit.athena.service.client.AthenaServiceClient;
import com.curefit.athena.service.entry.AthenaQueryOutput;
import com.curefit.athena.service.entry.AthenaTaskEntry;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.uas.enums.AthenaSource;
import com.curefit.uas.services.AchievementShowcaseService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * Handler for Achievement Showcase Report Athena task notifications
 *
 * TODO: implement MongoDB storage for the following contract:
 * {
 *   user_id: number;
 *   classes_attended: number;
 *   calories: number;
 *   total_minutes_worked: number;
 *   weeks_active: number;
 *   favorate_format_1: string;
 *   favorate_format_1_classes: number;
 *   formats: number;
 *   favorate_format_2: string;
 *   favorate_format_3: string;
 *   squad_friends_count: number;
 *   trainer_name: string;
 *   trainer_classes: number;
 *   pack_utilisation_count: number;
 *   country_percentile: number;
 *   cities_count: number;
 *   favourite_city_1: string;
 *   favourite_city_2: string;
 *   favourite_city_3: string;
 *   centers_visited: number;
 * }
 */
@Slf4j
@Component
@RequiredArgsConstructor
class AchievementShowcaseReportNotificationHandler implements AthenaNotificationHandler {

    private final AthenaServiceClient athenaServiceClient;
    private final RollbarService rollbarService;
    private final ObjectMapper objectMapper;
    private final AchievementShowcaseService achievementShowcaseService;

    @Override
    public boolean handleCompletionEvent(AthenaTaskEntry athenaTaskEntry) {
        try {
            log.info("ASR: Handler triggered - Processing Achievement Showcase Report completion for taskId: {}, refId: {}, source: {}",
                    athenaTaskEntry.getId(), athenaTaskEntry.getRefId(), athenaTaskEntry.getSource());

            // Fetch query results from Athena
            log.info("ASR: Fetching query results from Athena for taskId: {}", athenaTaskEntry.getId());
            AthenaQueryOutput queryOutput = athenaServiceClient.getQueryResult(athenaTaskEntry.getId());
            log.info("ASR: Query output received - hasResults: {}", queryOutput != null && queryOutput.getQueryResult() != null);

            if (queryOutput.getQueryResult() == null || queryOutput.getQueryResult().isEmpty()) {
                log.info("ASR: No query results found for Achievement Showcase Report taskId: {}", athenaTaskEntry.getId());
                return true; // Consider empty results as successful completion
            }

            // Parse the query results
            log.info("ASR: Parsing query results for taskId: {}", athenaTaskEntry.getId());
            List<Map<String, Object>> rawResults = objectMapper.convertValue(
                queryOutput.getQueryResult(),
                new TypeReference<List<Map<String, Object>>>() {}
            );

            log.info("ASR: Successfully parsed {} user records from Achievement Showcase Report query", rawResults.size());

            // Log sample data to verify what we're receiving
            log.info("ASR: Logging sample data for verification");
            if (!rawResults.isEmpty()) {
                Map<String, Object> firstRecord = rawResults.get(0);
                log.info("ASR: Sample data - First record keys: {}", firstRecord.keySet());
                log.info("ASR: Sample data - First record: {}", firstRecord);

                // Log a few more samples if available
                if (rawResults.size() > 1) {
                    log.info("ASR: Sample data - Second record: {}", rawResults.get(1));
                }
                if (rawResults.size() > 2) {
                    log.info("ASR: Sample data - Third record: {}", rawResults.get(2));
                }
            } else {
                log.info("ASR: No sample data available - rawResults is empty");
            }

            // Dump data to MongoDB using the service
            log.info("ASR: Dumping data to MongoDB for taskId: {}", athenaTaskEntry.getId());
//            int savedCount = achievementShowcaseService.dumpDataToDatabase(rawResults, athenaTaskEntry.getId().toString());
//            log.info("ASR: Successfully saved {} records to MongoDB for taskId: {}", savedCount, athenaTaskEntry.getId());

            log.info("ASR: Successfully completed processing Achievement Showcase Report data for {} users, taskId: {}",
                    rawResults.size(), athenaTaskEntry.getId());

            return true;

        } catch (Exception e) {
            log.info("ASR: Error1");
            log.error("ASR: Error processing Achievement Showcase Report for taskId: {}", athenaTaskEntry.getId(), e);
            rollbarService.error(e, "ASR: Error processing Achievement Showcase Report for taskId: " + athenaTaskEntry.getId());
            return false;
        }
    }

    @Override
    public boolean handleFailureEvent(AthenaTaskEntry athenaTaskEntry) {
        try {
            log.info("ASR: Error2");
            log.error("ASR: Achievement Showcase Report query failed for taskId: {}, refId: {}",
                    athenaTaskEntry.getId(), athenaTaskEntry.getRefId());
            rollbarService.error("ASR: Achievement Showcase Report query failed for taskId: " + athenaTaskEntry.getId());
        } catch (Exception e) {
            log.info("ASR: Error3");
            log.error("ASR: Error handling Achievement Showcase Report failure for taskId: {}", athenaTaskEntry.getId(), e);
        }
        return true;
    }

    @Override
    public boolean canHandle(String source) {
        boolean canHandle = AthenaSource.ACHIEVEMENT_SHOWCASE_REPORT.getValue().equals(source);
        log.debug("ASR: canHandle called with source: '{}', canHandle: {}", source, canHandle);
        return canHandle;
    }
}
