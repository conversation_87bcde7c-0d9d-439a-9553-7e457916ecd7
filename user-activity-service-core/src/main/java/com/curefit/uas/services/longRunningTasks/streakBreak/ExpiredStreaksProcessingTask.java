package com.curefit.uas.services.longRunningTasks.streakBreak;

import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.uas.entities.streaks.StreakBaseEntity;
import com.curefit.uas.entities.streaks.StreakConfigEntity;
import com.curefit.uas.entities.streaks.StreakEntity;
import com.curefit.uas.repository.mongo.streaks.IStreakConfigRepository;
import com.curefit.uas.repository.mongo.streaks.IStreakRepository;
import com.curefit.uas.services.streak.StreakHelperService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Slf4j
class ExpiredStreaksProcessingTask implements Callable<String> {
    private final int pageSize;
    private final String streakResetDate;
    private final RollbarService rollbarService;
    private final IStreakRepository streakRepository;
    private final IStreakConfigRepository streakConfigRepository;
    private final StreakHelperService streakHelperService;
    
    @Override
    public String call() {
        int pageNo = 0;
        Page<StreakEntity> streaksToBreak = streakRepository.findStreaksToBreakOnDate(streakResetDate,
                PageRequest.of(pageNo, pageSize, Sort.by(Sort.Order.desc("tentativeStreakBreakDate")))
        );
        log.info("ExpiredStreaksProcessingTask Init streakResetDate={} pageNo={} pageSize={} streaksToBreak:: pages={} count={}", streakResetDate, pageNo, pageSize, streaksToBreak.getTotalPages(), streaksToBreak.getTotalElements());
        List<StreakEntity> streakEntitiesToBreak = streaksToBreak.getContent();
        try {
            while (CollectionUtils.isNotEmpty(streakEntitiesToBreak)) {
                if (CollectionUtils.isNotEmpty(streakEntitiesToBreak)) {
                    List<String> streakConfigIds = streakEntitiesToBreak.stream()
                            .map(StreakBaseEntity::getStreakConfigId)
                            .collect(Collectors.toSet()).stream().toList();
                    
                    Map<String, StreakConfigEntity> configIdStreakConfigEntityMap = streakConfigRepository.findAllByConfigIdIn(streakConfigIds)
                            .stream()
                            .collect(Collectors.toMap(
                                    StreakConfigEntity::getConfigId,
                                    streakConfig -> streakConfig
                            ));
                    
                    List<StreakEntity> streakEntities = streakEntitiesToBreak.stream()
                            .map(streakToBreak -> {
                                try {
                                    if (streakToBreak == null || streakToBreak.getStreakCount() == 0 || StringUtils.isBlank(streakToBreak.getTentativeStreakBreakDate()) || StringUtils.isNotBlank(streakToBreak.getStreakPauseId())) return null;
//                                rashiPublisherService.publishRashiEventForStreakBroken(streakToBreak);
                                    return streakHelperService.resetCurrentStreakForUser(
                                            streakToBreak.getUserId(),
                                            streakResetDate,
                                            streakToBreak,
                                            configIdStreakConfigEntityMap.get(streakToBreak.getStreakConfigId()));
                                } catch(Exception e) {
                                    rollbarService.error(e, "Error while breaking streak for userId=" + streakToBreak.getUserId());
                                    return null;
                                }
                            })
                            .filter(Objects::nonNull)
                            .toList();
                    
                    streakRepository.saveAll(streakEntities);
                    pageNo++;
                    streakEntitiesToBreak = streakRepository.findStreaksToBreakOnDate(streakResetDate,
                            PageRequest.of(pageNo, pageSize, Sort.by(Sort.Order.desc("tentativeStreakBreakDate")))
                    ).getContent();
                }
            }
            log.info("ExpiredStreaksProcessingTask Processed streakResetDate={} till pageNo={} pageSize={}", streakResetDate, pageNo, pageSize);
        } catch(Exception e) {
            rollbarService.error(e, "Error while processing expired streaks for date: " + streakResetDate + ". Processed till pageNo " + pageNo);
        }
        return null;
    }
}
