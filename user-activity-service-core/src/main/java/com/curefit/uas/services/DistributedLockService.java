package com.curefit.uas.services;


import com.curefit.commons.store.KeyValueStore;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DistributedLockService {
    @Autowired
    @Qualifier("defaultRedisKeyValueStore")
    KeyValueStore keyValueStore;
    @Value("${log-enabled.distributed-lock:false}")
    boolean isDistributedLockServiceLogsEnabled;
    
    public final static String KEY_SEPARATOR = "::";
    public final static String REDIS_METHOD_LOCK_KEY_PREFIX = "REDIS_LOCK";
    private final static int DEFAULT_MAX_RETRIES = 10;
    private final static int DEFAULT_RETRY_INTERVAL_IN_MS = 500;
    private final static int DEFAULT_LOCK_TIMEOUT_IN_S = 5;
    
    public boolean lock(String uniqueIdentifier) {
        return lock(uniqueIdentifier, DEFAULT_MAX_RETRIES, DEFAULT_RETRY_INTERVAL_IN_MS, DEFAULT_LOCK_TIMEOUT_IN_S);
    }
    
    private void log(String uniqueIdentifier, String msg) {
        if (this.isDistributedLockServiceLogsEnabled) {
            log.info("DistributedLockService::{}::{}", uniqueIdentifier, msg);
        }
    }
    
    private boolean lock(String uniqueIdentifier, int maxRetries, int retryIntervalInMS, int lockTimeout) {
        int retryCount = 0;
        String lockKey = getLockKeyForUniqueIdentifier(uniqueIdentifier);
        String lockingThreadId = String.valueOf(Thread.currentThread().threadId());
        while (retryCount < maxRetries) {
            String cacheValue = keyValueStore.get(lockKey);
            if (cacheValue == null) {
                if (keyValueStore.setIfNotExists(lockKey, lockingThreadId, lockTimeout)) {
                    log(uniqueIdentifier, "Lock was acquired for threadId = " + lockingThreadId + " after retries  = " + retryCount);
                    return true;
                }
            }
            retryCount++;
            waitBeforeRetry(uniqueIdentifier, retryIntervalInMS);
        }
        log(uniqueIdentifier, "Lock was not acquired for uniqueIdentifier = " + uniqueIdentifier);
        return false;
    }
    
    public void unlock(String uniqueIdentifier) {
        String unlockingThreadId = String.valueOf(Thread.currentThread().threadId());
        String lockKey = getLockKeyForUniqueIdentifier(uniqueIdentifier);
        String cacheLockingThreadId = keyValueStore.get(getLockKeyForUniqueIdentifier(uniqueIdentifier));
        if (StringUtils.isBlank(cacheLockingThreadId)) {
            log(uniqueIdentifier, "Lock has already been released");
        } else if (!cacheLockingThreadId.equals(unlockingThreadId)) {
            log(uniqueIdentifier, "Wrong thread is being used for unlocking lock_key_of_identifier = " + lockKey + " with cacheLockingThreadId = " + cacheLockingThreadId + " and unlockingThreadId = " + unlockingThreadId);
        } else {
            keyValueStore.del(getLockKeyForUniqueIdentifier(uniqueIdentifier));
            log(uniqueIdentifier, "Lock was released for threadId = " + unlockingThreadId);
        }
    }
    
    private String getLockKeyForUniqueIdentifier(String uniqueIdentifier) {
        return String.join(KEY_SEPARATOR, REDIS_METHOD_LOCK_KEY_PREFIX, uniqueIdentifier);
    }
    
    private void waitBeforeRetry(String uniqueIdentifier, int retryIntervalInMS) {
        try {
            Thread.sleep(retryIntervalInMS);
        } catch (InterruptedException ignored) {
            log(uniqueIdentifier, "Thread has been interrupted while retrying lock.");
            // Proceed  to next retry
        }
    }
}

