package com.curefit.uas.services;

import com.curefit.athena.service.client.AthenaServiceClient;
import com.curefit.athena.service.entry.AthenaQueryOutput;
import com.curefit.athena.service.entry.AthenaTaskEntry;
import com.curefit.athena.service.entry.AthenaTaskRequest;
import com.curefit.athena.service.enums.RequestType;
import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.segmentation.client.cache.SegmentationCacheClient;
import com.curefit.uas.entities.UserActivityStatsEntity;
import com.curefit.uas.pojo.PercentileValue;
import com.curefit.uas.pojo.athenaResults.ExerciseWeightLoggingStatsQueryResult;
import com.curefit.uas.pojo.entries.UserActivityStats;
import com.curefit.uas.pojo.entries.UserActivityStatsCohortConfig;
import com.curefit.uas.repository.jpa.IUserActivityStatsRepository;
import com.curefit.uas.requests.UserActivityStatsRequest;
import com.curefit.uas.responses.UserActivityStatsResponse;
import com.curefit.uas.types.UserActivityType;
import com.curefit.uas.utils.Constants;
import com.curefit.uas.enums.AthenaSource;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.*;

import static com.curefit.uas.utils.Constants.DEFAULT_SEGMENT_FOR_ACTIVITY_STATS;

@Slf4j
@Service
public class UserActivityStatsService extends BaseMySQLService<UserActivityStatsEntity, UserActivityStats> {

    private final IUserActivityStatsRepository repository;

    @Autowired
    UserActivityStatsCohortConfigService configService;

    @Autowired
    AthenaServiceClient athenaServiceClient;

    @Autowired
    SegmentationCacheClient segmentationClient;
    
    private final static Set<String> ELIGIBLE_SEGMENTS_FOR_USER_ACTIVITY_STATS = Set.of(
        "Undisclosed", "male", "female", "GX Logging Super Users"
    );
    
    
    public UserActivityStatsService(IUserActivityStatsRepository repository) {
        super(repository);
        this.repository = repository;
    }

    public void recomputeAllStats() {
        log.info("inside recomputeAllStats function");
        List<UserActivityStatsCohortConfig> activeConfigs = configService.getAllActiveConfigs();
        log.info("recomputeAllStats got active configs " + activeConfigs);
        for (UserActivityStatsCohortConfig config : activeConfigs) {
            switch (config.getActivityType()) {
                case EXERCISE_WEIGHT_LOGGING:
                case EXERCISE_WEIGHT_LOGGING_GYM:
                    recomputeExerciseWeightLoggingStats(config);
                    break;
                default:
                    log.error("Unsupported UserActivityType " + config.getActivityType() + " for config " + config.getId());
            }
        }
    }

    private void recomputeExerciseWeightLoggingStats(UserActivityStatsCohortConfig config) {
        log.info("inside recomputeExerciseWeightLoggingStats function");
        List<String> exerciseIds = config.getAllowedActivityIds();
        log.info("recomputeExerciseWeightLoggingStats got allowed activity ids");

        if (CollectionUtils.isEmpty(exerciseIds)) {
            exerciseIds = Constants.WEIGHT_LOGGING_EXERCISE_IDS;
        }

        for (String exerciseId : exerciseIds) {
            String query = getExerciseStatsQuery(config.getActivityType(), config.getSegmentId(), exerciseId);
            log.info("recomputeExerciseWeightLoggingStats query: "+query);
            createAthenaTask(query, String.format("%s::%s", config.getId(), exerciseId));
        }
    }

    private void createAthenaTask(String query, String referenceId) {
        log.info("inside createAthenaTask function");
        AthenaTaskRequest request = new AthenaTaskRequest();
        request.setQuery(query);
        request.setSource(AthenaSource.USER_ACTIVITY_STATS.getValue());
        request.setRequestType(RequestType.RUN_QUERY);
        request.setRefId(referenceId);
        AthenaTaskEntry taskEntry = athenaServiceClient.createTask(request);
        log.info("createAthenaTask query: "+ query);
        log.info(String.format("Athena task created - taskId:%s, referenceId: %s", taskEntry.getId(), referenceId));
    }

    private String getExerciseStatsQuery(UserActivityType userActivityType, String segmentId, String exerciseId) {
        return switch (userActivityType) {
            case EXERCISE_WEIGHT_LOGGING -> AthenaQueryHelper.getExerciseWeightLoggingStatsQuery(segmentId, exerciseId);
            case EXERCISE_WEIGHT_LOGGING_GYM ->
                    AthenaQueryHelper.getExerciseWeightLoggingGymStatsQuery(segmentId, exerciseId);
            default -> {
                log.error("Unsupported UserActivityType " + userActivityType);
                yield null;
            }
        };
    }

    @Transactional
    public void handleAthenaTaskCompleteNotification(Long taskId, String referenceId) throws BaseException {
        log.info("Inside handleAthenaTaskCompleteNotification function");
        String[] refIdSplit = referenceId.split("::");
        Long configId = Long.valueOf(refIdSplit[0]);
        String exerciseId = refIdSplit[1];
        AthenaQueryOutput queryOutput = athenaServiceClient.getQueryResult(taskId);
        log.info("handleAthenaTaskCompleteNotification queryOutput: "+queryOutput);

        if (queryOutput.getQueryResult() != null && !queryOutput.getQueryResult().isEmpty()) {
            log.info("handleAthenaTaskCompleteNotification queryOutputResult: "+queryOutput.getQueryResult());

            List<ExerciseWeightLoggingStatsQueryResult> parsedQueryResults = objectMapper.convertValue(queryOutput.getQueryResult(), new TypeReference<List<ExerciseWeightLoggingStatsQueryResult>>() {
            });

            for(ExerciseWeightLoggingStatsQueryResult e : parsedQueryResults) log.info("handleAthenaTaskCompleteNotification parsedQueryResults - "+e);

            HashMap<String, List<PercentileValue>> cohortIdIdAndMovementIdToPercentilesMap = new HashMap<>();
            for(ExerciseWeightLoggingStatsQueryResult e : parsedQueryResults){
                String cohortId = e.getSegmentId();
                String movementId = e.getMovementId();
                Double percentile = e.getPercentile();
                Double exerciseScore = e.getExerciseScore();

                log.info("handleAthenaTaskCompleteNotification exerciseScore: " + exerciseScore);

                String cohortIdAndActivityIdCombined = cohortId+"!@!"+movementId;
                PercentileValue percentileValue = new PercentileValue(percentile, exerciseScore);

                log.info("handleAthenaTaskCompleteNotification percentileValue: " + percentileValue);

                List<PercentileValue> percentileValueList;
                if(cohortIdIdAndMovementIdToPercentilesMap.containsKey(cohortIdAndActivityIdCombined)){
                    percentileValueList = cohortIdIdAndMovementIdToPercentilesMap.get(cohortIdAndActivityIdCombined);
                }
                else{
                    percentileValueList = new ArrayList<>();
                }
                percentileValueList.add(percentileValue);
                cohortIdIdAndMovementIdToPercentilesMap.put(cohortIdAndActivityIdCombined, percentileValueList);
            }

            log.info("handleAthenaTaskCompleteNotification hashmap populated");

            for(String cohortIdAndMovementIdCombined : cohortIdIdAndMovementIdToPercentilesMap.keySet()){

                log.info("handleAthenaTaskCompleteNotification cohortIdAndMovementIdCombined: "+cohortIdAndMovementIdCombined);

                for(PercentileValue p : cohortIdIdAndMovementIdToPercentilesMap.get(cohortIdAndMovementIdCombined)){
                    log.info("handleAthenaTaskCompleteNotification percentileValueListValue: "+p+" ");
                }

                cohortIdIdAndMovementIdToPercentilesMap.get(cohortIdAndMovementIdCombined).sort(Comparator.comparing(PercentileValue::getPercentile));

                UserActivityStats userActivityStats = new UserActivityStats(
                        configId, exerciseId, null, null, null, null, null,
                        cohortIdIdAndMovementIdToPercentilesMap.get(cohortIdAndMovementIdCombined)
                );

                log.info("handleAthenaTaskCompleteNotification userActivityStats: "+ userActivityStats);

                Optional<UserActivityStatsEntity> existingEntityOpt = repository.findByConfigIdAndActivityId(configId, exerciseId);
                Optional<UserActivityStatsEntity> testEntity = repository.findByConfigIdAndActivityId(configId, "5ae97b4c8f97dc5a555245ac");

                try {
                    if (existingEntityOpt.isPresent()) {
                        log.info("handleAthenaTaskCompleteNotification testEntity: " + testEntity.get());
                        UserActivityStats existingEntry = convertToEntry(existingEntityOpt.get());
                        log.info("handleAthenaTaskCompleteNotification existingEntry1: " + existingEntry);
                        // Update the deleted_at column
                        log.info("handleAthenaTaskCompleteNotification for id = {} existingEntry2: {}", existingEntry.getId(), existingEntry);
                        UserActivityStats patchUpdateEntry = new UserActivityStats();
                        patchUpdateEntry.setDeletedAt(new Date());
                        UserActivityStats updatedEntry = this.patchUpdate(existingEntry.getId(), patchUpdateEntry);
                        log.info("handleAthenaTaskCompleteNotification syccess: ");
                        log.info("handleAthenaTaskCompleteNotification updatedEntry: " + updatedEntry);
                    }
                } catch (Exception e) {
                    log.error("An error occurred while handling Athena task complete notification: "+e.getMessage());
                }


                this.create(userActivityStats);

                for(PercentileValue percentileValue : cohortIdIdAndMovementIdToPercentilesMap.get(cohortIdAndMovementIdCombined)){
                    log.info("handleAthenaTaskCompleteNotification percentileValueObjectPercentile: "+percentileValue.getPercentile()+" percentileObjectBodyWeight: "+percentileValue.getValue());
                }
            }

            log.info("Athena task result consumed. Updated stats for configId: "+ configId + ", excerciseId: "+ exerciseId);
        } else {
            log.error("Athena task result is null/empty. taskId: " + taskId);
        }
    }

    public UserActivityStatsResponse fetchActivityStatsForUser(UserActivityStatsRequest request) {
        Set<String> userSegmentNames = segmentationClient.getUserSegments(request.getUserId()).getRelevantEntries(ELIGIBLE_SEGMENTS_FOR_USER_ACTIVITY_STATS);
        List<UserActivityStatsCohortConfig> cohortConfigs = configService.getCohortConfigsForUser(userSegmentNames, List.of(UserActivityType.EXERCISE_WEIGHT_LOGGING, UserActivityType.EXERCISE_WEIGHT_LOGGING_GYM));
        if(cohortConfigs.isEmpty()){
            // failsafe
            cohortConfigs = configService.getCohortConfigsForUser(DEFAULT_SEGMENT_FOR_ACTIVITY_STATS, List.of(UserActivityType.EXERCISE_WEIGHT_LOGGING, UserActivityType.EXERCISE_WEIGHT_LOGGING_GYM));
            if(cohortConfigs.isEmpty()){
                log.error("No active cohort config found for user: "+request.getUserId());
                return null;
            }
        }

        UserActivityStatsCohortConfig cohortConfig = null;
        for(UserActivityStatsCohortConfig config : cohortConfigs){
            log.info("fetchActivityStatsForUser configs found: " + config);
            if(config.getAllowedActivityIds().contains(request.getActivityId())) {
                cohortConfig = config;
                break;
            }
        }

        log.info("fetchActivityStatsForUser cohortConfig: "+cohortConfig);
        if(cohortConfig == null) {
//            log.error("No active cohort config matched for user and exerciseId: "+request.getUserId() + ", " + request.getActivityId());
            return null;
        }

        Optional<UserActivityStatsEntity> userActivityStatsOpt = repository.findTopByConfigIdAndActivityIdOrderByIdDesc(cohortConfig.getId(), request.getActivityId());
        if(userActivityStatsOpt.isEmpty()) return null;
        UserActivityStats userActivityStats = convertToEntry(userActivityStatsOpt.get());

//        log.info("fetchActivityStatsForUser userActivityStats: "+userActivityStats);
        // Find the closest matching Percentile
        Double matchingPercentile = null;
        if (request.getUserActivityValue() != null) {
            matchingPercentile = userActivityStats.getPercentiles().stream().reduce((item1, item2) -> {
                Double diff1 = Math.abs(item1.getValue() - request.getUserActivityValue());
                Double diff2 = Math.abs(item2.getValue() - request.getUserActivityValue());
                if (diff1.equals(diff2)) {
                    return item1.getPercentile() > item2.getPercentile() ? item1 : item2;
                }
                return diff1 < diff2 ? item1 : item2;
            }).map(PercentileValue::getPercentile).orElse(null);
        }

        log.info("fetchActivityStatsForUser matchingPercentile: "+matchingPercentile);

        return new UserActivityStatsResponse(userActivityStats, matchingPercentile, cohortConfig.getSegmentName());
    }
}