package com.curefit.uas.services;

public class AthenaQueryHelper {



    AthenaQueryHelper() {

    }

    public static String getExerciseWeightLoggingStatsQuery(String segmentId, String movementId) {
        return String.format(
                """
                    WITH Activity AS (
                                SELECT
                                    fb.user_id,
                                    COUNT(*) AS TotalActivity,
                                    COUNT(DISTINCT CASE
                                        WHEN LOWER(fb.workout_name) LIKE ('%%hrx%%') THEN fb.booking_key
                                    END) AS HRXWorkouts,
                                    COUNT(DISTINCT CASE
                                        WHEN LOWER(fb.workout_name) LIKE ('%%hrx%%') OR LOWER(fb.workout_name) LIKE ('%%strength%%')
                                             OR LOWER(fb.workout_name) LIKE ('%%s&c%%') OR LOWER(fb.workout_name) LIKE ('%%hybrid%%')
                                             OR LOWER(fb.workout_name) LIKE ('%%gym%%') THEN fb.booking_key
                                    END) AS StrengthWorkouts,
                                    COUNT(DISTINCT CASE
                                        WHEN LOWER(fb.workout_name) LIKE ('%%hrx%%') OR LOWER(fb.workout_name) LIKE ('%%strength%%')
                                             OR LOWER(fb.workout_name) LIKE ('%%s&c%%') OR LOWER(fb.workout_name) LIKE ('%%hybrid%%')
                                             OR LOWER(fb.workout_name) LIKE ('%%gym%%') OR LOWER(category) LIKE ('%%gym%%') THEN fb.booking_key
                                    END) AS GymorStrengthWorkouts
                                FROM dwh_fitness.fitness_bookings fb
                                WHERE fb.class_date BETWEEN DATE_ADD('day', -365, NOW()) AND DATE_ADD('day', -60, NOW())
                                AND fb.attendance_time IS NOT NULL
                                GROUP BY 1
                                ORDER BY 2 DESC
                            ),
                            Weights AS (
                                SELECT
                                    um.user_id,
                                    MAX(um.metric_date) AS max_metric_date,
                                    MAX_BY(um.value, um.metric_date) AS bodyweight
                                FROM "pk_curefitprod_metric_service"."user_metric" um
                                WHERE um.metric_id = 3
                                AND um.created_at_Date >= DATE_ADD('year', -1, NOW())
                                GROUP BY 1
                            ),
                            MovementData AS (
                                SELECT
                                    fb.user_id,
                                    mv.uniquename AS Movement,
                                    mv._id AS MovementId,
                                    MAX(aew.weight[1]) AS MaxWe,
                                    max_by(reps,aew.weight[1]) reps
                                FROM "pk_cultprod_cultapp"."cultclass" cc
                                LEFT JOIN "dwh_fitness"."fitness_bookings" fb ON fb.class_id = cc.id
                                    AND fb.class_date = DATE_TRUNC('day', cc.startdatetimeutc)
                                    AND fb.attendance_time IS NOT NULL
                                    AND fb.workout_name LIKE ('%%HRX%%')
                                LEFT JOIN "pk_prod_curefit_prod"."simplewods" sw ON cc."wodid" = sw."_id"
                                LEFT JOIN "pk_prod_curefit_prod"."simplewods_parts" swp ON sw._id = swp."root_ref_id"
                                LEFT JOIN "pk_prod_curefit_prod"."simplewods_parts_movements" swpm ON sw._id = swpm.root_ref_id
                                    AND swpm.parent_array_index = swp.array_index
                                LEFT JOIN "pk_prod_curefit_prod"."movements" mv ON swpm.movementid = mv._id
                                LEFT JOIN "pk_prod_curefit_prod"."activitystoreattributes" aa ON aa.sessionid = CAST(fb.class_id AS VARCHAR)
                                    AND aa.userid = fb.user_id
                                    AND aa."createddate_date" >= fb.class_date
                                    AND aa."namespace" = 'GX'
                                    AND aa.activitytype = 'WEIGHT_LOGGING'
                                LEFT JOIN "pk_prod_curefit_prod"."activitystoreattributes_exercises" ae ON aa."_id" = ae."root_ref_id"
                                    AND ae.root_createddate_date >= fb.class_date
                                LEFT JOIN "pk_prod_curefit_prod"."activitystoreattributes_exercises_exerciseexecution_workoutsets" aew ON aew.root_ref_id = aa."_id"
                                    AND aew.parent_array_index = ae.array_index
                                    AND aew.root_createddate_date >= fb.class_date
                                    AND aew.weight IS NOT NULL
                                    AND CARDINALITY(aew.weight) > 0
                                WHERE cc.createdat_date >= DATE('2024-01-01')
                                AND swpm.loggingenabled = TRUE
                                AND cc.startdatetimeutc >= DATE_ADD('day', -60, NOW())
                                GROUP BY 1, 2, 3
                            ),
                            Percentiles AS (
                                SELECT
                                    x.user_id,
                                    x.MovementId AS movementId,
                                    x.movement,
                                    CASE
                                        WHEN hu.gender IN ('male', 'female') THEN hu.gender
                                        ELSE 'undisclosed'
                                    END AS cohort,
                                    100*x.MaxWe*(1+ ((x.reps*1.0)/30.0)) / CAST(w.bodyweight AS DOUBLE) AS ExerciseScore
                                   
                                   
                                FROM MovementData x
                                LEFT JOIN Activity ON x.user_id = Activity.user_id
                                LEFT JOIN "pk_cfuserservice_cultapp"."user" hu ON CAST(hu.id AS VARCHAR) = x.user_id
                                LEFT JOIN Weights w ON x.user_id = w.user_id
                                WHERE w.bodyweight IS NOT NULL
                                AND CAST(w.bodyweight AS DOUBLE) BETWEEN 40 and 150
                                and x.reps between 0 and 20
                                and x.user_id NOT IN ('97442499',
                            '91088955',
                            '94316727',
                            '91619512',
                            '85451715',
                            '92150925',
                            '12714925',
                            '93857422',
                            '10514425')
                                AND CAST(w.bodyweight AS DOUBLE) > 0
                                and MaxWe <= 200
                            ),
                            PercentileValues AS (
                                SELECT
                                    cohort,
                                    movement,
                                    movementId,
                                    percentile,
                                    count(*) as Loggers,
                                    APPROX_PERCENTILE(ExerciseScore, percentile / 100.00) AS ExerciseScore
                                FROM Percentiles
                                CROSS JOIN (VALUES (1),
                            (2),
                            (3),
                            (4),
                            (5),
                            (6),
                            (7),
                            (8),
                            (9),
                            (10),
                            (11),
                            (12),
                            (13),
                            (14),
                            (15),
                            (16),
                            (17),
                            (18),
                            (19),
                            (20),
                            (21),
                            (22),
                            (23),
                            (24),
                            (25),
                            (26),
                            (27),
                            (28),
                            (29),
                            (30),
                            (31),
                            (32),
                            (33),
                            (34),
                            (35),
                            (36),
                            (37),
                            (38),
                            (39),
                            (40),
                            (41),
                            (42),
                            (43),
                            (44),
                            (45),
                            (46),
                            (47),
                            (48),
                            (49),
                            (50),
                            (51),
                            (52),
                            (53),
                            (54),
                            (55),
                            (56),
                            (57),
                            (58),
                            (59),
                            (60),
                            (61),
                            (62),
                            (63),
                            (64),
                            (65),
                            (66),
                            (67),
                            (68),
                            (69),
                            (70),
                            (71),
                            (72),
                            (73),
                            (74),
                            (75),
                            (76),
                            (77),
                            (78),
                            (79),
                            (80),
                            (81),
                            (82),
                            (83),
                            (84),
                            (85),
                            (86),
                            (87),
                            (88),
                            (89),
                            (90),
                            (91),
                            (92),
                            (93),
                            (94),
                            (95),
                            (96),
                            (97),
                            (98),
                            (99),
                            (100)) AS percentiles(percentile)
                                GROUP BY cohort, movementId, percentile,movement
                            ),
                            SegmentedValues AS (
                            SELECT
                                cohort,
                                CASE
                                    WHEN cohort = 'male' THEN '21523'
                                    WHEN cohort = 'female' THEN '21525'
                                    WHEN cohort = 'undisclosed' THEN '21526'
                                END AS segmentId,
                                movementId,
                                movement,
                                percentile,
                                ExerciseScore
                            FROM PercentileValues
                            where Loggers >100
                            )
                            SELECT
                                cohort,
                                segmentId,
                                movementId,
                                movement,
                                percentile,
                                ExerciseScore
                            FROM SegmentedValues
                            WHERE segmentId = '%s'
                            AND movementId = '%s'
                            ORDER BY cohort, movementId, percentile
                """, segmentId, movementId
        );
    }



    public static String getExerciseWeightLoggingGymStatsQuery(String segmentId, String movementId) {
        return String.format(
                """ 
                WITH Activity AS (
                    SELECT
                        fb.user_id,
                        COUNT(*) AS TotalActivity,
                        COUNT(DISTINCT CASE 
                            WHEN LOWER(fb.workout_name) LIKE ('%%hrx%%') THEN fb.booking_key 
                        END) AS HRXWorkouts,
                        COUNT(DISTINCT CASE 
                            WHEN LOWER(fb.workout_name) LIKE ('%%hrx%%') OR LOWER(fb.workout_name) LIKE ('%%strength%%') 
                                 OR LOWER(fb.workout_name) LIKE ('%%s&c%%') OR LOWER(fb.workout_name) LIKE ('%%hybrid%%') 
                                 OR LOWER(fb.workout_name) LIKE ('%%gym%%') THEN fb.booking_key 
                        END) AS StrengthWorkouts,
                        COUNT(DISTINCT CASE 
                            WHEN LOWER(fb.workout_name) LIKE ('%%hrx%%') OR LOWER(fb.workout_name) LIKE ('%%strength%%') 
                                 OR LOWER(fb.workout_name) LIKE ('%%s&c%%') OR LOWER(fb.workout_name) LIKE ('%%hybrid%%') 
                                 OR LOWER(fb.workout_name) LIKE ('%%gym%%') OR LOWER(category) LIKE ('%%gym%%') THEN fb.booking_key 
                        END) AS GymorStrengthWorkouts
                    FROM dwh_fitness.fitness_bookings fb
                    WHERE fb.class_date BETWEEN DATE_ADD('day', -365, NOW()) AND DATE_ADD('day', -60, NOW())
                    AND fb.attendance_time IS NOT NULL
                    GROUP BY 1
                    ORDER BY 2 DESC
                ),
                Weights AS (
                    SELECT
                        um.user_id,
                        MAX(um.metric_date) AS max_metric_date,
                        MAX_BY(um.value, um.metric_date) AS bodyweight
                    FROM "pk_curefitprod_metric_service"."user_metric" um
                    WHERE um.metric_id = 3
                    AND um.created_at_Date >= DATE_ADD('year', -1, NOW())
                    GROUP BY 1
                ),
                MovementData AS (SELECT 
                        user_id,
                        uniquename Movement,
                        _id AS MovementId,
                        MAX(greatest(coalesce(first_weight,-1),coalesce(second_weight,-1),coalesce(third_weight,-1),coalesce(fourth_weight,-1),coalesce(fifth_weight,-1))) MaxWe,
                        MAX_BY(greatest(coalesce(first_count,-1),coalesce(second_count,-1),coalesce(third_count,-1),coalesce(fourth_count,-1),coalesce(fifth_count,-1)),greatest(coalesce(first_weight,-1),coalesce(second_weight,-1),coalesce(third_weight,-1),coalesce(fourth_weight,-1),coalesce(fifth_weight,-1))) reps
                
                FROM(
                SELECT user_id,
                       hercules_exercise_id,
                       movements.exerciseid,
                       movements._id,
                       uniquename,
                       title,
                       cast(json_extract(execution_logs, '$[0].weight') as double) AS first_weight,
                       cast(json_extract(execution_logs, '$[1].weight') as double)   AS second_weight,
                       cast(json_extract(execution_logs, '$[2].weight') as double)  AS third_weight,
                       cast(json_extract(execution_logs, '$[3].weight') as double)  AS fourth_weight,
                       cast(json_extract(execution_logs, '$[4].weight') as double)  AS fifth_weight,
                       cast(json_extract(execution_logs, '$[0].count')  as double) AS first_count,
                       cast(json_extract(execution_logs, '$[1].count')  as double) AS second_count,
                       cast(json_extract(execution_logs, '$[2].count')  as double) AS third_count,
                       cast(json_extract(execution_logs, '$[3].count')  as double) AS fourth_count,
                       cast(json_extract(execution_logs, '$[4].count')  as double) AS fifth_count
                FROM  pk_cultprod_user_fitness.exercise_log
                LEFT JOIN pk_prod_curefit_prod.movements ON hercules_exercise_id = movements.exerciseid
                where  exercise_log.created_on_date >= DATE('2024-01-01')
                and execution_type = 'COUNT'
                and movements._id IN 
                ('59c8efef8acffc465f38dd3c',
                '5abb447e17eee93212e30098',
                '5ae97b4c8f97dc5a555245ac',
                '5b0d21f842e13c066120afab',
                '60af65d3804e1900082e76d7',
                '60af65d4804e1900082e7743',
                '60af65d4804e1900082e775c',
                '60af65d4804e1900082e77fd',
                '60af65d5804e1900082e7852',
                '60af65d5804e1900082e7864',
                '60af65d5804e1900082e7897',
                '60af65d5804e1900082e78c0',
                '60af65d5804e1900082e7912',
                '60af65d5804e1900082e791f',
                '60af65d6804e1900082e79ed',
                '60af65d6804e1900082e7a61',
                '60af65d7804e1900082e7aaf',
                '60af65d7804e1900082e7af4',
                '60af65d7804e1900082e7b01',
                '60af65d7804e1900082e7b64',
                '60af65d8804e1900082e7ba2',
                '60af65d8804e1900082e7bd8',
                '60af65d8804e1900082e7be0',
                '60af65d8804e1900082e7bf1',
                '60af65d8804e1900082e7c36',
                '60af65d8804e1900082e7cae',
                '60af65d8804e1900082e7cb7',
                '60af65d9804e1900082e7cec',
                '60af65d9804e1900082e7cf3',
                '60af65d9804e1900082e7d4f',
                '60af65d9804e1900082e7d76',
                '60af65d9804e1900082e7da3',
                '60af65d9804e1900082e7daf',
                '60af65d9804e1900082e7ddc',
                '60af65d9804e1900082e7df9',
                '60af65da804e1900082e7e0d',
                '60af667d804e1900082e7eaa',
                '60af667d804e1900082e7ebb',
                '60af667d804e1900082e7f13',
                '60af667e804e1900082e7f5b',
                '60af667f804e1900082e7f79',
                '60af667f804e1900082e7fce',
                '60af6680804e1900082e8019',
                '60af6680804e1900082e8023',
                '60af74d5d673530008056055',
                '60af74ded6735300080567ff',
                '60af74ded67353000805687e',
                '60af74dfd6735300080568bc',
                '61eff3a89ac229557ffda1b7',
                '61eff3a89ac229557ffda653',
                '61eff3b89ac229557ffe6877',
                '61f03e0d9ac229557f191a10',
                '61f03e0d9ac229557f191cb5',
                '61f03e0e9ac229557f192a05',
                '61f03e0f9ac229557f192f74',
                '61f03e129ac229557f194d29',
                '61f03e1c9ac229557f19aa90',
                '61f03ee69ac229557f203fda',
                '61f03fcd9ac229557f27d75b',
                '62060f6708da6c399812ed11',
                '62060f6c08da6c39981325a8',
                '62060f6d08da6c3998132e72',
                '62060f6f08da6c39981346bd',
                '62060f7208da6c39981366b5',
                '62060f7408da6c3998137ca4',
                '62060f8608da6c3998142def',
                '62060f8908da6c39981454d0',
                '62060f8a08da6c3998145dbd',
                '6228c27a49abd72c5d1f7f68',
                '627a2c6db14d894a975ce511',
                '62ac278d5c36cb0009c2f4a4')
                --and 
                --and user_id = '2490983'
                ORDER BY created_on_date DESC
                )
                GROUP BY 1,2,3
                HAVING MAX(greatest(coalesce(first_weight,-1),coalesce(second_weight,-1),coalesce(third_weight,-1),coalesce(fourth_weight,-1),coalesce(fifth_weight,-1))) > 0),
                Percentiles AS (
                    SELECT
                        x.user_id,
                        x.MovementId AS movementId,
                        x.movement,
                        CASE
                            WHEN hu.gender IN ('male', 'female') THEN hu.gender
                            ELSE 'undisclosed'
                        --END || '_' || CASE
                            --WHEN Activity.GymorStrengthWorkouts IS NULL THEN 'NewUser'
                            --WHEN Activity.GymorStrengthWorkouts <= 20 THEN 'Beginner'
                            --WHEN Activity.GymorStrengthWorkouts BETWEEN 21 AND 50 THEN 'Intermediate'
                            --WHEN Activity.GymorStrengthWorkouts BETWEEN 51 AND 100 THEN 'Advanced'
                            --WHEN Activity.GymorStrengthWorkouts > 100 THEN 'SuperAdvanced'
                        END AS cohort,
                        100*x.MaxWe*(1+ ((x.reps*1.0)/30.0)) / CAST(w.bodyweight AS DOUBLE) AS ExerciseScore
                        
                        
                    FROM MovementData x
                    LEFT JOIN Activity ON x.user_id = Activity.user_id
                    LEFT JOIN "pk_cfuserservice_cultapp"."user" hu ON CAST(hu.id AS VARCHAR) = x.user_id
                    LEFT JOIN Weights w ON x.user_id = w.user_id
                    WHERE w.bodyweight IS NOT NULL
                    AND CAST(w.bodyweight AS DOUBLE) BETWEEN 40 and 150
                    and x.reps between 0 and 20
                    and x.user_id NOT IN ('97442499',
                '91088955',
                '94316727',
                '91619512',
                '85451715',
                '92150925',
                '12714925',
                '93857422',
                '10514425')
                    AND CAST(w.bodyweight AS DOUBLE) > 0
                    and MaxWe <= 200
                ),
                PercentileValues AS (
                    SELECT
                        cohort,
                        movement,
                        movementId,
                        percentile,
                        count(*) as Loggers,
                        APPROX_PERCENTILE(ExerciseScore, percentile / 100.00) AS ExerciseScore
                    FROM Percentiles
                    CROSS JOIN (VALUES (1),
                (2),
                (3),
                (4),
                (5),
                (6),
                (7),
                (8),
                (9),
                (10),
                (11),
                (12),
                (13),
                (14),
                (15),
                (16),
                (17),
                (18),
                (19),
                (20),
                (21),
                (22),
                (23),
                (24),
                (25),
                (26),
                (27),
                (28),
                (29),
                (30),
                (31),
                (32),
                (33),
                (34),
                (35),
                (36),
                (37),
                (38),
                (39),
                (40),
                (41),
                (42),
                (43),
                (44),
                (45),
                (46),
                (47),
                (48),
                (49),
                (50),
                (51),
                (52),
                (53),
                (54),
                (55),
                (56),
                (57),
                (58),
                (59),
                (60),
                (61),
                (62),
                (63),
                (64),
                (65),
                (66),
                (67),
                (68),
                (69),
                (70),
                (71),
                (72),
                (73),
                (74),
                (75),
                (76),
                (77),
                (78),
                (79),
                (80),
                (81),
                (82),
                (83),
                (84),
                (85),
                (86),
                (87),
                (88),
                (89),
                (90),
                (91),
                (92),
                (93),
                (94),
                (95),
                (96),
                (97),
                (98),
                (99),
                (100)) AS percentiles(percentile)
                    GROUP BY cohort, movementId, percentile,movement
                )
                
                SELECT *
                FROM(
                
                SELECT
                    cohort,
                    CASE
                    WHEN cohort = 'male' THEN '21523'
                        WHEN cohort = 'female' THEN '21525'
                        WHEN cohort = 'undisclosed' THEN '21526'
                        
                       /*WHEN cohort = 'female_NewUser' THEN '21227'
                        WHEN cohort = 'female_Beginner' THEN '21230'
                        WHEN cohort = 'female_Intermediate' THEN '21231'
                        WHEN cohort = 'female_Advanced' THEN '21232'
                        WHEN cohort = 'female_SuperAdvanced' THEN '21233'
                        WHEN cohort = 'male_NewUser' THEN '21234'
                        WHEN cohort = 'male_Beginner' THEN '21235'
                        WHEN cohort = 'male_Intermediate' THEN '21236'
                        WHEN cohort = 'male_Advanced' THEN '21237'
                        WHEN cohort = 'male_SuperAdvanced' THEN '21239'
                        WHEN cohort = 'undisclosed_NewUser' THEN '21240'
                        WHEN cohort = 'undisclosed_Beginner' THEN '21241'
                        WHEN cohort = 'undisclosed_Intermediate' THEN '21242'
                        WHEN cohort = 'undisclosed_Advanced' THEN '21244'
                        WHEN cohort = 'undisclosed_SuperAdvanced' THEN '21245'
                        */
                    END AS segmentId,
                    movementId,
                    movement,
                    percentile,
                    ExerciseScore
                    
                
                FROM PercentileValues
                
                --where Loggers >100
                ORDER BY cohort, movementId, percentile
                )
                WHERE segmentId = '%s'
                AND movementId = '%s'
                ORDER BY cohort, movementId, percentile
                """, segmentId, movementId
        );
    }

}