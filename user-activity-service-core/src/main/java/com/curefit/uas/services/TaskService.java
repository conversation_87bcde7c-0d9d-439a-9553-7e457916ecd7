package com.curefit.uas.services;

import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.uas.entities.TaskEntity;
import com.curefit.uas.pojo.entries.Task;
import com.curefit.uas.repository.jpa.ITaskRepository;
import com.curefit.uas.services.intefaces.ITaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TaskService extends BaseMySQLService<TaskEntity, Task> implements ITaskService {

    ITaskRepository taskRepository;

    

    public TaskService(ITaskRepository taskRepository) {

        super(taskRepository);
        this.taskRepository = taskRepository;
    }

    public List<Task> getTasksInState(String state) {
        return taskRepository.getAllByStateOrderByPriority(state).stream().map(this::convertToEntry).collect(Collectors.toList());
    }

    public Task getTaskByTaskKey(String taskKey) {
        return convertToEntry(taskRepository.getFirstByTaskKey(taskKey));
    }
}
