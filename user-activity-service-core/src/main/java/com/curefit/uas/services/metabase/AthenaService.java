package com.curefit.uas.services.metabase;

import com.curefit.athena.service.client.AthenaServiceClient;
import com.curefit.athena.service.entry.AthenaTaskEntry;
import com.curefit.athena.service.entry.AthenaTaskRequest;
import com.curefit.athena.service.enums.RequestType;
import com.curefit.common.data.exception.BaseException;
import com.curefit.uas.enums.AthenaSource;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Service
@Slf4j
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AthenaService {
    final AthenaServiceClient trinoServiceClient;
    final MetabaseService metabaseService;
    
    @Value("${app.name}") String appName;
    
    // Note:: Pass mozartJobConfigId as null if you do not want to use mozart job config
    public Boolean createAthenaTaskForMetabaseCard(String cardId, String mozartJobConfigId, String querySource) throws BaseException {
        if (StringUtils.isBlank(mozartJobConfigId)) mozartJobConfigId = null;
        
        String metabaseQueryString = metabaseService.getMetabaseCardQuery(cardId).orElse(null);
        if(StringUtils.isBlank(metabaseQueryString)) {
            log.error("createAthenaTaskForMetabaseCard failed for cardId={} since no query was present", cardId);
            return false;
        }
        String taskRefId = "createAthenaTaskForMetabaseCard" + '_' + cardId + '_' + UUID.randomUUID();
        Map<String, Object> metaInfo = new HashMap<>();
        executeQueryOnTrino(metabaseQueryString, taskRefId, metaInfo, RequestType.RUN_QUERY_AND_STREAM_RESULT, mozartJobConfigId, querySource);
        return true;
    }
    /**
     * Executes a query on Trino and returns the AthenaTaskEntry.
     *
     * @param query The SQL query to execute.
     * @param refId A reference ID for the task.
     * @param metaInfo Metadata information for the task.
     * @param requestType The type of request (e.g., RUN_QUERY_AND_STREAM_RESULT).
     * @param mozartJobConfigId Optional Mozart job configuration ID.
     * @param querySource Source of the query, can be null or empty.
     * @return AthenaTaskEntry containing details of the executed task.
     */
    public AthenaTaskEntry executeQueryOnTrino(String query, String refId, Map<String, Object> metaInfo,
                                               RequestType requestType, String mozartJobConfigId, String querySource) {
        if (StringUtils.isBlank(querySource)) {
            if (StringUtils.isNotBlank(mozartJobConfigId)) querySource = AthenaSource.TRINO_METABASE_CARD_MOZART.getValue();
            else querySource = AthenaSource.DEFAULT.getValue();
        }
        AthenaTaskRequest athenaTaskRequest = new AthenaTaskRequest(
                querySource, refId, query, null, metaInfo,
                requestType, mozartJobConfigId);
        return trinoServiceClient.createTask(athenaTaskRequest);
    }
    
}
