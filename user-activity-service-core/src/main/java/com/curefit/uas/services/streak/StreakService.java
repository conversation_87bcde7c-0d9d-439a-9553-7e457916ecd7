package com.curefit.uas.services.streak;

import com.curefit.base.enums.AppTenant;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.logging.models.ActivityTypeDS;
import com.curefit.membership.types.MembershipEvent;
import com.curefit.rashi.client.UserAttributesCacheClient;
import com.curefit.rashi.pojo.UserAttributesResponse;
import com.curefit.segmentation.client.rest.UserSegmentClient;
import com.curefit.socialservice.client.spi.NotificationCenterServiceClient;
import com.curefit.socialservice.enums.NotificationCenterEntryType;
import com.curefit.socialservice.pojo.NotificationCenterEntry;
import com.curefit.socialservice.pojo.NotificationCenterMeta;
import com.curefit.uas.constants.AppDeeplink;
import com.curefit.uas.constants.RashiUserPropertyKey;
import com.curefit.uas.entities.streaks.*;
import com.curefit.uas.enums.streaks.StreakDayType;
import com.curefit.uas.enums.streaks.StreakPauseType;
import com.curefit.uas.enums.streaks.StreakRestDaysResetInterval;
import com.curefit.uas.pojo.StreakActivityRewardConfig;
import com.curefit.uas.pojo.streak.activityDetails.BaseActivityDetails;
import com.curefit.uas.repository.mongo.streaks.*;
import com.curefit.uas.responses.StreakActivityResponse;
import com.curefit.uas.responses.StreakResponse;
import com.curefit.uas.services.ConfigStoreWrapperService;
import com.curefit.uas.services.DistributedLockService;
import com.curefit.uas.services.RashiPublisherService;
import com.curefit.uas.utils.TimeUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.LockTimeoutException;
import javax.ws.rs.BadRequestException;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

@Service
@Slf4j
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class StreakService {
    IStreakConfigRepository streakConfigRepository;
    IStreakActivityLogRepository streakActivityLogRepository;
    ConfigStoreWrapperService configStore;
    RollbarService rollbarService;
    IStreakRepository streakRepository;
    StreakHelperService streakHelperService;
    IBrokenStreakRepository brokenStreakRepository;
    IStreakPauseRepository streakPauseRepository;
    DistributedLockService distributedLockService;
    RashiPublisherService rashiPublisherService;
    UserSegmentClient userSegmentClient;
    NotificationCenterServiceClient notificationCenterServiceClient;
    UserAttributesCacheClient userAttributesCacheClient;
    
    public StreakConfigEntity createStreakConfigEntity(
            String configId,
            String configName,
            String configDescription,
            StreakPauseType pauseType,
            Integer restDaysQuota,
            StreakRestDaysResetInterval restDaysResetInterval,
            Map<String, List<StreakActivityRewardConfig>> activityRewardConfigMap) {
        return streakConfigRepository.save(StreakConfigEntity.builder()
                .configId(configId)
                .configName(configName)
                .configDescription(configDescription)
                .pauseType(pauseType)
                .restDaysQuota(restDaysQuota)
                .restDaysResetInterval(restDaysResetInterval)
                .activityRewardConfigMap(activityRewardConfigMap)
            .build());
    }
    public StreakConfigEntity getStreakConfigByConfigId(String configId) {
        return streakConfigRepository.findByConfigId(configId).orElse(null);
    }
    /*
        inputs:
            userId - mandatory
            fetchDateToDayTypeMapForLast2Weeks - boolean flag for fetching weekly activity details
            fetchEligibleActivitiesWithEndDateScore - boolean flag for fetching eligible activities
        process:
            retrieves current streak info for user
            calculates remaining rest days
            optionally fetches weekly activity map and eligible activities
        output:
            StreakResponse containing streak details, activity map and eligible activities
    */
    public StreakResponse getUserCurrentStreak(String userId, boolean fetchDateToDayTypeMapForLast2Weeks, boolean fetchEligibleActivitiesWithEndDateScore) throws ExecutionException, InterruptedException {
        StreakEntity streakEntity = streakRepository.findByUserId(Long.valueOf(userId)).orElse(new StreakEntity(Long.valueOf(userId)));
        StreakConfigEntity streakConfig = streakHelperService.getStreakConfigForUser(userId);
        StreakResponse streakResponse = new StreakResponse();
        if (streakConfig != null) {
            // get current date to evaluate streak parameters
            LocalDate currentDate = LocalDate.now();
            LocalDate mondayOfCurrentWeek = currentDate.with(DayOfWeek.MONDAY);
            LocalDate sundayOfCurrentWeek = currentDate.with(DayOfWeek.SUNDAY);
            
            streakResponse.setCurrentStreakCount(streakEntity.getStreakCount());
            streakResponse.setCurrentStreakStartDate(streakEntity.getFirstActivityDate());
            streakResponse.setMaximumRestDays(streakConfig.getRestDaysQuota());
            streakResponse.setBestStreakCount(streakEntity.getMaxStreakCount());
            
            if (streakEntity.getStreakCount() > 0) {
                streakResponse.setCurrentStreakEndDate(LocalDate.now().toString());
            }
            
            boolean streakPaused = StringUtils.isNotBlank(streakEntity.getStreakPauseId());
            streakResponse.setStreakPaused(streakPaused);
            
            CompletableFuture<Integer> remainingRestDaysFuture = CompletableFuture.supplyAsync(() -> streakHelperService.calculateRemainingRestDaysForUser(streakEntity, streakConfig, currentDate, mondayOfCurrentWeek, sundayOfCurrentWeek));
            CompletableFuture<List<StreakActivityResponse>> eligibleActivitiesWithEndDateScoreFuture = null;
            CompletableFuture<Map<String, StreakDayType>> dateToDayTypeMapForLast2WeeksFuture = null;
            
            if (fetchEligibleActivitiesWithEndDateScore) {
                eligibleActivitiesWithEndDateScoreFuture = CompletableFuture.supplyAsync(() -> streakHelperService.getEligibleActivitiesWithEndDateScore(userId, currentDate, streakConfig));
            }
            
            if (fetchDateToDayTypeMapForLast2Weeks) {
                LocalDate mondayOfLastWeek = currentDate.minusWeeks(1).with(DayOfWeek.MONDAY);
                dateToDayTypeMapForLast2WeeksFuture = CompletableFuture.supplyAsync(() -> getStreakDetailsForTimePeriod(userId, mondayOfLastWeek.toString(), sundayOfCurrentWeek.toString()));
            }
            
            streakResponse.setRemainingRestDays(remainingRestDaysFuture.get());
            if(eligibleActivitiesWithEndDateScoreFuture != null) {
                streakResponse.setEligibleActivitiesWithEndDateScore(eligibleActivitiesWithEndDateScoreFuture.get());
            }
            if(dateToDayTypeMapForLast2WeeksFuture != null) {
                streakResponse.setDateToDayTypeMap(dateToDayTypeMapForLast2WeeksFuture.get());
            }
        }
        return streakResponse;
    }
    public Map<String, StreakDayType> getStreakDetailsForTimePeriod(String userId, String startDateString, String endDateString) {
        Map<String, StreakDayType> dateToActivitiesDetailsMap = new HashMap<>();
        LocalDate startDate = TimeUtil.getLocalDateFromString(startDateString, null);
        LocalDate endDate = TimeUtil.getLocalDateFromString(endDateString, null);
        
//        // Default config
//        for (LocalDate current = startDate; !current.isAfter(endDate); current = current.plusDays(1)) {
//            dateToActivitiesDetailsMap.put(current.toString(), StreakDayType.REST);
//        }
        
        // Add rest days for valid streaks
        StreakEntity streakEntity = streakRepository.findByUserId(Long.valueOf(userId)).orElse(null);
        if (streakEntity != null && StringUtils.isNotBlank(streakEntity.getFirstActivityDate()) && StringUtils.isNotBlank(streakEntity.getLastActivityDate())) {
            LocalDate spanStartDate = TimeUtil.getLocalDateFromString(streakEntity.getFirstActivityDate(), null);
//            LocalDate spanEndDate = TimeUtil.getLocalDateFromString(streakEntity.getLastActivityDate(), null);
            LocalDate spanEndDate = LocalDate.now();
            if (streakEntity.getStreakCount() > 0) {
                Integer userStreakDayEndHour = configStore.getUserStreakDayEndHour();
                if (LocalTime.of(userStreakDayEndHour, 0).isAfter(LocalDateTime.now().toLocalTime())) {
                    spanEndDate = LocalDate.now().minusDays(1);
                }
            }
            if (spanEndDate.isBefore(TimeUtil.getLocalDateFromString(streakEntity.getLastActivityDate()))) {
                spanEndDate = TimeUtil.getLocalDateFromString(streakEntity.getLastActivityDate());
            }
            if (endDate.isBefore(spanEndDate)) endDate = spanEndDate;
            if (spanStartDate.isBefore(startDate)) startDate = spanStartDate;
            for (LocalDate current = spanStartDate; !current.isAfter(spanEndDate); current = current.plusDays(1)) {
                dateToActivitiesDetailsMap.put(current.toString(), StreakDayType.REST);
            }
        }
        List<BrokenStreakEntity> brokenStreakEntities = brokenStreakRepository.findBrokenStreaksBetweenStartAndEndDate(Long.valueOf(userId), startDateString, endDateString);
        if (CollectionUtils.isNotEmpty(brokenStreakEntities)) {
            for(BrokenStreakEntity brokenStreakEntity : brokenStreakEntities) {
                LocalDate spanStartDate = TimeUtil.getLocalDateFromString(brokenStreakEntity.getFirstActivityDate(), null);
                LocalDate spanEndDate = TimeUtil.getLocalDateFromString(brokenStreakEntity.getLastActivityDate(), null);
                LocalDate streakBreakDate = TimeUtil.getLocalDateFromString(brokenStreakEntity.getTentativeStreakBreakDate(), null);
                if (streakBreakDate.isAfter(spanEndDate)) {
                    spanEndDate = streakBreakDate.minusDays(1);
                }
                if (endDate.isBefore(spanEndDate)) endDate = spanEndDate;
                if (spanStartDate.isBefore(startDate)) startDate = spanStartDate;
                for(LocalDate current = spanStartDate; ! current.isAfter(spanEndDate); current = current.plusDays(1)) {
                    dateToActivitiesDetailsMap.put(current.toString(), StreakDayType.REST);
                }
            }
        }
        
        try {
            List<StreakPauseEntity> streakPausesEntities = streakPauseRepository.findPausesBetweenIncludingDates(Long.valueOf(userId), startDate.toString(), endDate.toString());
            for (StreakPauseEntity streakPauseEntity : streakPausesEntities) {
                LocalDate pauseStart = TimeUtil.getLocalDateFromString(streakPauseEntity.getStartDate(), null);
                LocalDate pauseEnd = endDate;
                if (StringUtils.isNotBlank(streakPauseEntity.getEndDate())) {
                    pauseEnd = TimeUtil.getLocalDateFromString(streakPauseEntity.getEndDate(), null);
                }
                for (LocalDate current = pauseStart; !current.isAfter(pauseEnd); current = current.plusDays(1)) {
                    dateToActivitiesDetailsMap.put(current.toString(), StreakDayType.PAUSED);
                }
            }
        } catch (Exception ex) {
            rollbarService.error(ex, "Error while calculating streak pauses for user: " + userId);
        }
        
        try {
            List<StreakActivityLogEntity> streakActivityLogEntities = streakActivityLogRepository.findAllByUserIdAndDateInRange(Long.valueOf(userId), startDate.toString(), endDate.toString());
            for (StreakActivityLogEntity streakActivityLogEntity: streakActivityLogEntities) {
                dateToActivitiesDetailsMap.put(streakActivityLogEntity.getActivityDate(), StreakDayType.ACTIVE);
            }
        } catch (Exception ex) {
            rollbarService.error(ex, "Error while calculating streak activities for user: " + userId + " and startDate = " + startDate);
        }
        
        return dateToActivitiesDetailsMap;
    }
    
    private String getLockKeyForProcessActivity(String userId) {
        return String.join(DistributedLockService.KEY_SEPARATOR, "processActivity", userId);
    }
    
    @Async
    @Qualifier("mongoDbTransactionManager")
    @Transactional(transactionManager = "mongoDbTransactionManager", rollbackFor = Exception.class, timeout = 300)
    public CompletableFuture<StreakEntity> processActivityWithLock(String userId, BaseActivityDetails currentActivityDetails, ActivityTypeDS activityTypeDS) throws Exception {
        String uniqueIdentifier = getLockKeyForProcessActivity(userId);
        try {
            if (distributedLockService.lock(uniqueIdentifier)) {
                return CompletableFuture.completedFuture(processActivity(userId, currentActivityDetails, activityTypeDS));
            } else {
                String msg = String.format("Locking failed for processActivityWithLock with uniqueIdentifier=%s",
                        uniqueIdentifier);
                LockTimeoutException ex = new LockTimeoutException(msg);
                ex.setStackTrace(Thread.currentThread().getStackTrace());
//                rollbarService.error(ex, msg);
                throw ex;
            }
        } finally {
            distributedLockService.unlock(uniqueIdentifier);
        }
    }
    
    
    /*
        inputs:
            logTag - logging identifier
            currentActivityDetailsString - activity details in string format
            activityTypeDS - type of activity
        process:
            processes new activity for user's streak
            handles streak updates and missed activities
        output:
            updated StreakEntity
    */
    private StreakEntity processActivity(String userId, BaseActivityDetails currentActivityDetails, ActivityTypeDS activityTypeDS) throws Exception {
        StreakConfigEntity streakConfig = streakHelperService.getStreakConfigForUser(userId);
        StreakEntity userCurrentStreak = streakRepository.findByUserId(Long.valueOf(userId)).orElse(null);
        // Validation: Throw error if activity is not of user
        if (!userId.equals(currentActivityDetails.getUserId())) {
            throw new BadRequestException(String.format("Streak Processing Failed since userId in msg attributes is %s while currentActivityDetails userId = %s", userId, currentActivityDetails.getUserId()));
        }
        
        // Validation: If no config present for user, do not process
        if (streakConfig == null) {
            return null;
        }
        
        // Validation: check if this activityType needs to be processed for the config; if not, return
        Set<String> eligibleActivityTypes = streakConfig.getActivityRewardConfigMap().keySet();//stream().map(ActivityTypeDS::valueOf).toList();
        if (!eligibleActivityTypes.contains((activityTypeDS.toString()))) {
            return null;
        }
        
        // Validation: If streak is paused, do not process
//        boolean isStreakPaused = userCurrentStreak != null && StringUtils.isNotBlank(userCurrentStreak.getStreakPauseId());
//        if (isStreakPaused) {
//            rollbarService.log(String.format("%s Streak Processing Failed since streak/membership is paused userCurrentStreak.getId = %s, userCurrentStreak.getStreakPauseId() = %s ", logTag, userCurrentStreak.getId(), userCurrentStreak.getStreakPauseId()));
//            return null;
//        }
        
        // if no streak found for user, create a new streak with back-filling
        if ((userCurrentStreak == null || userCurrentStreak.getStreakCount() == 0)) {
            int newStreakVersion = 0;
            BrokenStreakEntity latestBrokenStreak = brokenStreakRepository.findFirstByUserIdOrderByStreakVersionDesc(Long.valueOf(userId)).orElse(null);
            if (latestBrokenStreak != null) {
                newStreakVersion = latestBrokenStreak.getStreakVersion() + 1;
            } else {
                rashiPublisherService.publishRashiEventForFirstUserStreak(Long.valueOf(userId), currentActivityDetails, streakConfig.getConfigId());
                
                String initialStreakRecomputeDate = configStore.getUserStreakInitialRecomputeDateForConfigId(streakConfig.getConfigId());
                if (StringUtils.isNotBlank(initialStreakRecomputeDate)) {
                    boolean isActivityOlderThanRecomputeDate = TimeUtil.getLocalDateFromString(currentActivityDetails.getDate(), null)
                            .isBefore(TimeUtil.getLocalDateFromString(initialStreakRecomputeDate, null));
                    if (isActivityOlderThanRecomputeDate) {
                        initialStreakRecomputeDate = currentActivityDetails.getDate();
                    }
                    rashiPublisherService.publishRashiEventForNewUserStreak(Long.valueOf(userId), currentActivityDetails, newStreakVersion, streakConfig.getConfigId());
                    return recomputeStreakForUserStartingFromDate(userId, initialStreakRecomputeDate, false);
                }
            }
            rashiPublisherService.publishRashiEventForNewUserStreak(Long.valueOf(userId), currentActivityDetails, newStreakVersion, streakConfig.getConfigId());
            
        }
        
        // If a past missed activity arrives for processing for the user with an active streak, recompute
        if (streakHelperService.isActivityInPastAndProcessingMissed(currentActivityDetails)) {
            return recomputeStreakForUserStartingFromDate(userId, currentActivityDetails.getDate(), false);
        }
        
        // Note: Start Processing Activity
        
        // In case no streak is present for the user, create a default streak
        userCurrentStreak = streakHelperService.resetCurrentStreakForUser(Long.valueOf(currentActivityDetails.getUserId()), currentActivityDetails.getDate(), userCurrentStreak, streakConfig);
        
        
        // update streak config id and remaining rest days in case of config change in period
        if (!streakConfig.getConfigId().equals(userCurrentStreak.getStreakConfigId())) {
            userCurrentStreak.setStreakConfigId(streakConfig.getConfigId());
        }
        
        userCurrentStreak = streakHelperService.updateAndSaveStreakEntityWithActivitiesInPeriod(
                userId, userCurrentStreak.getLastActivityDate(), currentActivityDetails.getDate(),
                currentActivityDetails, userCurrentStreak, streakConfig, false
        );
        return userCurrentStreak;
    }
    
    /*
    inputs:
        eventType - MembershipEvent type (PAUSED/UNPAUSED)
        userId - mandatory
    process:
        handles membership pause/unpause events
        updates streak pause status based on membership status
    output:
        StreakPauseEntity for the updated pause status
*/
    @Qualifier("mongoDbTransactionManager")
    @Transactional(transactionManager = "mongoDbTransactionManager", rollbackFor = Exception.class, timeout = 300)
    public Optional<StreakPauseEntity> consumeMembershipPauseWithLock(String eventTypeString, String userId) throws ExecutionException, JsonProcessingException, InterruptedException {
        String uniqueIdentifier = getLockKeyForConsumeMembershipPause(userId);
        try {
            if (distributedLockService.lock(uniqueIdentifier)) {
                return consumeMembershipPause(eventTypeString, userId);
            } else {
                String msg = String.format("Locking failed for consumeMembershipPauseWithLock with uniqueIdentifier=%s",
                        uniqueIdentifier);
                LockTimeoutException ex = new LockTimeoutException(msg);
                ex.setStackTrace(Thread.currentThread().getStackTrace());
                throw ex;
            }
        } finally {
            distributedLockService.unlock(uniqueIdentifier);
        }
    }
    
    private String getLockKeyForConsumeMembershipPause(String userId) {
        return String.join(DistributedLockService.KEY_SEPARATOR, "consumeMembershipPause", userId);
    }
    private Optional<StreakPauseEntity> consumeMembershipPause(String eventTypeString, String userId) throws JsonProcessingException, ExecutionException, InterruptedException {
        if (!List.of(
                MembershipEvent.UNPAUSED.toString(), MembershipEvent.PAUSE_CANCELED.toString(),
                MembershipEvent.PAUSED.toString(), MembershipEvent.PAUSE_CREATED.toString(), MembershipEvent.EXTENDED.toString(),
                MembershipEvent.CANCELLED.toString()
        ).contains(eventTypeString)) return Optional.empty();
        
        MembershipEvent eventType = MembershipEvent.valueOf(eventTypeString);
//        MembershipPauseSqsMsgBody msgBody = objectMapper.readValue(msgBodyString, MembershipPauseSqsMsgBody.class);
        StreakEntity currentStreak = streakRepository.findByUserId(Long.valueOf(userId)).orElse(null);
        
        if (currentStreak == null) return Optional.empty();
        
        StreakConfigEntity streakConfig = streakHelperService.getStreakConfigForUser(userId);
        boolean isMembershipBasedPauseConfig = streakConfig != null && StreakPauseType.MEMBERSHIP_BASED.equals(streakConfig.getPauseType());
        
        if (!isMembershipBasedPauseConfig) return Optional.empty();
        
        boolean pauseEvent = eventType.equals(MembershipEvent.PAUSE_CREATED) || eventType.equals(MembershipEvent.PAUSED) || eventType.equals(MembershipEvent.EXTENDED);
        boolean unpauseEvent = eventType.equals(MembershipEvent.PAUSE_CANCELED) || eventType.equals(MembershipEvent.UNPAUSED) || eventType.equals(MembershipEvent.CANCELLED);
        
        boolean shouldProcessPause = streakHelperService.shouldProcessPause(Long.valueOf(userId), currentStreak, streakConfig);
        if (pauseEvent && shouldProcessPause) {
            return Optional.ofNullable(streakHelperService.pauseCurrentStreak(Long.valueOf(userId), eventType, currentStreak));
        } else if (unpauseEvent && shouldProcessPause) {
            return Optional.ofNullable(streakHelperService.unpauseCurrentStreak(Long.valueOf(userId), eventType, currentStreak, streakConfig));
        } else {
            rollbarService.error("Skipping event due to shouldProcessPause=false, userId=" + userId + ", eventType=" + eventType);
        }
        
        return Optional.empty();
    }
    
    /*
        inputs:
            userId - mandatory
            requestedRecomputeDate - optional; defaults to current date
        process:
            deletes any streak or broken_streaks or activity_streak_log which had the requestedRecomputeDate within the firstActivityDate and lastActivityDate interval
        output:
            Promise of StreakEntity
    */
    @Qualifier("mongoDbTransactionManager")
    @Transactional(transactionManager = "mongoDbTransactionManager", rollbackFor = Exception.class, timeout = 300)
    public StreakEntity recomputeStreakForUserStartingFromDate(String userId, String requestedRecomputeDate, boolean logRollbarForRecomputation) throws Exception {
        String recomputeDate = requestedRecomputeDate;
        // default re-computation date to last 1 month
        LocalDate currentDate = LocalDate.now();
        if (StringUtils.isBlank(recomputeDate)) recomputeDate = currentDate.minusDays(30).toString();
        try {
            recomputeDate = streakHelperService.cleanupStreaksAndActivitiesAfterDateAndGetNewRecomputeDate(Long.valueOf(userId), recomputeDate);
            
            // Create Default Streak for the User
            StreakConfigEntity userStreakConfig = streakHelperService.getStreakConfigForUser(userId);
            if (userStreakConfig == null) {
                String errMsg = String.format("Streak Processing Failed since no user streak config found for userId = %s", userId);
                log.error(errMsg);
                return null;
            }
            StreakEntity userCurrentStreak = streakRepository.findByUserId(Long.valueOf(userId)).orElse(null);
            userCurrentStreak = streakHelperService.resetCurrentStreakForUser(Long.valueOf(userId), recomputeDate, userCurrentStreak, userStreakConfig);
            userCurrentStreak = streakHelperService.updateAndSaveStreakEntityWithActivitiesInPeriod(userId, recomputeDate, currentDate.toString(), null, userCurrentStreak, userStreakConfig, true);
            log.info("Streak_Re_Computation Completed for userId={} requested_recomputeDate={} actual_recomputeDate={} and newStreakId={}", userId, requestedRecomputeDate, recomputeDate, userCurrentStreak.getId());
            return userCurrentStreak;
        } catch(Exception ex) {
            if (logRollbarForRecomputation) rollbarService.error(ex, "Streak_Re_Computation Error for userId=" + userId + " requested_recomputeDate=" + requestedRecomputeDate + " actual_recomputeDate=" + recomputeDate);
            throw ex;
        }
    }
    
    @Async
    public void sendNewStreakFirstRestDayConsumption(String segmentId, Integer batchOffset, Integer batchSize) {
        // Note:: segment id for this is currently 26006. Made it pass as a parameter to make it dynamic in future if needed
        int count = 0;
        try {
            boolean isSegmentIterated = false;
            
            while (!isSegmentIterated) {
                List<String> userIdsInSegment = userSegmentClient.getUserIdsInSegment(segmentId, AppTenant.CUREFIT, batchOffset.toString(), batchSize.toString());
                if (CollectionUtils.isNotEmpty(userIdsInSegment)) {
                    for (String userId : userIdsInSegment) {
                        boolean notificationSent = sendNewStreakFirstRestDayConsumptionForUser(userId);
                        if (notificationSent) count++;
                    }
                    batchOffset = batchOffset + userIdsInSegment.size();
                } else {
                    isSegmentIterated = true;
                }
            }
        } catch (Exception ex) {
            rollbarService.error(ex, "Notification Sending failed for segmentId = " + segmentId);
        }
        log.info("sendNewStreakFirstRestDayConsumption Notification sent for segmentId={} for {} users till batchOffset={} and batchSize={}", segmentId, count, batchOffset, batchSize);
    }
    
    private boolean sendNewStreakFirstRestDayConsumptionForUser(String userId) {
        Object userStreakCountRashiAttribute = null;
        try {
            int userStreakCount = 0;
            UserAttributesResponse userAttributesResponse = userAttributesCacheClient.getAttributes(Long.valueOf(userId), List.of(RashiUserPropertyKey.user_streak_count.toString()), AppTenant.CUREFIT);
            userStreakCountRashiAttribute = userAttributesResponse.getAttributes().get(RashiUserPropertyKey.user_streak_count.toString());
            if (userStreakCountRashiAttribute != null) {
                userStreakCount = Integer.parseInt((String) userStreakCountRashiAttribute);
            } else {
                StreakEntity userStreak = streakRepository.findByUserId(Long.valueOf(userId)).orElse(null);
                if (userStreak != null) userStreakCount = userStreak.getStreakCount();
            }
            
            NotificationCenterMeta notificationContent = new NotificationCenterMeta();
            notificationContent.setTitle(String.format("Rest Day used - %d days streak safe!", userStreakCount));
            notificationContent.setSubTitle("Recharge and come back tomorrow.");
            notificationContent.setLogo("https://cdn-images.cure.fit/www-curefit-com/image/upload/fl_progressive,f_auto,w_100/dpr_2/image/vm/294cf357-8a7a-4318-a46a-c621b96d66bd.svg");
            notificationContent.setDeeplink(AppDeeplink.USER_STREAK_MAIN_PAGE.getDeeplinkString());
          
            notificationContent.setSubTitle("Recharge and come back tomorrow.");
            NotificationCenterEntry notification = new NotificationCenterEntry();
            notification.setUserId(userId);
            notification.setNotificationId("Streak_FirstRestConsumedNewStreak_" + UUID.randomUUID());
            notification.setType(NotificationCenterEntryType.GENERAL);
            notification.setMeta(notificationContent);
            notification.setCreatedAt(new Date());
            
            notificationCenterServiceClient.sendNotification(userId, List.of(notification));
            return true;
        } catch (Exception ex) {
            rollbarService.error(ex, "Notification Sending failed for userId = " + userId + " and userStreakCountRashiAttribute = " + userStreakCountRashiAttribute);
            return false;
        }
    }
    
}
