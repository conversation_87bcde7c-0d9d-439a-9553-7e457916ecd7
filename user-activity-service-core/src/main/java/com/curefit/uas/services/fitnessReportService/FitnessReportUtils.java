package com.curefit.uas.services.fitnessReportService;

import com.curefit.cult.models.ClassDetails;
import com.curefit.cult.models.CultClass;
import com.curefit.cult.models.WorkoutCount;
import com.curefit.cult.models.WorkoutDetails;
import com.curefit.uas.entities.FitnessReportEntity;
import com.curefit.uas.utils.FitnessReportConstants;
import com.curefit.uas.utils.TimeUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.lang.reflect.Field;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
public class FitnessReportUtils {
    
    
    static String getStepsDayKey(String startDate) {
        return "steps_" + startDate;
    }
    
    public static Float getBMI(Float weightInKg, Float heightInCm) {
        // Check if weight or height is null
        if (weightInKg == null || heightInCm == null) {
            return null;
        }
        
        // Calculate BMI
        float bmi = (weightInKg * 10000) / (heightInCm * heightInCm);
        
        // Return BMI rounded to 2 decimal places
        return Math.round(bmi * 100f) / 100f;
    }
    static int insertEntryInOrder(List<FitnessReportEntity> sortedEntries, FitnessReportEntity entry) {
        int index = 0;
        while(index < sortedEntries.size() && sortedEntries.get(index).getStartDate().compareTo(entry.getStartDate()) <= 0) {
            index++;
        }
        sortedEntries.add(index, entry);
        return index;
    }
    // Doubt: When does wodId come from top level and when does it come from workout?
    static String getWodIdFromCultClass(CultClass cultClass) {
        if (cultClass != null) {
            if (cultClass.getWodId() != null) {
                return cultClass.getWodId();
            } else if (cultClass.getWorkout() != null && cultClass.getWorkout().getDefaultWodID() != null) {
                return cultClass.getWorkout().getDefaultWodID();
//            } else if (configStore.getBooleanValueForConfigKey(AppConfigKey.FITNESS_REPORT_ENABLE_ROLLBAR, false)) {
//                String msg = "wodId and workout default wodId dont exist for class id " + cultClass.getId();
//                log.error(msg);
//                rollbarService.error(msg);
            } else {
                String msg = "wodId and workout default wodId dont exist for class id " + cultClass.getId();
                log.warn(msg);
            }
        }
        return null;
    }
    static Map<String, Integer> getUpdatedCalorieMapForUFSWodCompletion(Map<String, Integer> currentClassCalorieMap, ClassDetails classDetails, String wodIdString) {
        String classId = classDetails.getId().toString();
        Map<String, Integer> classCalorieMap = new HashMap<>();
        currentClassCalorieMap.forEach((key, value) -> {
            if (key.contains(classId)) {
                // Doubt:: What is being done here?
                classCalorieMap.put(key + wodIdString, value);
            } else {
                classCalorieMap.put(key, value);
            }
        });
        return classCalorieMap;
    }
    
    public static FitnessReportEntity getLatestEntityBeforeOrOnGivenDate(List<FitnessReportEntity> prevEntriesSortedNewToOld, String startDate) {
        if (CollectionUtils.isEmpty(prevEntriesSortedNewToOld)) return null;
        for (FitnessReportEntity fitnessReportEntity : prevEntriesSortedNewToOld) {
            if (fitnessReportEntity.getStartDate().compareTo(startDate) <= 0 ) {
                return fitnessReportEntity;
            }
        }
        return null;
    }
//    public static FitnessReportEntity getLatestEntryBeforeOrOnGivenDate(List<FitnessReportEntity> prevEntitiesSortedNewToOld, String startDate) {
//        if (CollectionUtils.isEmpty(prevEntitiesSortedNewToOld)) return null;
//        for (FitnessReportEntity fitnessReportEntity : prevEntitiesSortedNewToOld) {
//            if (fitnessReportEntity.getStartDate().compareTo(startDate) <= 0 ) {
//                return fitnessReportEntity;
//            }
//        }
//        return null;
//    }
    static List<WorkoutCount> generateWorkoutCounts(List<WorkoutCount> workoutCounts, WorkoutDetails workoutDetails, ClassDetails classDetails) {
        if (workoutDetails == null) {
            return workoutCounts;
        }
        
        // Get current datetime in IST using getMomentWithOffset
        String currentDateTime = TimeUtil.getMomentWithOffset(FitnessReportConstants.INDIA_TIMEZONE).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        
        Object workoutId = workoutDetails.getId();
        String workoutName = workoutDetails.getName();
        
        // Find existing workout entry
        Optional<WorkoutCount> existingEntry = workoutCounts.stream()
                .filter(workout -> workout.getName().equals(workoutName))
                .findFirst();
        
        if (existingEntry.isPresent()) {
            WorkoutCount workoutEntry = existingEntry.get();
            workoutEntry.setCount(workoutEntry.getCount()+1);
            workoutEntry.setName(workoutName);
            if (workoutEntry.getEventTime() != null && classDetails != null) {
                workoutEntry.getEventTime().put(classDetails.getId().toString(), currentDateTime);
            }
        } else {
            WorkoutCount newWorkoutEntry = new WorkoutCount(workoutId, 1, workoutName, null);
            if (classDetails != null) {
                newWorkoutEntry.setEventTime(new HashMap<>(Map.of(classDetails.getId().toString(), currentDateTime)));
            }
            workoutCounts.add(newWorkoutEntry);
        }
        // Sort by count in descending order
        workoutCounts.sort((a, b) -> Long.compare(b.getCount(), a.getCount()));
        return workoutCounts;
    }
    static FitnessReportEntity getEntryOnOrAfter(List<FitnessReportEntity> fitnessReportEntities, String startDate) {
        if (CollectionUtils.isEmpty(fitnessReportEntities)) return null;
        for (FitnessReportEntity fitnessReportEntity : fitnessReportEntities) {
            if (fitnessReportEntity.getStartDate().compareTo(startDate) >= 0) {
                return fitnessReportEntity;
            }
        }
        return null;
    }
    static FitnessReportEntity getMatchingEntryWithStartDate(List<FitnessReportEntity> fitnessReportEntities, String startDate) {
        if (CollectionUtils.isEmpty(fitnessReportEntities)) return null;
        for (FitnessReportEntity fitnessReportEntity : fitnessReportEntities) {
            if (fitnessReportEntity.getStartDate().equals(startDate)) {
                return fitnessReportEntity;
            }
        }
        return null;
    }
    static int getCaloriesBurned(Float userWeight, double metValue, double classDurationInHr) {
        userWeight = ObjectUtils.defaultIfNull(userWeight, 75.0f);
        return (int) Math.ceil(userWeight * metValue * classDurationInHr);
    }
    
    static FitnessReportEntity partialUpdateDataAndReturnDeepCopy(FitnessReportEntity originalData, FitnessReportEntity overrideData) throws JsonProcessingException, IllegalAccessException {
        if (overrideData == null && originalData == null) {
            return null;
        } else if (overrideData == null) {
            return originalData;
        } else if (originalData == null) {
            return overrideData;
        }
        ObjectMapper objectMapper = new ObjectMapper();
        FitnessReportEntity entity = objectMapper.readValue((objectMapper.writeValueAsString(originalData)), FitnessReportEntity.class);
        Field[] fields = FitnessReportEntity.class.getFields();
        for (Field field: fields) {
            field.setAccessible(true);
            Object overrideValue = field.get(overrideData);
            if (overrideValue != null) {
                field.set(entity, overrideValue);
            }
        }
        return entity;
        
        // create new deep copy of incoming originalData
//        FitnessReportEntity mergedEntity = objectMapper.readValue((objectMapper.writeValueAsString(originalData)), FitnessReportEntity.class);
//        if (overrideData.getId() != null) mergedEntity.setId(overrideData.getId());
//        if (overrideData.getUserId() != null) mergedEntity.setUserId(overrideData.getUserId());
//        if (overrideData.getInterval() != null) mergedEntity.setInterval(overrideData.getInterval());
//        if (overrideData.getStartDate() != null) mergedEntity.setStartDate(overrideData.getStartDate());
//        if (overrideData.getEndDate() != null) mergedEntity.setEndDate(overrideData.getEndDate());
//        if (overrideData.getClassesAttended() != null) mergedEntity.setClassesAttended(overrideData.getClassesAttended());
//        if (overrideData.getMaxClassesAttended() != null) mergedEntity.setMaxClassesAttended(Math.max(overrideData.getMaxClassesAttended(), originalData.getMaxClassesAttended()));
//        if (overrideData.getPercentile() != null) mergedEntity.setPercentile(overrideData.getPercentile());
//        if (overrideData.getCaloriesBurned() != null) mergedEntity.setCaloriesBurned(overrideData.getCaloriesBurned());
//        if (overrideData.getWeight() != null) mergedEntity.setWeight(overrideData.getWeight());
//        if (overrideData.getWeightUpdatedEpoch() != null) mergedEntity.setWeightUpdatedEpoch(overrideData.getWeightUpdatedEpoch());
//        if (overrideData.getHeight() != null) mergedEntity.setHeight(overrideData.getHeight());
//        if (overrideData.getBmi() != null) mergedEntity.setBmi(overrideData.getBmi());
//        if (overrideData.getStreak() != null) mergedEntity.setStreak(overrideData.getStreak());
//        if (CollectionUtils.isNotEmpty(overrideData.getClassList())) mergedEntity.setClassList(overrideData.getClassList());
//        if (CollectionUtils.isNotEmpty(overrideData.getWorkoutCounts())) mergedEntity.setWorkoutCounts(overrideData.getWorkoutCounts());
//        if (CollectionUtils.isNotEmpty(overrideData.getBenefits())) mergedEntity.setBenefits(overrideData.getBenefits());
//        if (CollectionUtils.isNotEmpty(overrideData.getBodyParts())) mergedEntity.setBodyParts(overrideData.getBodyParts());
//        if (overrideData.getImageUrl() != null) mergedEntity.setImageUrl(overrideData.getImageUrl());
//        if (overrideData.getImageFilter() != null) mergedEntity.setImageFilter(overrideData.getImageFilter());
//        if (overrideData.getTimezone() != null) mergedEntity.setTimezone(overrideData.getTimezone());
//        if (overrideData.getDeletedAt() != null) mergedEntity.setDeletedAt(overrideData.getDeletedAt());
//        if (overrideData.getClassCalorieMap() != null) mergedEntity.setClassCalorieMap(mergeClassCalorieMaps(overrideData.getClassCalorieMap(), originalData.getClassCalorieMap()));
//        if (overrideData.getTotalClassesAttended() != null) mergedEntity.setTotalClassesAttended(overrideData.getTotalClassesAttended());
//        if (overrideData.getMaxWeeklyStreak() != null) mergedEntity.setMaxWeeklyStreak(Math.max(overrideData.getMaxWeeklyStreak(), originalData.getMaxWeeklyStreak()));
        
//        mergedEntity.setCaloriesBurned(overrideData.getCaloriesBurned() + originalData.getCaloriesBurned());
//        mergedEntity.setWeight(overrideData.getWeight() != null ? overrideData.getWeight() : originalData.getWeight());
//        mergedEntity.setWeightUpdatedEpoch(overrideData.getWeightUpdatedEpoch() != null ? overrideData.getWeightUpdatedEpoch() : originalData.getWeightUpdatedEpoch());
//        mergedEntity.setHeight(overrideData.getHeight() != null ? overrideData.getHeight() : originalData.getHeight());
//        mergedEntity.setBmi(overrideData.getBmi() != null ? overrideData.getBmi() : originalData.getBmi());
//        mergedEntity.setStreak(overrideData.getStreak() + originalData.getStreak());
//        mergedEntity.setClassList(Stream.concat(overrideData.getClassList().stream(), originalData.getClassList().stream()).toList());
//        mergedEntity.setWorkoutCounts(Stream.concat(overrideData.getWorkoutCounts().stream(), originalData.getWorkoutCounts().stream()).toList());
//        mergedEntity.setBenefits(Stream.concat(overrideData.getBenefits().stream(), originalData.getBenefits().stream()).toList());
//        mergedEntity.setBodyParts(Stream.concat(overrideData.getBodyParts().stream(), originalData.getBodyParts().stream()).toList());
//        mergedEntity.setImageUrl(overrideData.getImageUrl() != null ? overrideData.getImageUrl() : originalData.getImageUrl());
//        mergedEntity.setImageFilter(overrideData.getImageFilter() != null ? overrideData.getImageFilter() : originalData.getImageFilter());
//        mergedEntity.setTimezone(overrideData.getTimezone() != null ? overrideData.getTimezone() : originalData.getTimezone());
//        mergedEntity.setDeletedAt(overrideData.getDeletedAt() != null ? overrideData.getDeletedAt() : originalData.getDeletedAt());
//        mergedEntity.setClassCalorieMap(mergeClassCalorieMaps(overrideData.getClassCalorieMap(), originalData.getClassCalorieMap()));
//        mergedEntity.setTotalClassesAttended(overrideData.getTotalClassesAttended() + originalData.getTotalClassesAttended());
//        mergedEntity.setMaxWeeklyStreak(Math.max(overrideData.getMaxWeeklyStreak(), originalData.getMaxWeeklyStreak()));
        
//        return mergedEntity;
    }
    
    private static Map<String, Integer> mergeClassCalorieMaps(Map<String, Integer> map1, Map<String, Integer> map2) {
        Map<String, Integer> mergedMap = new HashMap<>(map1);
        map2.forEach((key, value) -> mergedMap.merge(key, value, Integer::sum));
        return mergedMap;
    }
}
