package com.curefit.uas.services;

import com.curefit.configstore.sdk.AppConfigCache;
import com.curefit.uas.constants.AppConfigKey;
import com.curefit.uas.pojo.fitnessReport.FitnessReportBodyPart;
import com.curefit.uas.pojo.fitnessReport.FitnessReportInfo;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@FieldDefaults(level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
public class ConfigStoreWrapperService {
    private static final Logger log = LoggerFactory.getLogger(ConfigStoreWrapperService.class);
    final AppConfigCache appConfigCache;
    
    public Map<String, String> getUserSegmentNameToStreakConfigIdMap() {
        try {
            return appConfigCache.getConfig(AppConfigKey.USER_SEGMENT_NAME_TO_STREAK_CONFIG_ID.toString(), new TypeReference<Map<String, String>>() {
            }, new HashMap<>());
        } catch(Exception ex) {
            return new HashMap<>();
        }
    }
    public Integer getUserStreakDayEndHour() {
        Integer defaultUserStreakDayEndHour = 22;
        try {
            return appConfigCache.getConfig(AppConfigKey.USER_STREAK_DAY_END_HOUR.toString(), new TypeReference<>() {
            }, defaultUserStreakDayEndHour);
        } catch(Exception ex) {
            return defaultUserStreakDayEndHour;
        }
    }

    public String getUserStreakInitialRecomputeDateForConfigId(String configId) {
        try {
            Map<String, String> initialRecomputeDateMap = appConfigCache.getConfig(AppConfigKey.USER_STREAK_INITIAL_RECOMPUTE_DATE.toString(), new TypeReference<Map<String, String>>() {
            }, new HashMap<>());
            if (initialRecomputeDateMap.containsKey(configId)) {
                return initialRecomputeDateMap.get(configId);
            } else if (initialRecomputeDateMap.containsKey("default")) {
                return initialRecomputeDateMap.get("default");
            }
        } catch(Exception ex) {
            log.error("Error while getting initialRecomputeDate for configId: {} with error = {}", configId, ex.getMessage());
        }
        
        return null;
    }
    public Boolean getBooleanValueForConfigKey(AppConfigKey appConfigKey, Boolean defaultVal) {
        try {
            return appConfigCache.getConfig(appConfigKey.toString(), new TypeReference<Boolean>() {
            }, defaultVal);
        } catch (Exception ex) {
            return defaultVal;
        }
    }
    public String getStringValueForConfigKey(AppConfigKey appConfigKey, String defaultVal) {
        try {
            return appConfigCache.getConfig(appConfigKey.toString(), new TypeReference<String>() {
            }, defaultVal);
        } catch (Exception ex) {
            return defaultVal;
        }
    }
    
    public List<String> getListOfStringsValueForConfigKey(AppConfigKey appConfigKey, List<String> defaultVal) {
        try {
            return appConfigCache.getConfig(appConfigKey.toString(), new TypeReference<List<String>>() {
            }, defaultVal);
        } catch(Exception ex) {
            return defaultVal;
        }
    }
    public List<FitnessReportBodyPart> getFitnessReportBodyParts() {
        try {
            return appConfigCache.getConfig(AppConfigKey.FITNESS_REPORT_BODY_PARTS.toString(), new TypeReference<List<FitnessReportBodyPart>>() {
            }, new ArrayList<>());
        } catch(Exception ex) {
            return new ArrayList<>();
        }
    }
    public Map<String, List<FitnessReportBodyPart>> getFitnessReportPrimaryMusclesToBodyPartsMap() {
        try {
            return appConfigCache.getConfig(AppConfigKey.FITNESS_REPORT_PRIMARY_MUSCLES_TO_BODY_PARTS_MAP.toString(), new TypeReference<Map<String, List<FitnessReportBodyPart>>>() {
            }, new HashMap<>());
        } catch(Exception ex) {
            return new HashMap<>();
        }
    }
    public Map<String, List<String>> getUserFitnessServiceGoalIdToBenefits() {
        try {
            return appConfigCache.getConfig(AppConfigKey.USER_FITNESS_SERVICE_GOAL_ID_TO_BENEFITS.toString(), new TypeReference<Map<String, List<String>>>() {
            }, new HashMap<>());
        } catch(Exception ex) {
            return new HashMap<>();
        }
    }
    public Map<String, Object> getUserYearlyFitnessAttributeConfig() {
        try {
            return appConfigCache.getConfig(AppConfigKey.USER_YEARLY_FITNESS_ATTRIBUTE_CONFIG.toString(), new TypeReference<Map<String, Object>>() {
            }, new HashMap<>());
        } catch(Exception ex) {
            return new HashMap<>();
        }
    }
    public Map<String, FitnessReportInfo> getFitnessReportInfoSection() {
        try {
            return appConfigCache.getConfig(AppConfigKey.FITNESS_REPORT_INFO_SECTION.toString(), new TypeReference<Map<String, FitnessReportInfo>>() {
            }, new HashMap<>());
        } catch(Exception ex) {
            return new HashMap<>();
        }
    }
    public Double getFitnessReportResponseCompareRate() {
        try {
            String compareRate = appConfigCache.getConfig(AppConfigKey.FITNESS_REPORT_RESPONSE_COMPARE_RATE.toString(), new TypeReference<String>() {
            }, "0.0");
            return Double.parseDouble(compareRate);
        } catch (Exception ex) {
            return 0.0;
        }
    }
    
    public List<String> getFieldNamesToIgnoreComparison() {
        try {
            return appConfigCache.getConfig(AppConfigKey.FITNESS_REPORT_RESPONSE_COMPARE_FIELD_NAMES_TO_IGNORE.toString(), new TypeReference<List<String>>() {
            }, new ArrayList<>());
        } catch(Exception ex) {
            return new ArrayList<>();
        }
    }
}
