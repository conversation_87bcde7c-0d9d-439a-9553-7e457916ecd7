package com.curefit.uas.services.fitnessReportService;

import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.cult.models.ClassDetails;
import com.curefit.cult.models.CultClass;
import com.curefit.cult.models.WorkoutDetails;
import com.curefit.cult.services.impl.CultServiceImpl;
import com.curefit.hercules.client.HerculesService;
import com.curefit.metricservice.client.MetricClient;
import com.curefit.uas.enums.ActivityType;
import com.curefit.uas.pojo.fitnessReport.CultClassUpdateFitnessReportBody;
import com.curefit.uas.pojo.fitnessReport.BaseUpdateFitnessReportBody;
import com.curefit.uas.mapper.FitnessReportMapper;
import com.curefit.uas.pojo.fitnessReport.FitnessReportEntry;
import com.curefit.uas.pojo.fitnessReport.LiveUpdateFitnessReportBody;
import com.curefit.uas.publisher.FitnessReportHabitAndMilestonesPublisher;
import com.curefit.uas.services.ConfigStoreWrapperService;
import com.curefit.commons.lock.LockService;
import com.curefit.gymfit.client.GymfitClient;
import com.curefit.rashi.client.RashiClient;
import com.curefit.rashi.client.UserAttributesClient;
import com.curefit.segmentation.client.rest.SegmentationClient;
import com.curefit.segmentation.client.rest.UserSegmentClient;
import com.curefit.uas.repository.mongo.FitnessReportRepository;
import com.curefit.uas.utils.ObjectUtils;
import com.curefit.uas.utils.TimeUtil;
import com.curefit.ufs.services.UfsService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.persistence.EntityNotFoundException;

@Service
@Slf4j
class CultClassUpdateFitnessReportService extends BaseUpdateFitnessReportService implements UpdateFitnessReportService {
    CultServiceImpl cultServiceImpl;
    public CultClassUpdateFitnessReportService(ObjectMapper objectMapper, FitnessReportMapper fitnessReportMapper, FitnessReportHabitAndMilestonesPublisher fitnessReportHabitAndMilestonesPublisher, HerculesService herculesService, ConfigStoreWrapperService configStore, RollbarService rollbarService, MetricClient metricClient, UfsService ufsService, GymfitClient gymfitClient, LockService lockService, UserSegmentClient userSegmentClient, SegmentationClient segmentationClient, UserAttributesClient userAttributesClient, RashiClient rashiClient, FitnessReportRepository fitnessReportRepository, CultServiceImpl cultServiceImpl, UpdateFitnessReportHelperService updateFitnessReportHelperService) {
        super(objectMapper, fitnessReportMapper, fitnessReportHabitAndMilestonesPublisher, herculesService, configStore, rollbarService, metricClient, ufsService, gymfitClient, lockService, userSegmentClient, segmentationClient, userAttributesClient, rashiClient, fitnessReportRepository, updateFitnessReportHelperService);
        this.cultServiceImpl = cultServiceImpl;
    }
    
    
    @Override
    public boolean isEligibleActivityType(ActivityType activityType) {
        return ActivityType.CULT_CLASS.equals(activityType);
    }
    
    @Override
    public FitnessReportEntry processClass(String sqsMsgBodyString, String logTag) throws Exception {
        CultClassUpdateFitnessReportBody cultClassUpdateFitnessReportBody = objectMapper.readValue(sqsMsgBodyString, CultClassUpdateFitnessReportBody.class);
        Long userId = cultClassUpdateFitnessReportBody.getUserId();
        Integer cultClassId = cultClassUpdateFitnessReportBody.getClassId();
        try {
            // Fetch Cult Class for classId
            CultClass cultClass = cultServiceImpl.getCultClass(String.valueOf(cultClassId), false, false, false, true, true, true).get().getCultClass();
            if (cultClass == null) {
                log.error("Cult class not found for cultClassId: {} for userId: {}", cultClassId, userId);
//                throw new EntityNotFoundException("No cult class found for cultClassId = " + cultClassId + " and userId = " + userId);
                return null;
            }
            long durationMinutes = TimeUtil.getMinutesDifference(cultClass.getStartDateTimeUTC(), cultClass.getEndDateTimeUTC());
            ClassDetails classDetails = new ClassDetails();
            classDetails.setId(cultClass.getId());
            classDetails.setDate(cultClass.getDate());
            classDetails.setWodId(FitnessReportUtils.getWodIdFromCultClass(cultClass));
            classDetails.setTimezone(cultClass.getTimezone());
            classDetails.setDurationH(durationMinutes / 60.00f);
            classDetails.setDurationMinutes((int) durationMinutes);
            classDetails.setCenterServiceId(Long.parseLong(cultClass.getCenter().getCenterServiceId()));
            if (cultClass.getWorkout() != null && cultClass.getWorkout().getOtherAttributes() != null && cultClass.getWorkout().getOtherAttributes().get("averageCaloriesBurnt") != null) {
                classDetails.setCalories(ObjectUtils.convertToInteger(cultClass.getWorkout().getOtherAttributes().get("averageCaloriesBurnt")));
            }
            return this.processClass(userId,
                    classDetails,
                    new WorkoutDetails(cultClass.getWorkout().getId(), cultClass.getWorkout().getName()),
                    null);
        } catch(Exception e) {
            rollbarService.error(e, logTag + "Error while fetching cult class details for cultClassId: {}" + cultClassId);
            throw e;
        }
    }
}
