package com.curefit.uas.services.metabase;

import com.curefit.common.data.exception.BaseException;
import com.curefit.metabase.auth.MetabaseCardAPIClient;
import com.curefit.metabase.models.CardInfoResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;


@Service
@Slf4j
public class MetabaseService {

    @Autowired
    MetabaseCardAPIClient metabaseCardAPIClient;
    @Autowired
    ObjectMapper objectMapper;
    
    public Optional<String> getMetabaseCardQuery(String cardID) throws BaseException {
        CardInfoResponse response = metabaseCardAPIClient.getCard(cardID);
        return Optional.ofNullable(response)
                .map(CardInfoResponse::getDatasetQuery)
                .map(CardInfoResponse.DatasetQuery::getNativeResponse)
                .map(CardInfoResponse.Native::getQuery);
    }
    
    /*
        Assuming the response of cardID has the following columns:
            - campaignId - String
            - creativeId - String
            - userId - String
            - tags - Map<String,String> :: {"gender":"Male","name":"Amit"}
     */
//    public List<SendCampaignNotificationsRequest> getPNRequestsFromMetabaseCard(String cardID) throws BaseException {
//        CardRequest cardRequest = new CardRequest();
//        cardRequest.setIgnore_cache(true);
//        LinkedHashMap<String, Object> response = metabaseCardAPIClient.fetchCardResponse(cardID, cardRequest);
//
//        if (response == null || response.get("data") == null) {
//            log.error("No data found in the response");
//            return Collections.emptyList();
//        }
//
//        List<SendCampaignNotificationsRequest> sendCampaignNotificationsRequests = new ArrayList<>();
//        LinkedHashMap<String, Object> data = (LinkedHashMap<String, Object>) response.get("data");
//        List<List<Object>> rows = (List<List<Object>>) data.get("rows");
//        List<Map<String, String>> cols = (List<Map<String, String>>) data.get("cols");
//
//
//        for (List<Object> row : rows) {
//            SendCampaignNotificationsRequest sendCampaignNotificationsRequest = new SendCampaignNotificationsRequest();
//            for (int i = 0; i < cols.size(); i++) {
//                String columnName = cols.get(i).get("name");
//                String columnValue = row.get(i) != null ? row.get(i).toString() : null;
//                switch (columnName) {
//                    case "campaignId":
//                        sendCampaignNotificationsRequest.setCampaignId(columnValue);
//                        break;
//                    case "creativeId":
//                        sendCampaignNotificationsRequest.setCreativeIds(Collections.singletonList(columnValue));
//                        break;
//                    case "userId":
//                        UserContext userContext = new UserContext();
//                        userContext.setUserId(columnValue);
//                        sendCampaignNotificationsRequest.setUserContexts(Collections.singletonList(userContext));
//                        break;
//                    case "tags":
//                        if (columnValue != null && !columnValue.isEmpty()) {
//                            Map<String, Object> tags = new HashMap<>();
//                            try {
//                                tags = objectMapper.readValue(columnValue, new TypeReference<>() {});
//                            } catch (JsonProcessingException e) {
//                                log.error("Failed to parse tags JSON: {}", columnValue, e);
//                            }
//                            sendCampaignNotificationsRequest.setGlobalTags(tags);
//                        }
//                        break;
//                }
//            }
//            sendCampaignNotificationsRequests.add(sendCampaignNotificationsRequest);
//        }
//        return sendCampaignNotificationsRequests;
//    }
}
