package com.curefit.uas.services;

import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.uas.entities.UserTaskMappingEntity;
import com.curefit.uas.pojo.entries.UserTaskMapping;
import com.curefit.uas.repository.jpa.IUserTaskMappingRepository;
import com.curefit.uas.services.intefaces.IUserTaskMappingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserTaskMappingService extends BaseMySQLService<UserTaskMappingEntity, UserTaskMapping> implements IUserTaskMappingService {

    IUserTaskMappingRepository userTaskMappingRepository;

    public UserTaskMappingService(IUserTaskMappingRepository userTaskMappingRepository) {
        super(userTaskMappingRepository);
        this.userTaskMappingRepository = userTaskMappingRepository;
    }

    public List<UserTaskMapping> getStatesForUser(Long userId, String state) {
        List<UserTaskMapping> userTasks = userTaskMappingRepository.findAllByUserIdAndState(userId, state).stream().
                map(this::convertToEntry).collect(Collectors.toList());

        return userTasks;
    }

    public UserTaskMapping getTaskForUser(Long userId, Long taskId) {
        UserTaskMappingEntity userTaskMappingEntity = userTaskMappingRepository.findFirstByUserIdAndTaskId(userId, taskId);

        if(userTaskMappingEntity != null)
            return convertToEntry(userTaskMappingEntity);

        return null;
    }
}
