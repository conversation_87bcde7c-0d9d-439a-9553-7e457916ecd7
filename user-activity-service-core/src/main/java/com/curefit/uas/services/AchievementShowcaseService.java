package com.curefit.uas.services;

import com.curefit.uas.entities.AchievementShowcaseEntity;
import com.curefit.uas.repository.mongo.AchievementShowcaseRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Service for managing Achievement Showcase data
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AchievementShowcaseService {

    private final AchievementShowcaseRepository achievementShowcaseRepository;

    /**
     * Dump Achievement Showcase data to MongoDB
     * 
     * @param rawData List of raw data maps from query results
     * @param athenaTaskId The Athena task ID for tracking
     * @return Number of records saved
     */
    public int dumpDataToDatabase(List<Map<String, Object>> rawData, String athenaTaskId) {
        log.info("ASR: Starting to dump {} records to database for taskId: {}", rawData.size(), athenaTaskId);
        
        List<AchievementShowcaseEntity> entities = convertToEntities(rawData, athenaTaskId);

        // Upsert in batches for efficiency (update if exists, create if not)
        List<AchievementShowcaseEntity> upsertedEntities = upsertInBatches(entities);
        
        log.info("ASR: Successfully upserted {} records to database for taskId: {}", upsertedEntities.size(), athenaTaskId);
        return upsertedEntities.size();
    }

    /**
     * Convert raw data to entities
     */
    private List<AchievementShowcaseEntity> convertToEntities(List<Map<String, Object>> rawData, String athenaTaskId) {
        List<AchievementShowcaseEntity> entities = new ArrayList<>();
        String currentDate = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
        long currentTimestamp = System.currentTimeMillis();
        
        for (Map<String, Object> row : rawData) {
            try {
                AchievementShowcaseEntity entity = new AchievementShowcaseEntity();
                
                // Map Metabase field names to DB field names
                entity.setUserId(extractLongValue(row, "user_id"));
                entity.setClassesAttended(extractIntegerValue(row, "classes"));
                entity.setCalories(extractIntegerValue(row, "calorie_burnt"));
                entity.setTotalMinutesWorked(extractIntegerValue(row, "total_minutes_worked"));
                entity.setWeeksActive(extractIntegerValue(row, "weeks_active"));

                entity.setFavorateFormat1(extractStringValue(row, "favorate_format"));
                entity.setFavorateFormat1Classes(extractIntegerValue(row, "favorate_format_classes"));
                entity.setFormats(extractIntegerValue(row, "formats"));
                entity.setFavorateFormat2(extractStringValue(row, "favorate_format_second"));
                entity.setFavorateFormat3(extractStringValue(row, "favorate_format_third"));

                entity.setSquadFriendsCount(extractIntegerValue(row, "squad_friends"));

                entity.setTrainerName(extractStringValue(row, "fav_trainer"));
                entity.setTrainerClasses(extractIntegerValue(row, "fav_trainer_classes"));

                entity.setPackUtilisationCount(extractFloatValue(row, "pack_utilisation"));
                entity.setCountryPercentile(extractFloatValue(row, "percentile"));

                entity.setCitiesCount(extractIntegerValue(row, "cities"));
                entity.setFavouriteCity1(extractStringValue(row, "favorate_city"));
                entity.setFavouriteCity2(extractStringValue(row, "favorate_city_second"));
                entity.setFavouriteCity3(extractStringValue(row, "favorate_city_third"));
                entity.setCentersVisited(extractIntegerValue(row, "centers"));
                
                // Set metadata
                entity.setReportDate(currentDate);
                entity.setAthenaTaskId(athenaTaskId);
                entity.setQuerySource("UAS:achievement-showcase-report");
                entity.setProcessedAt(currentTimestamp);
                
                entities.add(entity);
                
            } catch (Exception e) {
                log.warn("ASR: Failed to convert row to entity, skipping: {}", row, e);
            }
        }
        
        log.info("ASR: Converted {} raw records to {} entities", rawData.size(), entities.size());
        return entities;
    }

    /**
     * Upsert entities in batches for efficiency (update if exists, create if not)
     */
    private List<AchievementShowcaseEntity> upsertInBatches(List<AchievementShowcaseEntity> entities) {
        final int BATCH_SIZE = 100;
        List<AchievementShowcaseEntity> allUpserted = new ArrayList<>();

        for (int i = 0; i < entities.size(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, entities.size());
            List<AchievementShowcaseEntity> batch = entities.subList(i, endIndex);

            try {
                List<AchievementShowcaseEntity> upsertedBatch = achievementShowcaseRepository.upsertAll(batch);
                allUpserted.addAll(upsertedBatch);
                log.debug("ASR: Upserted batch {}-{} successfully", i + 1, endIndex);
            } catch (Exception e) {
                log.error("ASR: Failed to upsert batch {}-{}", i + 1, endIndex, e);
            }
        }

        return allUpserted;
    }

    /**
     * Get Achievement Showcase data for a user
     */
    public List<AchievementShowcaseEntity> getDataForUser(Long userId, int limit, int offset) {
        Pageable pageable = PageRequest.of(offset / limit, limit, 
                Sort.by(Sort.Direction.DESC, "reportDate"));
        return achievementShowcaseRepository.findByUserId(userId, pageable);
    }

    /**
     * Get Achievement Showcase data for a date
     */
    public List<AchievementShowcaseEntity> getDataForDate(String reportDate, int limit, int offset) {
        Pageable pageable = PageRequest.of(offset / limit, limit, 
                Sort.by(Sort.Direction.ASC, "userId"));
        return achievementShowcaseRepository.findByReportDate(reportDate, pageable);
    }

    /**
     * Get count for a date
     */
    public long getCountForDate(String reportDate) {
        return achievementShowcaseRepository.countByReportDate(reportDate);
    }

    // Helper methods for safe data extraction
    private Long extractLongValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        if (value == null) return null;
        if (value instanceof Number) return ((Number) value).longValue();
        try {
            return Long.parseLong(value.toString());
        } catch (NumberFormatException e) {
            log.warn("ASR: Failed to parse Long value for key {}: {}", key, value);
            return null;
        }
    }

    private Integer extractIntegerValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        if (value == null) return null;
        if (value instanceof Number) return ((Number) value).intValue();
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            log.warn("ASR: Failed to parse Integer value for key {}: {}", key, value);
            return null;
        }
    }

    private Float extractFloatValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        if (value == null) return null;
        if (value instanceof Number) return ((Number) value).floatValue();
        try {
            return Float.parseFloat(value.toString());
        } catch (NumberFormatException e) {
            log.warn("ASR: Failed to parse Float value for key {}: {}", key, value);
            return null;
        }
    }

    private String extractStringValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        if (value == null) return null;
        return value.toString().trim();
    }
}
