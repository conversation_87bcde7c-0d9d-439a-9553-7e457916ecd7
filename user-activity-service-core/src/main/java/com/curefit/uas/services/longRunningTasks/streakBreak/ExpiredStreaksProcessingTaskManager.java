package com.curefit.uas.services.longRunningTasks.streakBreak;

import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.uas.repository.mongo.streaks.IStreakConfigRepository;
import com.curefit.uas.repository.mongo.streaks.IStreakRepository;
import com.curefit.uas.services.streak.StreakHelperService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@RequiredArgsConstructor
@Component
@Slf4j
public class ExpiredStreaksProcessingTaskManager {
    private volatile ExecutorService executorService;
    private final RollbarService rollbarService;
    private final IStreakRepository streakRepository;
    private final IStreakConfigRepository streakConfigRepository;
    private final StreakHelperService streakHelperService;
    
    private ExecutorService getExecutorService() {
        if (executorService == null || executorService.isShutdown() || executorService.isTerminated()) {
            synchronized (this) {
                executorService = Executors.newFixedThreadPool(3);
                log.info("ExpiredStreaksProcessingTaskManager ExecutorService created.");
            }
        }
        return executorService;
    }
    
    @PreDestroy
    public void cleanup() {
        if (executorService != null) {
            executorService.shutdown();
            log.info("ExpiredStreaksProcessingTaskManager ExecutorService shutdown.");
        }
    }
    
    public void submitExpiredStreaksProcessingTask(int pageSize, String streakResetDate) {
        ExpiredStreaksProcessingTask task = new ExpiredStreaksProcessingTask(
                pageSize, streakResetDate, rollbarService, streakRepository, streakConfigRepository, streakHelperService
        );
        getExecutorService().submit(task);
        cleanup();
    }
    
}
