package com.curefit.uas.services;

import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.uas.entities.UserActivityStatsCohortConfigEntity;
import com.curefit.uas.pojo.entries.UserActivityStatsCohortConfig;
import com.curefit.uas.repository.jpa.IUserActivityStatsCohortConfigRepository;
import com.curefit.uas.types.UserActivityType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserActivityStatsCohortConfigService extends BaseMySQLService<UserActivityStatsCohortConfigEntity, UserActivityStatsCohortConfig> {
    private final IUserActivityStatsCohortConfigRepository configRepository;

    public UserActivityStatsCohortConfigService(IUserActivityStatsCohortConfigRepository repository) {
        super(repository);
        this.configRepository = repository;
    }

//    public Optional<UserActivityStatsCohortConfig> getCohortConfigForUser(Set<String> userSegmentNames, UserActivityType activityType) {
//        return (configRepository.findTopByActivityTypeAndSegmentNameInOrderByIdDesc(activityType, userSegmentNames)).map(this::convertToEntry);
//    }

    public List<UserActivityStatsCohortConfig> getCohortConfigsForUser(Set<String> userSegmentNames, List<UserActivityType> activityTypes) {
        return (configRepository.findByActivityTypeInAndSegmentNameIn(activityTypes, userSegmentNames)).stream().map(this::convertToEntry).collect(Collectors.toList());
    }

    public List<UserActivityStatsCohortConfig> getAllActiveConfigs() {
        return (configRepository.findAllByActiveIsTrue()).stream().map(this::convertToEntry).collect(Collectors.toList());

    }
}