package com.curefit.uas.services.fitnessReportService;

import com.amazonaws.services.sns.model.PublishResult;
import com.curefit.base.enums.AppTenant;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.lock.LockService;
import com.curefit.cult.models.ClassDetails;
import com.curefit.cult.models.WorkoutDetails;
import com.curefit.gymfit.client.GymfitClient;
import com.curefit.hercules.client.HerculesService;
import com.curefit.metricservice.client.MetricClient;
import com.curefit.metricservice.models.UserMetricValue;
import com.curefit.rashi.client.RashiClient;
import com.curefit.rashi.client.UserAttributesClient;
import com.curefit.rashi.enums.UserEventType;
import com.curefit.rashi.pojo.UserEventEntry;
import com.curefit.segmentation.client.rest.SegmentationClient;
import com.curefit.segmentation.client.rest.UserSegmentClient;
import com.curefit.uas.constants.LockKey;
import com.curefit.uas.constants.RashiUserActivityEventName;
import com.curefit.uas.constants.RashiUserProfileEventNames;
import com.curefit.uas.entities.FitnessReportEntity;
import com.curefit.uas.mapper.FitnessReportMapper;
import com.curefit.uas.pojo.fitnessReport.FitnessReportEntry;
import com.curefit.uas.pojo.fitnessReport.FitnessReportHabitAndMilestonesSqsMsg;
import com.curefit.uas.pojo.fitnessReport.FitnessReportInterval;
import com.curefit.uas.pojo.fitnessReport.FitnessReportSubData;
import com.curefit.uas.publisher.FitnessReportHabitAndMilestonesPublisher;
import com.curefit.uas.repository.mongo.FitnessReportRepository;
import com.curefit.uas.services.ConfigStoreWrapperService;
import com.curefit.uas.utils.FitnessReportConstants;
import com.curefit.uas.utils.TimeUtil;
import com.curefit.ufs.services.UfsService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.simple.JSONObject;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class BaseUpdateFitnessReportService {
    final ObjectMapper objectMapper;
    final FitnessReportMapper fitnessReportMapper;
    final FitnessReportHabitAndMilestonesPublisher fitnessReportHabitAndMilestonesPublisher;
    final HerculesService herculesService;
    final ConfigStoreWrapperService configStore;
    final RollbarService rollbarService;
    final MetricClient metricClient;
    final UfsService ufsService;
    final GymfitClient gymfitClient;
    final LockService lockService;
    final UserSegmentClient userSegmentClient;
    final SegmentationClient segmentationClient;
    final UserAttributesClient userAttributesClient;
    final RashiClient rashiClient;
    final FitnessReportRepository fitnessReportRepository;
    final UpdateFitnessReportHelperService updateFitnessReportHelperService;
    
    public FitnessReportEntry processClass(Long userId, ClassDetails classDetails, WorkoutDetails workoutDetails, String ufsCompletedWodId) throws Exception {
        if (StringUtils.isNotBlank(classDetails.getWodId())) {
            if (!updateFitnessReportHelperService.isValidWodId(classDetails.getWodId())) {
                log.info("Invalid wod id {} for fitness report processing", classDetails.getWodId());
                return null;
            }
        }
        String currentWeekMonday = TimeUtil.getMondayOfRelativeWeek(classDetails.getDate(), 0);
        String currentWeekSunday = TimeUtil.getSundayOfRelativeWeek(classDetails.getDate(), 0);
        log.debug("Dheeraj Fitness Report processClass userId={} classDate={} currentWeekMonday={} currentWeekSunday={}", userId, classDetails.getDate(), currentWeekMonday, currentWeekSunday);
        FitnessReportInterval interval = FitnessReportInterval.WEEKLY;
        String lockKey = LockKey.FITNESS_REPORT_USER_INTERVAL_START_DATE.getValue() + userId + "_" + interval + "_" + currentWeekMonday;
        String lock = null;
        FitnessReportEntry processedEntry;
        try {
            lock = lockService.acquire(lockKey).get();
            List<FitnessReportEntity> previousNEntriesSortedNewToOld = updateFitnessReportHelperService.getPreviousNEntitiesSortedNewToOld(userId, currentWeekMonday, 7);
            FitnessReportEntity currentEntry = FitnessReportUtils.getMatchingEntryWithStartDate(previousNEntriesSortedNewToOld, currentWeekMonday);
            boolean isClassAlreadyLoggedInFitnessReport = currentEntry != null && CollectionUtils.isNotEmpty(currentEntry.getClassList())
                    && currentEntry.getClassList().stream().anyMatch(x -> x.toString().contains(classDetails.getId().toString()));
            
            if (isClassAlreadyLoggedInFitnessReport) {
                boolean isAiTrainerEvent = StringUtils.isNotBlank(ufsCompletedWodId);
                boolean gymfitWorkoutId = FitnessReportConstants.FITNESS_REPORT_GYMFIT_ID.equals(workoutDetails.getId());
                
                if (gymfitWorkoutId) {
                    // Doubt: How is the class id of ufs different than gym
                    // This is a delayed gym event or duplicate ufs wod completion event
                    return null;
                } else if (isAiTrainerEvent) {
                    FitnessReportSubData dataToBeProcessed = new FitnessReportSubData(currentEntry.getWorkoutCounts(), currentEntry.getBenefits(), currentEntry.getBodyParts());
                    FitnessReportSubData fitnessReportSubData = updateFitnessReportHelperService.getUFSPlanDataForFitnessReport(userId, dataToBeProcessed, classDetails);
                    currentEntry.setClassList(updateFitnessReportHelperService.getUpdatedClassListForUFSWodCompletion(currentEntry.getClassList(), classDetails, "_" + ufsCompletedWodId));
                    currentEntry.setClassCalorieMap(FitnessReportUtils.getUpdatedCalorieMapForUFSWodCompletion(currentEntry.getClassCalorieMap(), classDetails, "_" + ufsCompletedWodId));
                    currentEntry.setBenefits(fitnessReportSubData.getBenefits());
                    currentEntry.setBodyParts(fitnessReportSubData.getBodyParts());
                    currentEntry.setWorkoutCounts(fitnessReportSubData.getWorkoutCounts());
                    return fitnessReportMapper.map(fitnessReportRepository.save(currentEntry));
                } else {
                    log.debug("Duplicate event UpdateFitnessReportEvent for {} and {}", userId, classDetails.getId());
                    return fitnessReportMapper.map(fitnessReportRepository.save(updateReportData(userId, classDetails, currentEntry)));
                }
            } else {
                FitnessReportEntity reportData = generateReportData(userId, classDetails, currentEntry, previousNEntriesSortedNewToOld, workoutDetails, ufsCompletedWodId, null);
                reportData.setUserId(userId);
                reportData.setStartDate(currentWeekMonday);
                reportData.setEndDate(currentWeekSunday);
                reportData.setInterval(interval);
                if (currentEntry != null) {
//                    ObjectUtils.copyNonNullProperties(reportData, currentEntry);
                    reportData = FitnessReportUtils.partialUpdateDataAndReturnDeepCopy(currentEntry, reportData);
                }
                processedEntry = fitnessReportMapper.map(fitnessReportRepository.save(reportData));
                try {
                    updateUserYearlyFitnessRashiAttributes(userId, classDetails, workoutDetails, currentEntry, previousNEntriesSortedNewToOld);
                } catch (Exception e) {
                    rollbarService.error(e, "Error while updating user yearly fitness attributes for user: " + userId + " and for class " + classDetails.getId());
                    log.error("Error while updating user yearly fitness attributes for user: {} and for class {}", userId, classDetails.getId(), e);
                }
                try {
                    publishFitnessReportUpdateRashiEvent(userId, reportData.getClassesAttended() != null ?
                            reportData.getClassesAttended() : 0, currentWeekMonday, reportData.getStartDate(), reportData.getEndDate());
                } catch (Exception e) {
                    log.error("Error while updating user attributes for fitness report for user: {} and for class {}", userId, classDetails.getId(), e);
                }
                FitnessReportHabitAndMilestonesSqsMsg fitnessReportHabitAndMilestonesSqsMsg = new FitnessReportHabitAndMilestonesSqsMsg(userId, currentWeekMonday, currentWeekSunday, classDetails, reportData.getStartDate(), workoutDetails);
                fitnessReportHabitAndMilestonesPublisher.pushFitnessReportHabitAndMilestonesSqsMsg(fitnessReportHabitAndMilestonesSqsMsg);
                if (TimeUtil.isBeforeCurrentWeek(classDetails.getDate())) {
                    String userLock = null, userLockKey = LockKey.FITNESS_REPORT_USER.getValue() + userId;
                    try {
                        userLock = lockService.acquire(userLockKey).get();
                        List<FitnessReportEntity> nextEntries = updateFitnessReportHelperService.getNextEntries(userId, currentWeekMonday);
                        updateFitnessReportHelperService.propagateClassBackfillToNextWeek(reportData, nextEntries, userId);
                    } catch (Exception e) {
                        log.error("Backfill of FitnessReport for user {}. class {}, week {} failed with error", userId, classDetails.getId(), currentWeekMonday, e);
                    } finally {
                        if (StringUtils.isNotBlank(userLock)) {
                            lockService.release(userLockKey, userLock);
                        }
                    }
                }
            }
            return processedEntry;
        } catch (Exception e) {
            log.error("Update of FitnessReport for class {} failed with error", classDetails.getId(), e);
//            if (StringUtils.isNotBlank(lock)) {
//                throw e;
//            }
//            return null;
            throw e;
        } finally {
            if (StringUtils.isNotBlank(lock)) {
                lockService.release(lockKey, lock);
            }
        }
    }
    
    FitnessReportEntity updateReportData(Long userId, ClassDetails classDetails, FitnessReportEntity currentEntry) {
        Integer lastUpdatedClassCalorieValue = currentEntry.getClassCalorieMap().get(classDetails.getId().toString());
        if (lastUpdatedClassCalorieValue == null) {
            log.debug("Past data is not present for {} for {}", userId, classDetails.getId());
            return currentEntry;
        }
        float metValue = updateFitnessReportHelperService.safeGetMetValue(classDetails.getWodId());
        Integer currentCaloriesBurned = classDetails.getCalories() != null ? classDetails.getCalories() : FitnessReportUtils.getCaloriesBurned(currentEntry.getWeight(), metValue, classDetails.getDurationH());
        if (currentCaloriesBurned.equals(lastUpdatedClassCalorieValue)) {
            log.debug("No change in FitnessReport for {} for {}", userId, classDetails.getId());
            return currentEntry;
        }
        Integer caloriesBurned = currentEntry.getCaloriesBurned() - lastUpdatedClassCalorieValue + currentCaloriesBurned;
        currentEntry.getClassCalorieMap().put(classDetails.getId().toString(), currentCaloriesBurned);
        currentEntry.setCaloriesBurned(caloriesBurned);
        return currentEntry;
    }
    
    FitnessReportEntity generateReportData(Long userId, @NotNull ClassDetails classDetails, FitnessReportEntity currentEntry, List<FitnessReportEntity> prev7EntriesSortedNewToOld,
                                                  WorkoutDetails workoutDetails, String ufsCompletedWodId, FitnessReportEntity lastEntryInput) throws Exception {
        if (classDetails == null) {
            throw new NullPointerException("Class Details cannot be empty");
        }
        String previousWeekMonday = TimeUtil.getMondayOfPreviousWeek(classDetails.getDate());
        FitnessReportEntity previousWeekEntry = FitnessReportUtils.getMatchingEntryWithStartDate(prev7EntriesSortedNewToOld, previousWeekMonday);
        if (lastEntryInput == null) {
            lastEntryInput = FitnessReportUtils.getLatestEntityBeforeOrOnGivenDate(prev7EntriesSortedNewToOld, previousWeekMonday);
        }
        int currentClassesAttended = (currentEntry != null && currentEntry.getClassesAttended() != null ? currentEntry.getClassesAttended() : 0) + 1;
        int previousMaxClassesAttended = lastEntryInput != null && lastEntryInput.getMaxClassesAttended() != null ? lastEntryInput.getMaxClassesAttended() : 0;
        UserMetricValue weightMetric = updateFitnessReportHelperService.getMetricValue(userId, FitnessReportConstants.METRIC_SERVICE_CONSTANTS_WEIGHT_ID);
        UserMetricValue heightMetric = updateFitnessReportHelperService.getMetricValue(userId, FitnessReportConstants.METRIC_SERVICE_CONSTANTS_HEIGHT_ID);
        Float weight = weightMetric != null ? (Math.round(weightMetric.getValue().floatValue() * 100f) / 100f) : null;
        Float height = heightMetric != null ? (Math.round(heightMetric.getValue().floatValue() * 100f) / 100f) : null;
        Long weightUpdatedEpoch = weightMetric != null && weightMetric.getMetricDate() != null ? weightMetric.getMetricDate().getTime() : null;
        float metValue = updateFitnessReportHelperService.safeGetMetValue(classDetails.getWodId());
        int currentCaloriesBurned = classDetails.getCalories() != null ? classDetails.getCalories() : FitnessReportUtils.getCaloriesBurned(weight, metValue, classDetails.getDurationH());
        Float bmi = FitnessReportUtils.getBMI(weight, height);
        int caloriesBurned = (currentEntry != null ? currentEntry.getCaloriesBurned() : 0) + currentCaloriesBurned;
        List<Object> classList =  currentEntry != null && currentEntry.getClassList() != null ? currentEntry.getClassList() : new ArrayList<>();
        Map<String, Integer> classCalorieMap = currentEntry != null && currentEntry.getClassCalorieMap() != null ? currentEntry.getClassCalorieMap() : new HashMap();
        if (classDetails.getId() != null) {
            classList.add(classDetails.getId());
            classCalorieMap.put(classDetails.getId().toString(), currentCaloriesBurned);
        }
        FitnessReportSubData fitnessReportSubData =  updateFitnessReportHelperService.getDataForFitnessReport(userId, workoutDetails, currentEntry, classDetails, ufsCompletedWodId);;
        if (StringUtils.isNotBlank(ufsCompletedWodId)) {
            classList = updateFitnessReportHelperService.getUpdatedClassListForUFSWodCompletion(classList, classDetails, "_" + ufsCompletedWodId);
            classCalorieMap = FitnessReportUtils.getUpdatedCalorieMapForUFSWodCompletion(classCalorieMap, classDetails, "_" + ufsCompletedWodId);
        }
        int streak = (previousWeekEntry != null ? previousWeekEntry.getStreak() : 0) + 1;
        Integer totalClassesAttended = 1 + (currentEntry != null && currentEntry.getTotalClassesAttended() != null
                ? currentEntry.getTotalClassesAttended() : (lastEntryInput != null && lastEntryInput.getTotalClassesAttended() != null
                ? lastEntryInput.getTotalClassesAttended() : 0));
        FitnessReportEntity fitnessReportEntity = new FitnessReportEntity();
        if (currentEntry != null) {
            fitnessReportEntity = currentEntry;
        }
        fitnessReportEntity.setClassesAttended(currentClassesAttended);
        fitnessReportEntity.setMaxClassesAttended(Math.max(currentClassesAttended, previousMaxClassesAttended));
        fitnessReportEntity.setCaloriesBurned(Math.min(caloriesBurned, FitnessReportConstants.FITNESS_REPORT_VALIDATION_CONSTANTS_MAX_CALORIES_BURNED));
        fitnessReportEntity.setStreak(streak);
        fitnessReportEntity.setTimezone(classDetails.getTimezone());
        fitnessReportEntity.setWeight(weight);
        fitnessReportEntity.setWeightUpdatedEpoch(weightUpdatedEpoch);
        fitnessReportEntity.setHeight(height);
        fitnessReportEntity.setBmi(bmi);
        fitnessReportEntity.setClassList(classList);
        fitnessReportEntity.setClassCalorieMap(classCalorieMap);
        fitnessReportEntity.setWorkoutCounts(fitnessReportSubData.getWorkoutCounts());
        fitnessReportEntity.setBenefits(fitnessReportSubData.getBenefits());
        fitnessReportEntity.setBodyParts(fitnessReportSubData.getBodyParts());
        fitnessReportEntity.setTotalClassesAttended(totalClassesAttended);
        fitnessReportEntity.setMaxWeeklyStreak(lastEntryInput != null && lastEntryInput.getMaxWeeklyStreak() != null ? Math.max(streak, lastEntryInput.getMaxWeeklyStreak()) : streak);
        return fitnessReportEntity;
    }
    
    void publishFitnessReportUpdateRashiEvent(Long userId, int weeklyActiveDays, String updatedDate, String startDate, String endDate) {
        JSONObject body = new JSONObject();
        body.put("weekly_active_days", weeklyActiveDays);
        UserEventEntry rashiUserProfileEventForActiveDays = new UserEventEntry();
        rashiUserProfileEventForActiveDays.setUserId((long) userId);
        rashiUserProfileEventForActiveDays.setType(UserEventType.USER_PROFILE_EVENT);
        rashiUserProfileEventForActiveDays.setEventName(RashiUserProfileEventNames.FITNESS_REPORT_WEEKLY_ACTIVE_DAYS_UPDATED.toString());
        rashiUserProfileEventForActiveDays.setEventTime(new Date());
        rashiUserProfileEventForActiveDays.setBody(body);
        rashiUserProfileEventForActiveDays.setAppTenant(AppTenant.CUREFIT);
        
        try {
            PublishResult publishResult = rashiClient.publishUserEvent(rashiUserProfileEventForActiveDays, AppTenant.CUREFIT, String.valueOf(userId));
            log.info("Rashi weekly active days profile event result = {}", publishResult.getMessageId());
        } catch (Exception e) {
            log.error("Cannot publish weekly active days profile event to rashi with error ", e);
        }
        JSONObject rashiUserProfileEventForActiveDaysUpdateDateBody = new JSONObject();
        rashiUserProfileEventForActiveDaysUpdateDateBody.put("weekly_active_days_updated_date", updatedDate);
        UserEventEntry rashiUserProfileEventForActiveDaysUpdateDate = new UserEventEntry();
        rashiUserProfileEventForActiveDaysUpdateDate.setUserId((long) userId);
        rashiUserProfileEventForActiveDaysUpdateDate.setType(UserEventType.USER_PROFILE_EVENT);
        rashiUserProfileEventForActiveDaysUpdateDate.setEventName(RashiUserProfileEventNames.FITNESS_REPORT_WEEKLY_ACTIVE_DAYS_UPDATED.toString());
        rashiUserProfileEventForActiveDaysUpdateDate.setEventTime(new Date());
        rashiUserProfileEventForActiveDaysUpdateDate.setBody(rashiUserProfileEventForActiveDaysUpdateDateBody);
        rashiUserProfileEventForActiveDaysUpdateDate.setAppTenant(AppTenant.CUREFIT);
        
        try {
            PublishResult publishResult2 = rashiClient.publishUserEvent(rashiUserProfileEventForActiveDaysUpdateDate, AppTenant.CUREFIT, String.valueOf(userId));
            log.info("Rashi weekly active days updated date profile event result = {}", publishResult2.getMessageId());
        } catch (Exception e) {
            log.error("Cannot publish weekly active days updated date profile event to Rashi with error ", e);
        }
        
        if (weeklyActiveDays == 1) {
            JSONObject rashiEventBody = new JSONObject();
            rashiEventBody.put("startDate", startDate);
            rashiEventBody.put("endDate", endDate);
            UserEventEntry rashiEvent = new UserEventEntry();
            rashiEvent.setUserId((long) userId);
            rashiEvent.setType(UserEventType.USER_ACTIVITY_EVENT);
            rashiEvent.setEventName(RashiUserActivityEventName.FITNESS_WEEKLY_REPORT_GENERATED.toString());
            rashiEvent.setEventTime(new Date());
            rashiEvent.setBody(rashiEventBody);
            rashiEvent.setAppTenant(AppTenant.CUREFIT);
            try {
                rashiClient.publishUserEvent(rashiEvent, AppTenant.CUREFIT, String.valueOf(userId));
            } catch (Exception e) {
                log.error("publishFitnessReportUpdateRashiEvent:Couldn't send rashi event for weekly report for user = {}", userId);
            }
        }
    }
    
    //ToDo:: Revisit and check if it is being used or not
    private void updateUserYearlyFitnessRashiAttributes(Long userId, ClassDetails classDetails,
                                                        WorkoutDetails workoutDetails, FitnessReportEntity reportData, List<FitnessReportEntity> lastSevenEntries) {
        try {
            // Get the current moment in the specified time zone
            ZonedDateTime currentMoment = ZonedDateTime.now(FitnessReportConstants.INDIA_ZONE_ID);
            // Format the current year
            String currentYear = currentMoment.format(DateTimeFormatter.ofPattern("yyyy"));
            // Format the current time in HH:mm:ss
            String currentTime = currentMoment.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
            // Get the current month as a string (e.g., "January")
            String currentMonth = currentMoment.getMonth().getDisplayName(TextStyle.FULL, Locale.ENGLISH);
            // Get the start date of the current month
            LocalDate currentMonthStartDate = currentMoment.toLocalDate().withDayOfMonth(1);
            // Get the Monday of the current ISO week
            // ToDo:: figure out the right structure
            LocalDate currentWeekMonday = currentMoment.toLocalDate().with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
            
            Map<String, Object> userYearlyFitnessAttributeConfig = configStore.getUserYearlyFitnessAttributeConfig();
            List<String> blackListedWorkoutPatterns = new ArrayList<>();
            if (userYearlyFitnessAttributeConfig.get("BLACKLISTED_WORKOUT_PATTERNS") instanceof List) {
                blackListedWorkoutPatterns = (List<String>) userYearlyFitnessAttributeConfig.get("BLACKLISTED_WORKOUT_PATTERNS");
            } else {
                log.error("blackListedWorkoutPatterns from config is not of the correct type but instead of type = {}", userYearlyFitnessAttributeConfig.get("BLACKLISTED_WORKOUT_PATTERNS").getClass().getName());
            }
            if (workoutDetails != null && CollectionUtils.isNotEmpty(blackListedWorkoutPatterns)
                    && blackListedWorkoutPatterns.stream().anyMatch(x -> workoutDetails.getName().toLowerCase().contains(x))) {
                log.debug("updateUserYearlyFitnessRashiAttributes exit");
                return;
            }
            // Note:: segment is expired so currently not being stored
//            Object FITNESS_ATTRIBUTE_SEGMENT_ID = userYearlyFitnessAttributeConfig.get("CRM_SEGMENT_ID");
//            if (FITNESS_ATTRIBUTE_SEGMENT_ID instanceof Integer) {
//                boolean isUserInSegment = segmentationClient.checkUserExistenceInSegment(((Integer) FITNESS_ATTRIBUTE_SEGMENT_ID).longValue(), (long) userId).getUserExists();
//                if (isUserInSegment) {
//                    Map<String, String> attributes = (Map<String, String>) userYearlyFitnessAttributeConfig.get("ATTRIBUTES");
//
//                    String ACTIVE_DAYS_ATTRIBUTE_KEY = attributes.get("ACTIVE_DAYS") + "_" + currentYear;
//                    String ACTIVITIES_DONE_ATTRIBUTE_KEY = attributes.get("ACTIVITIES_DONE") + "_" + currentYear;
//                    String WORKOUT_TIME_SPENT_ATTRIBUTE_KEY = attributes.get("WORKOUT_TIME_SPENT") + "_" + currentYear;
//                    String CALORIE_COUNT_ATTRIBUTE_KEY = attributes.get("CALORIE_COUNT") + "_" + currentYear;
//                    String UNIQUE_WORKOUT_FORMATS_ATTRIBUTE_KEY = attributes.get("UNIQUE_WORKOUT_FORMATS") + "_" + currentYear;
//                    String UNIQUE_WORKOUT_FORMATS_COUNT_ATTRIBUTE_KEY = attributes.get("UNIQUE_WORKOUT_FORMATS_COUNT") + "_" + currentYear;
//                    String LONGEST_WEEKLY_STREAK_ATTRIBUTE_KEY = attributes.get("LONGEST_WEEKLY_STREAK") + "_" + currentYear;
//                    String MORNING_SLOT_COUNT_ATTRIBUTE_KEY = attributes.get("MORNING_SLOT_COUNT") + "_" + currentYear;
//                    String EVENING_SLOT_COUNT_ATTRIBUTE_KEY = attributes.get("EVENING_SLOT_COUNT") + "_" + currentYear;
//                    String FAVOURITE_WORKOUT_SLOT_ATTRIBUTE_KEY = attributes.get("FAVOURITE_WORKOUT_SLOT") + "_" + currentYear;
//                    String YEARLY_MEMBERSHIP_DAYS_ATTRIBUTE_KEY = attributes.get("YEARLY_MEMBERSHIP_DAYS") + "_" + currentYear;
//                    String WORKOUTS_PER_MEMBERSHIP_MONTH_ATTRIBUTE_KEY = attributes.get("WORKOUTS_PER_MEMBERSHIP_MONTH") + "_" + currentYear;
//                    String FITNESS_LEVEL_ATTRIBUTE_KEY = attributes.get("FITNESS_LEVEL_ATTRIBUTE") + "_" + currentYear;
//                    String WEEKEND_ACTIVITY_COUNT_ATTRIBUTE_KEY = attributes.get("WEEKEND_ACTIVITY_COUNT") + "_" + currentYear;
//                    String MOST_ACTIVE_MONTH_ATTRIBUTE_KEY = attributes.get("MOST_ACTIVE_MONTH") + "_" + currentYear;
//                    String MOST_ACTIVE_MONTH_ACTIVITY_COUNT_ATTRIBUTE_KEY = attributes.get("MOST_ACTIVE_MONTH_ACTIVITY_COUNT") + "_" + currentYear;
//                    String FITNESS_COMEBACKS_ATTRIBUTE_KEY = attributes.get("FITNESS_COMEBACKS") + "_" + currentYear;
//                    String LAST_WORKOUT_DONE_AT_ATTRIBUTE_KEY = attributes.get("LAST_WORKOUT_DONE_AT") + "_" + currentYear;
//                    String FAVOURITE_FORMAT_ATTRIBUTE_KEY = attributes.get("FAVOURITE_FORMAT") + "_" + currentYear;
//                    String FAVOURITE_FORMAT_CLASSES_ATTRIBUTE_KEY = attributes.get("FAVOURITE_FORMAT_CLASSES") + "_" + currentYear;
//                    String GX_CLASSES_ATTRIBUTE_KEY = attributes.get("GX_CLASSES") + "_" + currentYear;
//                    String GYM_CLASSES_ATTRIBUTE_KEY = attributes.get("GYM_CLASSES") + "_" + currentYear;
//                    String LIVE_CLASSES_ATTRIBUTE_KEY = attributes.get("LIVE_CLASSES") + "_" + currentYear;
//                    String PLAY_CLASSES_ATTRIBUTE_KEY = attributes.get("PLAY_CLASSES") + "_" + currentYear;
//                    String DIY_CLASSES_ATTRIBUTE_KEY = attributes.get("DIY_CLASSES") + "_" + currentYear;
//
//                    Map<String, String> ALLOWED_WORKOUT_FORMATS = (Map<String, String>) userYearlyFitnessAttributeConfig.get("WORKOUT_FORMATS");
//                    UserAttributesResponse userFitnessAttributes  = userAttributesClient.getBulkAttributes((long) userId, List.of(ACTIVE_DAYS_ATTRIBUTE_KEY, ACTIVITIES_DONE_ATTRIBUTE_KEY, WORKOUT_TIME_SPENT_ATTRIBUTE_KEY,
//                            CALORIE_COUNT_ATTRIBUTE_KEY, UNIQUE_WORKOUT_FORMATS_ATTRIBUTE_KEY, UNIQUE_WORKOUT_FORMATS_COUNT_ATTRIBUTE_KEY, LONGEST_WEEKLY_STREAK_ATTRIBUTE_KEY,
//                            MORNING_SLOT_COUNT_ATTRIBUTE_KEY, EVENING_SLOT_COUNT_ATTRIBUTE_KEY, FAVOURITE_WORKOUT_SLOT_ATTRIBUTE_KEY, YEARLY_MEMBERSHIP_DAYS_ATTRIBUTE_KEY,
//                            WORKOUTS_PER_MEMBERSHIP_MONTH_ATTRIBUTE_KEY, FITNESS_LEVEL_ATTRIBUTE_KEY, WEEKEND_ACTIVITY_COUNT_ATTRIBUTE_KEY, MOST_ACTIVE_MONTH_ATTRIBUTE_KEY,
//                            MOST_ACTIVE_MONTH_ACTIVITY_COUNT_ATTRIBUTE_KEY, FITNESS_COMEBACKS_ATTRIBUTE_KEY, LAST_WORKOUT_DONE_AT_ATTRIBUTE_KEY, FAVOURITE_FORMAT_ATTRIBUTE_KEY,
//                            FAVOURITE_FORMAT_CLASSES_ATTRIBUTE_KEY, GX_CLASSES_ATTRIBUTE_KEY, GYM_CLASSES_ATTRIBUTE_KEY, LIVE_CLASSES_ATTRIBUTE_KEY, PLAY_CLASSES_ATTRIBUTE_KEY,
//                            DIY_CLASSES_ATTRIBUTE_KEY), AppTenant.CUREFIT, CircuitBreakerPropertiesProfiles.FAST_RECOVERY_HIGH_TOLERANCE);
//
//                    List<UserEventEntry> userEvents = new ArrayList<>();
//
//                    /*
//                     if (userFitnessAttributes && userFitnessAttributes.attributes) {
//                            let ACTIVE_DAYS_ATTRIBUTE_VALUE = userFitnessAttributes.attributes[ACTIVE_DAYS_ATTRIBUTE_KEY]
//                            let ACTIVITIES_DONE_ATTRIBUTE_VALUE = userFitnessAttributes.attributes[ACTIVITIES_DONE_ATTRIBUTE_KEY]
//                            let WORKOUT_TIME_SPENT_ATTRIBUTE_VALUE = userFitnessAttributes.attributes[WORKOUT_TIME_SPENT_ATTRIBUTE_KEY]
//                            let CALORIE_COUNT_ATTRIBUTE_VALUE = userFitnessAttributes.attributes[CALORIE_COUNT_ATTRIBUTE_KEY]
//                            let UNIQUE_WORKOUT_FORMATS_ATTRIBUTE_VALUE = userFitnessAttributes.attributes[UNIQUE_WORKOUT_FORMATS_ATTRIBUTE_KEY]
//                            let UNIQUE_WORKOUT_FORMATS_COUNT_ATTRIBUTE_VALUE = userFitnessAttributes.attributes[UNIQUE_WORKOUT_FORMATS_COUNT_ATTRIBUTE_KEY]
//                            let LONGEST_WEEKLY_STREAK_ATTRIBUTE_VALUE = userFitnessAttributes.attributes[LONGEST_WEEKLY_STREAK_ATTRIBUTE_KEY]
//                            let MORNING_SLOT_COUNT_ATTRIBUTE_VALUE = userFitnessAttributes.attributes[MORNING_SLOT_COUNT_ATTRIBUTE_KEY]
//                            let EVENING_SLOT_COUNT_ATTRIBUTE_VALUE = userFitnessAttributes.attributes[EVENING_SLOT_COUNT_ATTRIBUTE_KEY]
//                            let FAVOURITE_WORKOUT_SLOT_ATTRIBUTE_VALUE = userFitnessAttributes.attributes[FAVOURITE_WORKOUT_SLOT_ATTRIBUTE_KEY]
//                            let YEARLY_MEMBERSHIP_DAYS_ATTRIBUTE_VALUE = userFitnessAttributes.attributes[YEARLY_MEMBERSHIP_DAYS_ATTRIBUTE_KEY]
//                            let WORKOUTS_PER_MEMBERSHIP_MONTH_ATTRIBUTE_VALUE = userFitnessAttributes.attributes[WORKOUTS_PER_MEMBERSHIP_MONTH_ATTRIBUTE_KEY]
//                            let FITNESS_LEVEL_ATTRIBUTE_VALUE = userFitnessAttributes.attributes[FITNESS_LEVEL_ATTRIBUTE_KEY] ?? 0
//                            let WEEKEND_ACTIVITY_COUNT_ATTRIBUTE_VALUE = userFitnessAttributes.attributes[WEEKEND_ACTIVITY_COUNT_ATTRIBUTE_KEY]
//                            let MOST_ACTIVE_MONTH_ATTRIBUTE_VALUE = userFitnessAttributes.attributes[MOST_ACTIVE_MONTH_ATTRIBUTE_KEY]
//                            let MOST_ACTIVE_MONTH_ACTIVITY_COUNT_ATTRIBUTE_VALUE = userFitnessAttributes.attributes[MOST_ACTIVE_MONTH_ACTIVITY_COUNT_ATTRIBUTE_KEY]
//                            let FITNESS_COMEBACKS_ATTRIBUTE_VALUE = userFitnessAttributes.attributes[FITNESS_COMEBACKS_ATTRIBUTE_KEY]
//                            let LAST_WORKOUT_DONE_AT_ATTRIBUTE_VALUE = userFitnessAttributes.attributes[LAST_WORKOUT_DONE_AT_ATTRIBUTE_KEY]
//                            let FAVOURITE_FORMAT_ATTRIBUTE_VALUE = userFitnessAttributes.attributes[FAVOURITE_FORMAT_ATTRIBUTE_KEY]
//                            let FAVOURITE_FORMAT_CLASSES_ATTRIBUTE_VALUE = userFitnessAttributes.attributes[FAVOURITE_FORMAT_CLASSES_ATTRIBUTE_KEY]
//                            let GX_CLASSES_ATTRIBUTE_VALUE =  userFitnessAttributes.attributes[GX_CLASSES_ATTRIBUTE_KEY]
//                            let GYM_CLASSES_ATTRIBUTE_VALUE = userFitnessAttributes.attributes[GYM_CLASSES_ATTRIBUTE_KEY]
//                            let LIVE_CLASSES_ATTRIBUTE_VALUE = userFitnessAttributes.attributes[LIVE_CLASSES_ATTRIBUTE_KEY]
//                            let PLAY_CLASSES_ATTRIBUTE_VALUE = userFitnessAttributes.attributes[PLAY_CLASSES_ATTRIBUTE_KEY]
//                            let DIY_CLASSES_ATTRIBUTE_VALUE = userFitnessAttributes.attributes[DIY_CLASSES_ATTRIBUTE_KEY]
//
//
//                            let LAST_WORKOUT_DONE_AT = LAST_WORKOUT_DONE_AT_ATTRIBUTE_VALUE
//
//                            if(!_.isNil(ACTIVE_DAYS_ATTRIBUTE_VALUE) && !_.isNil(LAST_WORKOUT_DONE_AT_ATTRIBUTE_VALUE)) {
//                                let activeDays = parseInt(ACTIVE_DAYS_ATTRIBUTE_VALUE), lastWorkoutDoneAt = LAST_WORKOUT_DONE_AT_ATTRIBUTE_VALUE
//                                if(backfillData) {
//                                    if(backfillData?.activeDays && backfillData?.lastWorkoutDoneAt) {
//                                        activeDays += backfillData?.activeDays;
//                                        lastWorkoutDoneAt = backfillData?.lastWorkoutDoneAt
//                                    }
//                                }
//                                else if(currentMoment.diff(moment(new Date(LAST_WORKOUT_DONE_AT), GlobalConstants.INDIA_TIMEZONE), 'days') >= 1) {
//                                    lastWorkoutDoneAt = moment(new Date(), GlobalConstants.INDIA_TIMEZONE).format("YYYY-MM-DD")
//                                    activeDays++;
//                                }
//                                if(activeDays != parseInt(ACTIVE_DAYS_ATTRIBUTE_VALUE) && lastWorkoutDoneAt != LAST_WORKOUT_DONE_AT) {
//                                    ACTIVE_DAYS_ATTRIBUTE_VALUE = activeDays
//                                    LAST_WORKOUT_DONE_AT_ATTRIBUTE_VALUE = lastWorkoutDoneAt
//                                    const activeDaysUserEvent = FitnessReportHelperService.getUserEvent(ACTIVE_DAYS_ATTRIBUTE_KEY, ACTIVE_DAYS_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                    userEvents.push(activeDaysUserEvent);
//                                    const lastWorkoutAtEvent = FitnessReportHelperService.getUserEvent(LAST_WORKOUT_DONE_AT_ATTRIBUTE_KEY, LAST_WORKOUT_DONE_AT_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                    userEvents.push(lastWorkoutAtEvent);
//                                }
//                                LogUtil.debug(`updateUserYearlyFitnessRashiAttributes active days - ${ACTIVE_DAYS_ATTRIBUTE_VALUE} - ${LAST_WORKOUT_DONE_AT_ATTRIBUTE_VALUE}`);
//                            }
//                            if(!_.isNil(ACTIVITIES_DONE_ATTRIBUTE_VALUE)) { // Done
//                                LogUtil.debug(`updateUserYearlyFitnessRashiAttributes pre active done - ${ACTIVITIES_DONE_ATTRIBUTE_VALUE} - ${parseInt(ACTIVITIES_DONE_ATTRIBUTE_VALUE)} - ${!_.isNil(backfillData) && !_.isNil(backfillData?.activitiesDone)}`);
//                                ACTIVITIES_DONE_ATTRIBUTE_VALUE = parseInt(parseInt(ACTIVITIES_DONE_ATTRIBUTE_VALUE) + ((!_.isNil(backfillData) && !_.isNil(backfillData?.activitiesDone)) ? backfillData?.activitiesDone :  1))
//                                const activitiesDoneUserEvent = FitnessReportHelperService.getUserEvent(ACTIVITIES_DONE_ATTRIBUTE_KEY, ACTIVITIES_DONE_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                userEvents.push(activitiesDoneUserEvent);
//                                LogUtil.debug(`updateUserYearlyFitnessRashiAttributes active done - ${ACTIVITIES_DONE_ATTRIBUTE_VALUE}`);
//                            }
//                            if(!_.isNil(WORKOUT_TIME_SPENT_ATTRIBUTE_VALUE)) { // Done
//                                WORKOUT_TIME_SPENT_ATTRIBUTE_VALUE = parseInt(parseInt(WORKOUT_TIME_SPENT_ATTRIBUTE_VALUE) + ((!_.isNil(backfillData) && !_.isNil(backfillData?.workoutTimeSpent)) ? backfillData?.workoutTimeSpent : (classDetails?.durationH ? classDetails?.durationH * 60 : classDetails?.durationMinutes ?? 0)))
//                                const workoutTimeSpentUserEvent = FitnessReportHelperService.getUserEvent(WORKOUT_TIME_SPENT_ATTRIBUTE_KEY, WORKOUT_TIME_SPENT_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                userEvents.push(workoutTimeSpentUserEvent);
//                                LogUtil.debug(`updateUserYearlyFitnessRashiAttributes workout time spent - ${WORKOUT_TIME_SPENT_ATTRIBUTE_VALUE}`);
//                            }
//                            if(!_.isNil(CALORIE_COUNT_ATTRIBUTE_VALUE)) { // Done
//                                let currentCaloriesBurned = 0
//                                if(!_.isNil(backfillData) && !_.isNil(backfillData?.calorieCount)) {
//                                    currentCaloriesBurned = backfillData?.calorieCount
//                                }
//                                else {
//                                    let classCalorieMap: { [key: string]: any } = (!!reportData && reportData.classCalorieMap) ? reportData.classCalorieMap : {};
//                                    const classCalorieKey = Object.keys(classCalorieMap).find((k) => k.includes(classDetails.id));
//                                    currentCaloriesBurned = classCalorieKey ? parseInt(classCalorieMap[classCalorieKey] ?? 0) : undefined;
//                                }
//                                if (currentCaloriesBurned && currentCaloriesBurned > 0) {
//                                    CALORIE_COUNT_ATTRIBUTE_VALUE = parseInt(CALORIE_COUNT_ATTRIBUTE_VALUE) + currentCaloriesBurned
//                                    const calorieCountUserEvent = FitnessReportHelperService.getUserEvent(CALORIE_COUNT_ATTRIBUTE_KEY, CALORIE_COUNT_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                    userEvents.push(calorieCountUserEvent);
//                                }
//                                LogUtil.debug(`updateUserYearlyFitnessRashiAttributes workout time spent - ${CALORIE_COUNT_ATTRIBUTE_VALUE}`);
//                            }
//                            if(!_.isNil(UNIQUE_WORKOUT_FORMATS_ATTRIBUTE_VALUE)) { // Done
//                                UNIQUE_WORKOUT_FORMATS_COUNT_ATTRIBUTE_VALUE = parseInt(UNIQUE_WORKOUT_FORMATS_COUNT_ATTRIBUTE_VALUE)
//                                let workoutFormatCount = UNIQUE_WORKOUT_FORMATS_COUNT_ATTRIBUTE_VALUE
//                                let workoutFormats = UNIQUE_WORKOUT_FORMATS_ATTRIBUTE_VALUE
//                                if(!_.isNil(backfillData) && !_.isNil(backfillData?.workoutFormats) && backfillData?.workoutFormats.length) {
//                                    for(let workout of backfillData?.workoutFormats) {
//                                        const lowercaseSearchWorkoutName = workout.toLowerCase();
//                                        let foundWorkout = Object.keys(ALLOWED_WORKOUT_FORMATS).find(key => lowercaseSearchWorkoutName.includes(key.toLowerCase()));
//                                        foundWorkout = foundWorkout ? ALLOWED_WORKOUT_FORMATS[foundWorkout] : undefined;
//                                        if(foundWorkout && !workoutFormats.toLowerCase().includes(foundWorkout.toLowerCase())) {
//                                            workoutFormats += "_" + foundWorkout
//                                            workoutFormatCount ++;
//                                        }
//                                        if(foundWorkout) {
//                                            // Computing format level count for baxckfill
//                                            let FORMAT_CLASS_COUNT_ATTRIBUTE_KEY = "yearly_format_count_"
//                                            FORMAT_CLASS_COUNT_ATTRIBUTE_KEY = FORMAT_CLASS_COUNT_ATTRIBUTE_KEY
//                                                + (foundWorkout.replace(/\s/g, "").toLowerCase().includes("gym") ? "gym" : foundWorkout.replace(/\s/g, "").toLowerCase())
//                                                    + "_" + currentYear
//
//                                            const [ fitnessAttr ] = await Promise.all([
//                                                await userAttributeClient.getCachedUserAttributes(userId, FORMAT_CLASS_COUNT_ATTRIBUTE_KEY)
//                                            ])
//                                            let FORMAT_COUNT_CLASS_ATTRIBUTE_VALUE = null
//                                            if (fitnessAttr && fitnessAttr.attributes && fitnessAttr.attributes[FORMAT_CLASS_COUNT_ATTRIBUTE_KEY]) {
//                                                FORMAT_COUNT_CLASS_ATTRIBUTE_VALUE = parseInt(fitnessAttr.attributes[FORMAT_CLASS_COUNT_ATTRIBUTE_KEY])
//                                            }
//                                            if(!_.isNil(FORMAT_COUNT_CLASS_ATTRIBUTE_VALUE)) {
//                                                FORMAT_COUNT_CLASS_ATTRIBUTE_VALUE = parseInt(FORMAT_COUNT_CLASS_ATTRIBUTE_VALUE) + 1
//                                                const formatCountUserEvent = FitnessReportHelperService.getUserEvent(FORMAT_CLASS_COUNT_ATTRIBUTE_KEY, FORMAT_COUNT_CLASS_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                                userEvents.push(formatCountUserEvent);
//                                                LogUtil.debug(`updateUserYearlyFitnessRashiAttributes formats count - ${FORMAT_CLASS_COUNT_ATTRIBUTE_KEY} - ${FORMAT_COUNT_CLASS_ATTRIBUTE_VALUE}`);
//                                            }
//                                        }
//                                    }
//                                }
//                                else {
//                                    const lowercaseSearchWorkoutName = workoutDetails.name.toLowerCase();
//                                    const lowercaseSearchWorkoutId = workoutDetails.id ? (workoutDetails.id).toString().toLowerCase(): "";
//                                    let foundWorkout = Object.keys(ALLOWED_WORKOUT_FORMATS).find(key => lowercaseSearchWorkoutName.includes(key.toLowerCase())
//                                        || lowercaseSearchWorkoutId.includes(key.toLowerCase()));
//                                    foundWorkout = foundWorkout ? ALLOWED_WORKOUT_FORMATS[foundWorkout] : undefined;
//                                    if(foundWorkout && !workoutFormats.toLowerCase().includes(foundWorkout.toLowerCase())) {
//                                        if(typeof foundWorkout === "string") {
//                                            workoutFormats += "_" + foundWorkout
//                                            workoutFormatCount ++;
//                                        }
//                                        // else {
//                                        //     let foundSubWorkout = Object.keys(foundWorkout).find(key => lowercaseSearchWorkoutName.includes(key.toLowerCase())
//                                        //         || lowercaseSearchWorkoutId.includes(key.toLowerCase()));
//                                        //     if(foundSubWorkout && !workoutFormats.toLowerCase().includes(foundSubWorkout.toLowerCase())) {
//                                        //         workoutFormats += "_" + foundWorkout
//                                        //         workoutFormatCount ++;
//                                        //     }
//                                        // }
//                                    }
//                                    if(foundWorkout) {
//                                        // Computing format level count
//                                        try {
//                                            let FORMAT_CLASS_COUNT_ATTRIBUTE_KEY = "yearly_format_count_"
//                                            FORMAT_CLASS_COUNT_ATTRIBUTE_KEY = FORMAT_CLASS_COUNT_ATTRIBUTE_KEY
//                                                + (foundWorkout.replace(/\s/g, "").toLowerCase().includes("gym") ? "gym" : foundWorkout.replace(/\s/g, "").toLowerCase())
//                                                + "_" + currentYear
//
//                                            const [ fitnessAttr ] = await Promise.all([
//                                                await userAttributeClient.getCachedUserAttributes(userId, FORMAT_CLASS_COUNT_ATTRIBUTE_KEY)
//                                            ])
//                                            let FORMAT_COUNT_CLASS_ATTRIBUTE_VALUE = null
//                                            if (fitnessAttr && fitnessAttr.attributes && fitnessAttr.attributes[FORMAT_CLASS_COUNT_ATTRIBUTE_KEY]) {
//                                                FORMAT_COUNT_CLASS_ATTRIBUTE_VALUE = parseInt(fitnessAttr.attributes[FORMAT_CLASS_COUNT_ATTRIBUTE_KEY])
//                                            }
//                                            if(!_.isNil(FORMAT_COUNT_CLASS_ATTRIBUTE_VALUE)) {
//                                                FORMAT_COUNT_CLASS_ATTRIBUTE_VALUE = parseInt(FORMAT_COUNT_CLASS_ATTRIBUTE_VALUE) + 1
//                                                const formatCountUserEvent = FitnessReportHelperService.getUserEvent(FORMAT_CLASS_COUNT_ATTRIBUTE_KEY, FORMAT_COUNT_CLASS_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                                userEvents.push(formatCountUserEvent);
//                                                LogUtil.debug(`updateUserYearlyFitnessRashiAttributes formats count - ${FORMAT_CLASS_COUNT_ATTRIBUTE_KEY} - ${FORMAT_COUNT_CLASS_ATTRIBUTE_VALUE}`);
//                                            }
//                                        }
//                                    catch (e) {
//                                        LogUtil.error(`updateUserYearlyFitnessRashiAttributes: Error in updating format count for userId ${userId}`, e);
//                                    }
//                                }
//                                }
//                                if(workoutFormatCount != UNIQUE_WORKOUT_FORMATS_COUNT_ATTRIBUTE_VALUE) {
//                                    UNIQUE_WORKOUT_FORMATS_ATTRIBUTE_VALUE = workoutFormats
//                                    UNIQUE_WORKOUT_FORMATS_COUNT_ATTRIBUTE_VALUE = workoutFormatCount
//                                    const uniqueFormatsUserEvent = FitnessReportHelperService.getUserEvent(UNIQUE_WORKOUT_FORMATS_ATTRIBUTE_KEY, UNIQUE_WORKOUT_FORMATS_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                    userEvents.push(uniqueFormatsUserEvent);
//                                    const uniqueFormatsCountUserEvent = FitnessReportHelperService.getUserEvent(UNIQUE_WORKOUT_FORMATS_COUNT_ATTRIBUTE_KEY, UNIQUE_WORKOUT_FORMATS_COUNT_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                    userEvents.push(uniqueFormatsCountUserEvent);
//                                }
//                                LogUtil.debug(`updateUserYearlyFitnessRashiAttributes workout formats - ${UNIQUE_WORKOUT_FORMATS_COUNT_ATTRIBUTE_VALUE} ${UNIQUE_WORKOUT_FORMATS_ATTRIBUTE_VALUE}`);
//                            }
//                            if(!_.isNil(LONGEST_WEEKLY_STREAK_ATTRIBUTE_VALUE)) { // Done
//                                LONGEST_WEEKLY_STREAK_ATTRIBUTE_VALUE = Math.max(parseInt(LONGEST_WEEKLY_STREAK_ATTRIBUTE_VALUE),
//                                    (!_.isNil(backfillData) && !_.isNil(backfillData?.longestWeeklyStreak))? backfillData?.longestWeeklyStreak : reportData?.maxWeeklyStreak ?? 0)
//                                const longestWeeklyStreakUserEvent = FitnessReportHelperService.getUserEvent(LONGEST_WEEKLY_STREAK_ATTRIBUTE_KEY, LONGEST_WEEKLY_STREAK_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                userEvents.push(longestWeeklyStreakUserEvent);
//                                LogUtil.debug(`updateUserYearlyFitnessRashiAttributes longest streak - ${LONGEST_WEEKLY_STREAK_ATTRIBUTE_VALUE}`);
//                            }
//                            if(!_.isNil(MORNING_SLOT_COUNT_ATTRIBUTE_VALUE) && !_.isNil(EVENING_SLOT_COUNT_ATTRIBUTE_VALUE)
//                                && !_.isNil(FAVOURITE_WORKOUT_SLOT_ATTRIBUTE_VALUE)) { // Done
//                                MORNING_SLOT_COUNT_ATTRIBUTE_VALUE = parseInt(MORNING_SLOT_COUNT_ATTRIBUTE_VALUE)
//                                EVENING_SLOT_COUNT_ATTRIBUTE_VALUE = parseInt(EVENING_SLOT_COUNT_ATTRIBUTE_VALUE)
//                                let morningSlotCount = MORNING_SLOT_COUNT_ATTRIBUTE_VALUE
//                                let eveningSlotCount = EVENING_SLOT_COUNT_ATTRIBUTE_VALUE
//                                if(backfillData) {
//                                    morningSlotCount += backfillData?.morningSlotCount ? backfillData?.morningSlotCount: 0
//                                    eveningSlotCount += backfillData?.eveningSlotCount ? backfillData?.eveningSlotCount: 0
//                                }
//                                else {
//                                    currentTime <= "14:00:00" ? morningSlotCount ++ : eveningSlotCount ++
//                                }
//                                if(morningSlotCount != MORNING_SLOT_COUNT_ATTRIBUTE_VALUE) {
//                                    MORNING_SLOT_COUNT_ATTRIBUTE_VALUE = morningSlotCount
//                                    const morningSlotCountUserEvent = FitnessReportHelperService.getUserEvent(MORNING_SLOT_COUNT_ATTRIBUTE_KEY, MORNING_SLOT_COUNT_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                    userEvents.push(morningSlotCountUserEvent);
//                                }
//                                if (eveningSlotCount != EVENING_SLOT_COUNT_ATTRIBUTE_VALUE) {
//                                    EVENING_SLOT_COUNT_ATTRIBUTE_VALUE = eveningSlotCount
//                                    const eveningSlotCountUserEvent = FitnessReportHelperService.getUserEvent(EVENING_SLOT_COUNT_ATTRIBUTE_KEY, EVENING_SLOT_COUNT_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                    userEvents.push(eveningSlotCountUserEvent);
//                                }
//                                let favourite_slot = MORNING_SLOT_COUNT_ATTRIBUTE_KEY > EVENING_SLOT_COUNT_ATTRIBUTE_KEY ? "Morning": "Evening"
//                                if(!favourite_slot.toLowerCase().includes(FAVOURITE_WORKOUT_SLOT_ATTRIBUTE_VALUE.toLowerCase())) {
//                                    FAVOURITE_WORKOUT_SLOT_ATTRIBUTE_VALUE = favourite_slot
//                                    const favouriteSlotUserEvent = FitnessReportHelperService.getUserEvent(FAVOURITE_WORKOUT_SLOT_ATTRIBUTE_KEY, FAVOURITE_WORKOUT_SLOT_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                    userEvents.push(favouriteSlotUserEvent);
//                                }
//                                LogUtil.debug(`updateUserYearlyFitnessRashiAttributes slots - ${MORNING_SLOT_COUNT_ATTRIBUTE_VALUE} ${EVENING_SLOT_COUNT_ATTRIBUTE_VALUE} ${FAVOURITE_WORKOUT_SLOT_ATTRIBUTE_VALUE}`);
//                            }
//                            LogUtil.debug(`updateUserYearlyFitnessRashiAttributes workouts per month out - ${WORKOUTS_PER_MEMBERSHIP_MONTH_ATTRIBUTE_VALUE} -
//                                ${YEARLY_MEMBERSHIP_DAYS_ATTRIBUTE_VALUE} - ${ACTIVITIES_DONE_ATTRIBUTE_VALUE} - ${!_.isNil(WORKOUTS_PER_MEMBERSHIP_MONTH_ATTRIBUTE_VALUE)} - ${!_.isNil(ACTIVITIES_DONE_ATTRIBUTE_VALUE)}
//                                - ${!_.isNil(YEARLY_MEMBERSHIP_DAYS_ATTRIBUTE_VALUE) && parseInt(YEARLY_MEMBERSHIP_DAYS_ATTRIBUTE_VALUE) > 0}`);
//                            if(!_.isNil(WORKOUTS_PER_MEMBERSHIP_MONTH_ATTRIBUTE_VALUE) && !_.isNil(YEARLY_MEMBERSHIP_DAYS_ATTRIBUTE_VALUE) && parseInt(YEARLY_MEMBERSHIP_DAYS_ATTRIBUTE_VALUE) > 0 && !_.isNil(ACTIVITIES_DONE_ATTRIBUTE_VALUE)) {
//                                let workoutsPerMonth = Math.floor(30 * parseInt(ACTIVITIES_DONE_ATTRIBUTE_VALUE) / parseInt(YEARLY_MEMBERSHIP_DAYS_ATTRIBUTE_VALUE))
//                                if(workoutsPerMonth < 6)
//                                    FITNESS_LEVEL_ATTRIBUTE_VALUE = 1
//                                else if(workoutsPerMonth >= 6 && workoutsPerMonth < 9)
//                                    FITNESS_LEVEL_ATTRIBUTE_VALUE = 2
//                                else if(workoutsPerMonth > 9)
//                                    FITNESS_LEVEL_ATTRIBUTE_VALUE = 3
//                                if(WORKOUTS_PER_MEMBERSHIP_MONTH_ATTRIBUTE_VALUE != workoutsPerMonth) {
//                                    WORKOUTS_PER_MEMBERSHIP_MONTH_ATTRIBUTE_VALUE = workoutsPerMonth
//                                    const workoutsPerMemMonthUserEvent = FitnessReportHelperService.getUserEvent(WORKOUTS_PER_MEMBERSHIP_MONTH_ATTRIBUTE_KEY, WORKOUTS_PER_MEMBERSHIP_MONTH_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                    userEvents.push(workoutsPerMemMonthUserEvent);
//                                    const levelUserEvent = FitnessReportHelperService.getUserEvent(FITNESS_LEVEL_ATTRIBUTE_KEY, FITNESS_LEVEL_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                    userEvents.push(levelUserEvent);
//                                }
//                                LogUtil.debug(`updateUserYearlyFitnessRashiAttributes workouts per month - ${WORKOUTS_PER_MEMBERSHIP_MONTH_ATTRIBUTE_VALUE} ${YEARLY_MEMBERSHIP_DAYS_ATTRIBUTE_VALUE} ${ACTIVITIES_DONE_ATTRIBUTE_VALUE}`);
//                            }
//                            if(!_.isNil(WEEKEND_ACTIVITY_COUNT_ATTRIBUTE_VALUE)) { // Done
//                                let day = new Date().getDay()
//                                let weekendActivityCount = parseInt(WEEKEND_ACTIVITY_COUNT_ATTRIBUTE_VALUE)
//                                if(!_.isNil(backfillData) && !_.isNil(backfillData?.weekendActivityCount)) {
//                                    weekendActivityCount += backfillData?.weekendActivityCount
//                                }
//                                else if(day == 0 || day == 6) {
//                                    weekendActivityCount ++;
//                                }
//                                if(weekendActivityCount != WEEKEND_ACTIVITY_COUNT_ATTRIBUTE_VALUE) {
//                                    WEEKEND_ACTIVITY_COUNT_ATTRIBUTE_VALUE = weekendActivityCount
//                                    const weekendActivityEvent = FitnessReportHelperService.getUserEvent(WEEKEND_ACTIVITY_COUNT_ATTRIBUTE_KEY, WEEKEND_ACTIVITY_COUNT_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                    userEvents.push(weekendActivityEvent);
//                                }
//                                LogUtil.debug(`updateUserYearlyFitnessRashiAttributes weekend - ${WEEKEND_ACTIVITY_COUNT_ATTRIBUTE_VALUE}`);
//                            }
//                            if(!_.isNil(MOST_ACTIVE_MONTH_ATTRIBUTE_VALUE) && !_.isNil(MOST_ACTIVE_MONTH_ACTIVITY_COUNT_ATTRIBUTE_VALUE)) { // Done
//                                if(!lastSevenEntries)
//                                    lastSevenEntries = await FitnessReportHelperService.getPreviousSevenEntries(userId, currentWeekMonday);
//                                let thisMonthActivity = FitnessReportHelperService.getClassesAttendedSinceCount(lastSevenEntries, currentMonthStartDate.toDate(), currentMoment.toDate())
//                                if(currentMonth.toLowerCase().includes(MOST_ACTIVE_MONTH_ATTRIBUTE_VALUE)) {
//                                    MOST_ACTIVE_MONTH_ACTIVITY_COUNT_ATTRIBUTE_VALUE ++;
//                                    const activityCountEvent = FitnessReportHelperService.getUserEvent(MOST_ACTIVE_MONTH_ACTIVITY_COUNT_ATTRIBUTE_KEY, MOST_ACTIVE_MONTH_ACTIVITY_COUNT_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                    userEvents.push(activityCountEvent);
//                                }
//                                else if(MOST_ACTIVE_MONTH_ACTIVITY_COUNT_ATTRIBUTE_VALUE <= thisMonthActivity) {
//                                    MOST_ACTIVE_MONTH_ACTIVITY_COUNT_ATTRIBUTE_VALUE = thisMonthActivity
//                                    MOST_ACTIVE_MONTH_ATTRIBUTE_VALUE = currentMonth
//                                    const activityCountEvent = FitnessReportHelperService.getUserEvent(MOST_ACTIVE_MONTH_ACTIVITY_COUNT_ATTRIBUTE_KEY, MOST_ACTIVE_MONTH_ACTIVITY_COUNT_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                    userEvents.push(activityCountEvent);
//                                    const activityMonthEvent = FitnessReportHelperService.getUserEvent(MOST_ACTIVE_MONTH_ATTRIBUTE_KEY, MOST_ACTIVE_MONTH_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                    userEvents.push(activityMonthEvent);
//                                }
//                                LogUtil.debug(`updateUserYearlyFitnessRashiAttributes active month - ${MOST_ACTIVE_MONTH_ATTRIBUTE_VALUE} ${MOST_ACTIVE_MONTH_ACTIVITY_COUNT_ATTRIBUTE_VALUE}`);
//                            }
//                            if(!_.isNil(FITNESS_COMEBACKS_ATTRIBUTE_VALUE) && !_.isNil(LAST_WORKOUT_DONE_AT)) {
//                                if(currentMoment.diff(moment(new Date(LAST_WORKOUT_DONE_AT), GlobalConstants.INDIA_TIMEZONE), 'days') >= 14) {
//                                    FITNESS_COMEBACKS_ATTRIBUTE_VALUE += 1
//                                    LAST_WORKOUT_DONE_AT_ATTRIBUTE_VALUE = moment(new Date(), GlobalConstants.INDIA_TIMEZONE).format("YYYY-MM-DD")
//                                    const comebacksEvent = FitnessReportHelperService.getUserEvent(FITNESS_COMEBACKS_ATTRIBUTE_KEY, FITNESS_COMEBACKS_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                    userEvents.push(comebacksEvent);
//                                    const lastWorkoutAtEvent = FitnessReportHelperService.getUserEvent(LAST_WORKOUT_DONE_AT_ATTRIBUTE_KEY, LAST_WORKOUT_DONE_AT_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                    userEvents.push(lastWorkoutAtEvent);
//                                }
//                                LogUtil.debug(`updateUserYearlyFitnessRashiAttributes comebacks - ${FITNESS_COMEBACKS_ATTRIBUTE_VALUE} ${LAST_WORKOUT_DONE_AT_ATTRIBUTE_VALUE}`);
//                            }
//                            if(!_.isNil(LAST_WORKOUT_DONE_AT_ATTRIBUTE_VALUE)) {
//                                let lastWorkoutDoneAt = LAST_WORKOUT_DONE_AT_ATTRIBUTE_VALUE
//                                if(backfillData) {
//                                    if(backfillData?.lastWorkoutDoneAt) {
//                                        lastWorkoutDoneAt = backfillData?.lastWorkoutDoneAt
//                                    }
//                                }
//                                else if(currentMoment.diff(moment(new Date(LAST_WORKOUT_DONE_AT), GlobalConstants.INDIA_TIMEZONE), 'days') >= 1) {
//                                    lastWorkoutDoneAt = moment(new Date(), GlobalConstants.INDIA_TIMEZONE).format("YYYY-MM-DD")
//                                }
//                                if(lastWorkoutDoneAt != LAST_WORKOUT_DONE_AT) {
//                                    LAST_WORKOUT_DONE_AT_ATTRIBUTE_VALUE = lastWorkoutDoneAt
//                                    const lastWorkoutAtEvent = FitnessReportHelperService.getUserEvent(LAST_WORKOUT_DONE_AT_ATTRIBUTE_KEY, LAST_WORKOUT_DONE_AT_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                    userEvents.push(lastWorkoutAtEvent);
//                                }
//                            }
//                            if((!_.isNil(FAVOURITE_FORMAT_ATTRIBUTE_VALUE) && !_.isNil(FAVOURITE_FORMAT_CLASSES_ATTRIBUTE_VALUE)) ||
//                                !_.isNil(GX_CLASSES_ATTRIBUTE_VALUE) || !_.isNil(GYM_CLASSES_ATTRIBUTE_VALUE) || !_.isNil(LIVE_CLASSES_ATTRIBUTE_VALUE)
//                                || !_.isNil(PLAY_CLASSES_ATTRIBUTE_VALUE || !_.isNil(DIY_CLASSES_ATTRIBUTE_VALUE))
//                            ) {
//                                let gx_classes = !_.isNil(GX_CLASSES_ATTRIBUTE_VALUE) && GX_CLASSES_ATTRIBUTE_VALUE.length ? parseInt(GX_CLASSES_ATTRIBUTE_VALUE) : 0
//                                let gym_classes = !_.isNil(GYM_CLASSES_ATTRIBUTE_VALUE) && GYM_CLASSES_ATTRIBUTE_VALUE.length ? parseInt(GYM_CLASSES_ATTRIBUTE_VALUE) : 0
//                                let live_classes = !_.isNil(LIVE_CLASSES_ATTRIBUTE_VALUE) && LIVE_CLASSES_ATTRIBUTE_VALUE.length ? parseInt(LIVE_CLASSES_ATTRIBUTE_VALUE) : 0
//                                let play_classes = !_.isNil(PLAY_CLASSES_ATTRIBUTE_VALUE) && PLAY_CLASSES_ATTRIBUTE_VALUE.length ? parseInt(PLAY_CLASSES_ATTRIBUTE_VALUE) : 0
//                                let diy_classes = !_.isNil(DIY_CLASSES_ATTRIBUTE_VALUE) && DIY_CLASSES_ATTRIBUTE_VALUE.length ? parseInt(DIY_CLASSES_ATTRIBUTE_VALUE) : 0
//
//                                // NOTE : Fav Format and Fav Format class count not to be real time attributes
//                                // let fav_format = FAVOURITE_FORMAT_ATTRIBUTE_VALUE
//                                // let fav_format_count = FAVOURITE_FORMAT_CLASSES_ATTRIBUTE_VALUE != null ? parseInt(FAVOURITE_FORMAT_ATTRIBUTE_VALUE) : null
//
//                                if(_.isNil(backfillData)) {
//                                    if(workoutDetails.name.toString().toLowerCase().includes("gym")) {
//                                        gym_classes ++;
//                                        GYM_CLASSES_ATTRIBUTE_VALUE = gym_classes
//                                        const formatCountUserEvent = FitnessReportHelperService.getUserEvent(GYM_CLASSES_ATTRIBUTE_KEY, GYM_CLASSES_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                        userEvents.push(formatCountUserEvent);
//                                        // NOTE : Fav Format and Fav Format class count not to be real time attributes
//                                        // if(fav_format && fav_format_count && !fav_format.toString().toLowerCase().includes("gym") && gym_classes > fav_format_count) {
//                                        //     const favFormatCountUserEvent = FitnessReportHelperService.getUserEvent(FAVOURITE_FORMAT_CLASSES_ATTRIBUTE_KEY, gym_classes, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                        //     userEvents.push(favFormatCountUserEvent);
//                                        // }
//                                    }
//                                    else if(workoutDetails.name.toString().toLowerCase().includes("diy")) {
//                                        diy_classes ++;
//                                        DIY_CLASSES_ATTRIBUTE_VALUE = diy_classes
//                                        const formatCountUserEvent = FitnessReportHelperService.getUserEvent(DIY_CLASSES_ATTRIBUTE_KEY, DIY_CLASSES_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                        userEvents.push(formatCountUserEvent);
//                                        // NOTE : Fav Format and Fav Format class count not to be real time attributes
//                                        // if(fav_format && fav_format_count && !fav_format.toString().toLowerCase().includes("gym") && diy_classes > fav_format_count) {
//                                        //     const favFormatCountUserEvent = FitnessReportHelperService.getUserEvent(FAVOURITE_FORMAT_CLASSES_ATTRIBUTE_KEY, diy_classes, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                        //     userEvents.push(favFormatCountUserEvent);
//                                        // }
//                                    }
//                                    else if(workoutDetails.id.toString().toLowerCase().includes("live") || workoutDetails.name.toString().toLowerCase().includes("live")) {
//                                        live_classes ++;
//                                        LIVE_CLASSES_ATTRIBUTE_VALUE = live_classes
//                                        const formatCountUserEvent = FitnessReportHelperService.getUserEvent(LIVE_CLASSES_ATTRIBUTE_KEY, LIVE_CLASSES_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                        userEvents.push(formatCountUserEvent);
//                                        // NOTE : Fav Format and Fav Format class count not to be real time attributes
//                                        // if(fav_format && fav_format_count && !fav_format.toString().toLowerCase().includes("live") && live_classes > fav_format_count) {
//                                        //     const favFormatCountUserEvent = FitnessReportHelperService.getUserEvent(FAVOURITE_FORMAT_CLASSES_ATTRIBUTE_KEY, live_classes, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                        //     userEvents.push(favFormatCountUserEvent);
//                                        // }
//                                    }
//                                    else if(workoutDetails.id.toString().toLowerCase().includes("play")) {
//                                        play_classes ++;
//                                        PLAY_CLASSES_ATTRIBUTE_VALUE = play_classes
//                                        const formatCountUserEvent = FitnessReportHelperService.getUserEvent(PLAY_CLASSES_ATTRIBUTE_KEY, PLAY_CLASSES_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                        userEvents.push(formatCountUserEvent);
//                                        // NOTE : Fav Format and Fav Format class count not to be real time attributes
//                                        // if(fav_format && fav_format_count && !fav_format.toString().toLowerCase().includes("play") && play_classes > fav_format_count) {
//                                        //     const favFormatCountUserEvent = FitnessReportHelperService.getUserEvent(FAVOURITE_FORMAT_CLASSES_ATTRIBUTE_KEY, play_classes, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                        //     userEvents.push(favFormatCountUserEvent);
//                                        // }
//                                    }
//                                    else {
//                                        gx_classes ++;
//                                        GX_CLASSES_ATTRIBUTE_VALUE = gx_classes
//                                        const formatCountUserEvent = FitnessReportHelperService.getUserEvent(GX_CLASSES_ATTRIBUTE_KEY, GX_CLASSES_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                        userEvents.push(formatCountUserEvent);
//                                        // NOTE : Fav Format and Fav Format class count not to be real time attributes
//                                        // if(fav_format && fav_format_count && gx_classes > fav_format_count) {
//                                        //     const favFormatCountUserEvent = FitnessReportHelperService.getUserEvent(FAVOURITE_FORMAT_CLASSES_ATTRIBUTE_KEY, gx_classes, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                        //     userEvents.push(favFormatCountUserEvent);
//                                        // }
//                                    }
//                                }
//                                else {
//                                    for(let workout of backfillData?.workoutFormats) {
//                                        const lowercaseSearchWorkoutName = workout.toString().toLowerCase();
//                                        if(lowercaseSearchWorkoutName.includes("gym")) {
//                                            gym_classes ++;
//                                            GYM_CLASSES_ATTRIBUTE_VALUE = gym_classes
//                                            const formatCountUserEvent = FitnessReportHelperService.getUserEvent(GYM_CLASSES_ATTRIBUTE_KEY, GYM_CLASSES_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                            userEvents.push(formatCountUserEvent);
//                                        }
//                                        else if(lowercaseSearchWorkoutName.includes("diy")) {
//                                            diy_classes ++;
//                                            DIY_CLASSES_ATTRIBUTE_VALUE = diy_classes
//                                            const formatCountUserEvent = FitnessReportHelperService.getUserEvent(DIY_CLASSES_ATTRIBUTE_KEY, DIY_CLASSES_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                            userEvents.push(formatCountUserEvent);
//                                        }
//                                        else if(lowercaseSearchWorkoutName.includes("live")) {
//                                            live_classes ++;
//                                            LIVE_CLASSES_ATTRIBUTE_VALUE = live_classes
//                                            const formatCountUserEvent = FitnessReportHelperService.getUserEvent(LIVE_CLASSES_ATTRIBUTE_KEY, LIVE_CLASSES_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                            userEvents.push(formatCountUserEvent);
//                                        }
//                                        else if(lowercaseSearchWorkoutName.includes("play")) {
//                                            play_classes ++;
//                                            PLAY_CLASSES_ATTRIBUTE_VALUE = play_classes
//                                            const formatCountUserEvent = FitnessReportHelperService.getUserEvent(PLAY_CLASSES_ATTRIBUTE_KEY, PLAY_CLASSES_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                            userEvents.push(formatCountUserEvent);
//                                        }
//                                        else {
//                                            gx_classes ++;
//                                            GX_CLASSES_ATTRIBUTE_VALUE = gx_classes
//                                            const formatCountUserEvent = FitnessReportHelperService.getUserEvent(GX_CLASSES_ATTRIBUTE_KEY, GX_CLASSES_ATTRIBUTE_VALUE, userId, RashiUserProfileEventName.user_yearly_fitness_attribute_updated);
//                                            userEvents.push(formatCountUserEvent);
//                                        }
//                                    }
//                                }
//                            }
//                        }
//                     */
//
//                    /*
//                    if(userEvents.length) {
//                        rashiEventService.publishBulkUserProfileEvents(userEvents, AttributeAction.REPLACE)
//                        .then(isPublished => LogUtil.info(`Rashi user yearly fitness attribute profile event result ${isPublished}`))
//                        .catch(error => LogUtil.error(`Cannot publish user yearly fitness attribute profile event to Rashi with error `, error))
//                        await sleep(5);
//                    }
//                     */
//
//
//
//                }
//            }
        } catch(Exception e) {
            throw new RuntimeException(e);
        }
    }
}
