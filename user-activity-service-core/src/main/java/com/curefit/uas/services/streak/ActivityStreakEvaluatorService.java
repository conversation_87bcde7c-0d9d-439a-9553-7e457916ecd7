package com.curefit.uas.services.streak;


import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.logging.models.ActivityDS;
import com.curefit.logging.models.ActivityTypeDS;
import com.curefit.uas.entities.streaks.StreakActivityLogEntity;
import com.curefit.uas.entities.streaks.StreakConfigEntity;
import com.curefit.uas.entities.streaks.StreakEntity;
import com.curefit.uas.entities.streaks.StreakPseudoActivityEntity;
import com.curefit.uas.mapper.StreakMapper;
import com.curefit.uas.pojo.StreakActivityRewardConfig;
import com.curefit.uas.pojo.streak.activityDetails.BaseActivityDetails;
import com.curefit.uas.repository.mongo.streaks.IStreakActivityLogRepository;
import com.curefit.uas.services.streak.activityEvaluators.IActivityStreakEvaluatorService;
import com.curefit.uas.utils.TimeUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ActivityStreakEvaluatorService {
    Set<IActivityStreakEvaluatorService> activityStreakEvaluatorServiceSet;
    IStreakActivityLogRepository streakActivityLogRepository;
    StreakHelperService streakHelperService;
    StreakMapper streakMapper;
    RollbarService rollbarService;
    
    public ActivityStreakEvaluatorService(Set<IActivityStreakEvaluatorService> activityStreakEvaluatorServiceSet, IStreakActivityLogRepository streakActivityLogRepository, @Lazy StreakHelperService streakHelperService, StreakMapper streakMapper, RollbarService rollbarService) {
        this.activityStreakEvaluatorServiceSet = activityStreakEvaluatorServiceSet;
        this.streakActivityLogRepository = streakActivityLogRepository;
        this.streakHelperService = streakHelperService;
        this.streakMapper = streakMapper;
        this.rollbarService = rollbarService;
    }
    
    
    private IActivityStreakEvaluatorService getActivityStreakEvaluatorService(ActivityTypeDS activityDSType) throws IllegalArgumentException {
        for (IActivityStreakEvaluatorService activityStreakEvaluatorService : activityStreakEvaluatorServiceSet) {
            if (activityStreakEvaluatorService.isEligibleActivityType(activityDSType)) {
                return activityStreakEvaluatorService;
            }
        }
        throw new IllegalArgumentException("No IActivityStreakEvaluatorService found for activityTypeDS: " + activityDSType);
    }
    public BaseActivityDetails getActivityDetailsFromString(ActivityTypeDS activityTypeDS, String activityDetailsString) throws JsonProcessingException {
        IActivityStreakEvaluatorService activityStreakEvaluatorService = getActivityStreakEvaluatorService(activityTypeDS);
        return activityStreakEvaluatorService.getBaseActivityDetailsFromString(activityDetailsString);
    }
    
    public BaseActivityDetails getActivityDetailsFromActivityDS(ActivityDS activityDS) {
        IActivityStreakEvaluatorService activityStreakEvaluatorService = getActivityStreakEvaluatorService(activityDS.getActivityType());
        return activityStreakEvaluatorService.getActivityDetailsFromActivityDS(activityDS);
    }
    public BaseActivityDetails getActivityDetailsFromStreakPseudoActivity(StreakPseudoActivityEntity streakPseudoActivityEntity) {
        ActivityDS activityDS = streakMapper.map(streakPseudoActivityEntity);
        return getActivityDetailsFromActivityDS(activityDS);
    }
    public StreakEntity processActivityAndReturnStreak(String userId, BaseActivityDetails activityDetails, StreakConfigEntity streakConfig, StreakEntity userCurrentStreak) {
        String currentActivityIdKey = activityDetails.getIdempotenceKey();
        ActivityTypeDS activityType = activityDetails.getActivityType();
        
        if (userCurrentStreak == null) {
            log.info("Streak processActivityAndReturnStreak pre userId={} currentActivityId={} userCurrentStreak: null", userId, activityDetails.getIdempotenceKey());
        } else {
            log.info("Streak processActivityAndReturnStreak pre userId={} currentActivityId={} userCurrentStreak: maxStreakCount={} streakCount={} streakVersion={} tentativeStreakBreakDate={}", userId, activityDetails.getIdempotenceKey(), userCurrentStreak.getMaxStreakCount(), userCurrentStreak.getStreakCount(), userCurrentStreak.getStreakVersion(), userCurrentStreak.getTentativeStreakBreakDate());
        }
        
        boolean isFirstActivityInStreak = userCurrentStreak == null || userCurrentStreak.getStreakCount() == 0;
        boolean isActivityAfterStreakBreakDate = userCurrentStreak != null && StringUtils.isNotBlank(userCurrentStreak.getTentativeStreakBreakDate()) && TimeUtil.getLocalDateFromString(activityDetails.getDate()).isAfter(TimeUtil.getLocalDateFromString(userCurrentStreak.getTentativeStreakBreakDate()));
        
        if (isFirstActivityInStreak || isActivityAfterStreakBreakDate) {
            // break streak;
            userCurrentStreak = streakHelperService.resetCurrentStreakForUser(Long.valueOf(userId), activityDetails.getDate(), userCurrentStreak, streakConfig);
        }
        
        // calculate earned rest days
        Long activityScore = getScoreForActivity(activityType, activityDetails);
        int rewardedDaysForActivity = getRewardedDaysForActivityScore(activityType, activityScore, streakConfig);
        if (rewardedDaysForActivity == -1) {
            log.info("Streak processActivityAndReturnStreak userId={} currentActivityId={} rewardedDaysForActivity={} activityType={} activityScore={}", userId, activityDetails.getIdempotenceKey(), rewardedDaysForActivity, activityType, activityScore);
            return userCurrentStreak;
        }
        
        List<StreakActivityLogEntity> activityLogEntitiesForToday = streakActivityLogRepository.findAllByUserIdAndActivityDate(Long.valueOf(userId), activityDetails.getDate());
        AtomicInteger alreadyRewardedDaysForDate = new AtomicInteger(0);
        AtomicReference<StreakActivityLogEntity> streakActivityLogEntity = new AtomicReference<>();
        
        activityLogEntitiesForToday.forEach(activityLogEntity -> {
            alreadyRewardedDaysForDate.set(Math.max(activityLogEntity.getRewardedDays(), alreadyRewardedDaysForDate.get()));
            if (currentActivityIdKey.equals(activityLogEntity.getActivityIdempotenceKey())) {
                streakActivityLogEntity.set(activityLogEntity);
            }
        });
        
        // find and update existing log entry for the idempotence key
        if (streakActivityLogEntity.get() == null) {
            streakActivityLogEntity.set(StreakActivityLogEntity.builder()
                    .userId(userCurrentStreak.getUserId())
                    .streakConfigId(streakConfig.getConfigId())
                    .streakVersion(userCurrentStreak.getStreakVersion())
                    .activityIdempotenceKey(currentActivityIdKey)
                    .activityDate(activityDetails.getDate())
                    .activityType(activityDetails.getActivityType())
                    .rewardedDays(0)
                    .build());
        }
        streakActivityLogEntity.get().setRewardedDays(rewardedDaysForActivity);
        streakActivityLogEntity.get().setActivityScore(activityScore);
        streakActivityLogRepository.save(streakActivityLogEntity.get());
        
        boolean isStreakActive = userCurrentStreak != null && StringUtils.isBlank(userCurrentStreak.getStreakPauseId());
        
        int netExtraRestDaysToReward = Math.max(rewardedDaysForActivity - alreadyRewardedDaysForDate.get(), 0);
        String tentativeStreakBreakDate = userCurrentStreak != null ? userCurrentStreak.getTentativeStreakBreakDate() : null;
        log.info("Streak processActivityAndReturnStreak mid userId={} currentActivityId={} rewardedDaysForActivity={} netExtraRestDaysToReward={} isStreakActive={} current_streak_break_date={}",
                userId, activityDetails.getIdempotenceKey(), rewardedDaysForActivity, netExtraRestDaysToReward, isStreakActive, tentativeStreakBreakDate);
        
        if (netExtraRestDaysToReward == 0) {
            return userCurrentStreak;
        }
        if (isStreakActive) {
            // update streak break date
            String newStreakBreakDate_raw = TimeUtil.getLocalDateFromString(userCurrentStreak.getTentativeStreakBreakDate()).plusDays(netExtraRestDaysToReward).toString();
            
            String newStreakBreakDate = streakHelperService.getUpdatedStreakBreakDateForStreakConfig(activityDetails.getDate(), newStreakBreakDate_raw, userCurrentStreak, streakConfig);
            userCurrentStreak.setTentativeStreakBreakDate(newStreakBreakDate);
        }
        
        
        // set first activity details
        if (userCurrentStreak.getStreakCount() == 0) {
            userCurrentStreak.setFirstActivityDate(activityDetails.getDate());
            userCurrentStreak.setFirstActivityType(activityDetails.getActivityType());
            userCurrentStreak.setFirstActivityIdempotenceKey(activityDetails.getIdempotenceKey());
        }
        
        // set last activity details
        userCurrentStreak.setLastActivityDate(activityDetails.getDate());
        userCurrentStreak.setLastActivityType(activityDetails.getActivityType());
        userCurrentStreak.setLastActivityIdempotenceKey(activityDetails.getIdempotenceKey());
        
        // update streak count
        int newStreakCount = userCurrentStreak.getStreakCount() + 1;
        userCurrentStreak.setStreakCount(newStreakCount);
        
        // update best streak
        if (newStreakCount > userCurrentStreak.getMaxStreakCount()) {
            userCurrentStreak.setMaxStreakCount(newStreakCount);
            userCurrentStreak.setMaxStreakVersion(userCurrentStreak.getStreakVersion());
        }
        log.info("Streak processActivityAndReturnStreak post userId={} currentActivityId={} userCurrentStreak: maxStreakCount={} streakCount={} streakVersion={} tentativeStreakBreakDate={}", userId, activityDetails.getIdempotenceKey(), userCurrentStreak.getMaxStreakCount(), userCurrentStreak.getStreakCount(), userCurrentStreak.getStreakVersion(), userCurrentStreak.getTentativeStreakBreakDate());
        return userCurrentStreak;
    }
    public Long getScoreForActivity(ActivityTypeDS activityTypeDS, BaseActivityDetails activityDetails) {
        IActivityStreakEvaluatorService activityStreakEvaluatorService = getActivityStreakEvaluatorService(activityTypeDS);
        return activityStreakEvaluatorService.getScoreForCurrentActivity(activityTypeDS, activityDetails);
    }
    
    private int getRewardedDaysForActivityScore(ActivityTypeDS activityType, Long activityScore, StreakConfigEntity streakConfig) {
        List<StreakActivityRewardConfig> streakActivityRewardConfigs = new ArrayList<>(streakConfig.getActivityRewardConfigMap().get(activityType.toString()));
        streakActivityRewardConfigs.sort(Comparator.comparing(StreakActivityRewardConfig::getActivityScore));
        StreakActivityRewardConfig highestEarnedRewardConfig = StreakHelperService.findHighestEarnedRewardConfig(streakActivityRewardConfigs, activityScore);
        if (highestEarnedRewardConfig == null) {
            return -1;
        }
        return highestEarnedRewardConfig.getEarnedRestDays();
    }
}
