package com.curefit.uas.services.fitnessReportMigration;

import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.uas.services.fitnessReportService.FitnessReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.UUID;
import java.util.concurrent.Callable;

@RequiredArgsConstructor
@Slf4j
public class FitnessReportMigrationTask implements Callable<String> {
    private final int iterations;
    private final long reqStartingId;
    private final long totalEntriesToMigrate;
    private final long batchSize;
    private final boolean ignoreExistingData;
    private final FitnessReportService fitnessReportService;
    private final RollbarService rollbarService;
    
    @Override
    public String call() {
        int iterationsLeft = this.iterations;
        long startingId = reqStartingId;
        String uuid = UUID.randomUUID().toString();
        long totalStartTime = System.currentTimeMillis();
        while (iterationsLeft > 0) {
            try {
                long startTime = System.currentTimeMillis();
                int migratedCount = fitnessReportService.triggerFitnessReportMigration(startingId, totalEntriesToMigrate, batchSize, ignoreExistingData);
                if (migratedCount < 0) iterationsLeft = 0;
                iterationsLeft--;
                log.info("FitnessReportMigrationTask {} Completed Batch at iteration={} ignoreExistingData={} startingId={} totalEntriesToMigrate={} migratedCount={} batchSize={} took {}s", uuid, iterations - iterationsLeft - 1, ignoreExistingData, startingId, totalEntriesToMigrate, migratedCount, batchSize, (System.currentTimeMillis() - startTime) / 1000);
                startingId = startingId + totalEntriesToMigrate;
            } catch (Exception e) {
                rollbarService.error(e, "FitnessReportMigrationTask Error at iteration=" + iterationsLeft + " reqStartingId=" + reqStartingId + " totalEntriesToMigrate=" + totalEntriesToMigrate + " batchSize=" + batchSize);
                return String.valueOf(iterationsLeft);
            }
        }
        log.info("FitnessReportMigrationTask {} Completed Total for iterations={} reqStartingId={} totalEntriesToMigrate={} batchSize={} ignoreExistingData={} {}s",
                uuid, iterations, reqStartingId, totalEntriesToMigrate, batchSize, ignoreExistingData, (System.currentTimeMillis() - totalStartTime) / 1000);
        return String.valueOf(iterationsLeft);
    }
}
