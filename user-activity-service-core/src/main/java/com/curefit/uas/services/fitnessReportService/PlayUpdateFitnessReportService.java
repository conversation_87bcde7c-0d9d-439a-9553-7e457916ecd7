package com.curefit.uas.services.fitnessReportService;

import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.lock.LockService;
import com.curefit.cult.models.ClassDetails;
import com.curefit.cult.models.WorkoutDetails;
import com.curefit.gymfit.client.GymfitClient;
import com.curefit.hercules.client.HerculesService;
import com.curefit.metricservice.client.MetricClient;
import com.curefit.rashi.client.RashiClient;
import com.curefit.rashi.client.UserAttributesClient;
import com.curefit.segmentation.client.rest.SegmentationClient;
import com.curefit.segmentation.client.rest.UserSegmentClient;
import com.curefit.uas.enums.ActivityType;
import com.curefit.uas.pojo.fitnessReport.FitnessReportEntry;
import com.curefit.uas.pojo.fitnessReport.PlayUpdateFitnessReportBody;
import com.curefit.uas.mapper.FitnessReportMapper;
import com.curefit.uas.publisher.FitnessReportHabitAndMilestonesPublisher;
import com.curefit.uas.repository.mongo.FitnessReportRepository;
import com.curefit.uas.services.ConfigStoreWrapperService;
import com.curefit.uas.utils.FitnessReportConstants;
import com.curefit.uas.utils.ObjectUtils;
import com.curefit.uas.utils.TimeUtil;
import com.curefit.ufs.services.UfsService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
class PlayUpdateFitnessReportService extends BaseUpdateFitnessReportService implements UpdateFitnessReportService {
    public PlayUpdateFitnessReportService(ObjectMapper objectMapper, FitnessReportMapper fitnessReportMapper, FitnessReportHabitAndMilestonesPublisher fitnessReportHabitAndMilestonesPublisher, HerculesService herculesService, ConfigStoreWrapperService configStore, RollbarService rollbarService, MetricClient metricClient, UfsService ufsService, GymfitClient gymfitClient, LockService lockService, UserSegmentClient userSegmentClient, SegmentationClient segmentationClient, UserAttributesClient userAttributesClient, RashiClient rashiClient, FitnessReportRepository fitnessReportRepository, UpdateFitnessReportHelperService updateFitnessReportHelperService) {
        super(objectMapper, fitnessReportMapper, fitnessReportHabitAndMilestonesPublisher, herculesService, configStore, rollbarService, metricClient, ufsService, gymfitClient, lockService, userSegmentClient, segmentationClient, userAttributesClient, rashiClient, fitnessReportRepository, updateFitnessReportHelperService);
    }
    @Override
    public boolean isEligibleActivityType(ActivityType activityType) {
        return ActivityType.PLAY.equals(activityType);
    }
    
    @Override
    public FitnessReportEntry processClass(String sqsMsgBodyString, String logTag) throws Exception {
        PlayUpdateFitnessReportBody playUpdateFitnessReportBody;
        try {
            playUpdateFitnessReportBody = objectMapper.readValue(sqsMsgBodyString, PlayUpdateFitnessReportBody.class);
        } catch (JsonProcessingException e) {
            log.error("{}Error in processing play class for sqsMsgBodyString:{}", logTag, sqsMsgBodyString);
            throw e;
        }
        try {
            Long userId = playUpdateFitnessReportBody.getUserId();
            String sessionId = playUpdateFitnessReportBody.getBookingId() + "_" + playUpdateFitnessReportBody.getSlotId() + "_" + playUpdateFitnessReportBody.getCenterId();
            long durationInMinutes = TimeUtil.calculateDurationInMinutes(playUpdateFitnessReportBody.getStartTime(), playUpdateFitnessReportBody.getEndTime());
            ClassDetails classDetails = new ClassDetails();
            classDetails.setId(sessionId);
            classDetails.setDate(TimeUtil.convertToDateFromCustomString(playUpdateFitnessReportBody.getDate()).toString());
            classDetails.setTimezone(FitnessReportConstants.INDIA_TIMEZONE);
            classDetails.setWodId(playUpdateFitnessReportBody.getWodId());
            classDetails.setDurationMinutes((int) durationInMinutes);
            classDetails.setDurationH(durationInMinutes / 60.00f);
            classDetails.setCalories(ObjectUtils.convertToInteger(playUpdateFitnessReportBody.getCalories()));
            WorkoutDetails workoutDetails = new WorkoutDetails();
            workoutDetails.setId(FitnessReportConstants.FITNESS_REPORT_PLAY_ID);
            workoutDetails.setName(playUpdateFitnessReportBody.getWorkoutName());
            return this.processClass(userId, classDetails, workoutDetails, null);
        } catch(Exception e) {
            rollbarService.error(e, logTag + "Error in processing play class for playUpdateFitnessReportBody: " + playUpdateFitnessReportBody.toString());
            throw e;
        }
    }
}
