package com.curefit.uas.services.fitnessReportService;

import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.lock.LockService;
import com.curefit.cult.models.ClassDetails;
import com.curefit.cult.models.WorkoutDetails;
import com.curefit.gymfit.client.GymfitClient;
import com.curefit.hercules.client.HerculesService;
import com.curefit.metricservice.client.MetricClient;
import com.curefit.rashi.client.RashiClient;
import com.curefit.rashi.client.UserAttributesClient;
import com.curefit.segmentation.client.rest.SegmentationClient;
import com.curefit.segmentation.client.rest.UserSegmentClient;
import com.curefit.uas.enums.ActivityType;
import com.curefit.uas.mapper.FitnessReportMapper;
import com.curefit.uas.pojo.fitnessReport.FitnessReportEntry;
import com.curefit.uas.pojo.fitnessReport.LiveUpdateFitnessReportBody;
import com.curefit.uas.publisher.FitnessReportHabitAndMilestonesPublisher;
import com.curefit.uas.repository.mongo.FitnessReportRepository;
import com.curefit.uas.services.ConfigStoreWrapperService;
import com.curefit.uas.utils.FitnessReportConstants;
import com.curefit.uas.utils.TimeUtil;
import com.curefit.ufs.services.UfsService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Slf4j
class LiveUpdateFitnessReportService extends BaseUpdateFitnessReportService implements UpdateFitnessReportService {
    public LiveUpdateFitnessReportService(ObjectMapper objectMapper, FitnessReportMapper fitnessReportMapper, FitnessReportHabitAndMilestonesPublisher fitnessReportHabitAndMilestonesPublisher, HerculesService herculesService, ConfigStoreWrapperService configStore, RollbarService rollbarService, MetricClient metricClient, UfsService ufsService, GymfitClient gymfitClient, LockService lockService, UserSegmentClient userSegmentClient, SegmentationClient segmentationClient, UserAttributesClient userAttributesClient, RashiClient rashiClient, FitnessReportRepository fitnessReportRepository, UpdateFitnessReportHelperService updateFitnessReportHelperService) {
        super(objectMapper, fitnessReportMapper, fitnessReportHabitAndMilestonesPublisher, herculesService, configStore, rollbarService, metricClient, ufsService, gymfitClient, lockService, userSegmentClient, segmentationClient, userAttributesClient, rashiClient, fitnessReportRepository, updateFitnessReportHelperService);
    }
    @Override
    public boolean isEligibleActivityType(ActivityType activityType) {
        return ActivityType.LIVE.equals(activityType) || ActivityType.GYM.equals(activityType);
    }
    
    @Override
    public FitnessReportEntry processClass(String sqsMsgBodyString, String logTag) throws Exception {
        LiveUpdateFitnessReportBody liveUpdateFitnessReportBody = objectMapper.readValue(sqsMsgBodyString, LiveUpdateFitnessReportBody.class);
        try {
            
            Long userId = liveUpdateFitnessReportBody.getUserId();
            ClassDetails classDetails = liveUpdateFitnessReportBody.getClassDetails();
            classDetails.setDate(TimeUtil.convertStringDateToYYYYMMDDFormat(classDetails.getDate()));
            classDetails.setTimezone(FitnessReportConstants.INDIA_TIMEZONE);
            int durationInMinutes = Optional.ofNullable(classDetails.getDurationMinutes()).orElse(0);
            classDetails.setDurationH(durationInMinutes / 60.00f);
            classDetails.setDurationMinutes(durationInMinutes);
            WorkoutDetails workoutDetails = liveUpdateFitnessReportBody.getWorkoutDetails();
            return this.processClass(userId, classDetails, workoutDetails, null);
        } catch(Exception e) {
            rollbarService.error(e, logTag + "Error in processing play class for liveUpdateFitnessReportBody: " + liveUpdateFitnessReportBody.toString());
            throw e;
        }
    }
}
