package com.curefit.uas.services.fitnessReportService;

import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.lock.LockService;
import com.curefit.cult.models.ClassDetails;
import com.curefit.cult.models.WorkoutDetails;
import com.curefit.gymfit.client.GymfitClient;
import com.curefit.gymfit.models.CheckIn;
import com.curefit.hercules.client.HerculesService;
import com.curefit.metricservice.client.MetricClient;
import com.curefit.rashi.client.RashiClient;
import com.curefit.rashi.client.UserAttributesClient;
import com.curefit.segmentation.client.rest.SegmentationClient;
import com.curefit.segmentation.client.rest.UserSegmentClient;
import com.curefit.uas.enums.ActivityType;
import com.curefit.uas.pojo.fitnessReport.FitnessReportEntry;
import com.curefit.uas.pojo.fitnessReport.UFSUpdateFitnessReportBody;
import com.curefit.uas.mapper.FitnessReportMapper;
import com.curefit.uas.publisher.FitnessReportHabitAndMilestonesPublisher;
import com.curefit.uas.repository.mongo.FitnessReportRepository;
import com.curefit.uas.services.ConfigStoreWrapperService;
import com.curefit.uas.utils.FitnessReportConstants;
import com.curefit.uas.utils.TimeUtil;
import com.curefit.ufs.services.UfsService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

@Service
class UFSUpdateFitnessReportService extends BaseUpdateFitnessReportService implements UpdateFitnessReportService {
    final RollbarService rollbarService;
    final GymfitClient gymfitClient;
    public UFSUpdateFitnessReportService(ObjectMapper objectMapper, FitnessReportMapper fitnessReportMapper, FitnessReportHabitAndMilestonesPublisher fitnessReportHabitAndMilestonesPublisher, HerculesService herculesService, ConfigStoreWrapperService configStore, RollbarService rollbarService, MetricClient metricClient, UfsService ufsService, GymfitClient gymfitClient, LockService lockService, UserSegmentClient userSegmentClient, SegmentationClient segmentationClient, UserAttributesClient userAttributesClient, RashiClient rashiClient, FitnessReportRepository fitnessReportRepository, UpdateFitnessReportHelperService updateFitnessReportHelperService) {
        super(objectMapper, fitnessReportMapper, fitnessReportHabitAndMilestonesPublisher, herculesService, configStore, rollbarService, metricClient, ufsService, gymfitClient, lockService, userSegmentClient, segmentationClient, userAttributesClient, rashiClient, fitnessReportRepository, updateFitnessReportHelperService);
        this.rollbarService = rollbarService;
        this.gymfitClient = gymfitClient;
    }
    @Override
    public boolean isEligibleActivityType(ActivityType activityType) {
        return ActivityType.UFS.equals(activityType);
    }
    
    // This is used to update already logged in gym report once ufs wod completion event is fired or mimic a gym checkin event
    @Override
    public FitnessReportEntry processClass(String sqsMsgBodyString, String logTag) throws Exception {
        UFSUpdateFitnessReportBody ufsUpdateFitnessReportBody = objectMapper.readValue(sqsMsgBodyString, UFSUpdateFitnessReportBody.class);
        Long userId = ufsUpdateFitnessReportBody.getUserId();
        Long checkInId = ufsUpdateFitnessReportBody.getCheckInId();
        String wodId = ufsUpdateFitnessReportBody.getWodId();
       try {
           CheckIn checkIn = gymfitClient.checkInService().getCheckinById(checkInId, String.valueOf(userId));
           ClassDetails classDetails = new ClassDetails();
           classDetails.setId(String.valueOf(checkInId));
           LocalDateTime dateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(checkIn.getEndTime()), ZoneId.systemDefault());
           classDetails.setDate(TimeUtil.getDateInGivenFormatFromLocalDateTIme(TimeUtil.DATE_FORMAT, TimeUtil.getLocalDateTimeFromEpoch(checkIn.getEndTime())));
           classDetails.setWodId(null);
           classDetails.setTimezone(FitnessReportConstants.INDIA_TIMEZONE);
           classDetails.setDurationH(1.50f);
           classDetails.setDurationMinutes(90);
           WorkoutDetails workoutDetails = new WorkoutDetails();
           workoutDetails.setId(FitnessReportConstants.FITNESS_REPORT_GYMFIT_ID);
           workoutDetails.setName(FitnessReportConstants.FITNESS_REPORT_GYMFIT_NAME);
           return this.processClass(userId, classDetails, workoutDetails, wodId);
       } catch(Exception e) {
           rollbarService.error(e, logTag + "Error while fetching gymfit class details for checkInId: " + checkInId);
           throw e;
       }
    }
}
