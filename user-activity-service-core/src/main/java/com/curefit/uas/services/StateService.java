package com.curefit.uas.services;

import com.curefit.commons.sf.repository.BaseMySQLRepository;
import com.curefit.commons.sf.service.BaseMySQLService;
import com.curefit.uas.entities.StateEntity;
import com.curefit.uas.pojo.entries.State;
import com.curefit.uas.services.intefaces.IStateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class StateService extends BaseMySQLService<StateEntity, State> implements IStateService {

    public StateService(BaseMySQLRepository<StateEntity> crudRepository) {
        super(crudRepository);
    }
}
