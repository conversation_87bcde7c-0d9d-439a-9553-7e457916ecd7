package com.curefit.uas.services.fitnessReportService;

import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.uas.enums.ActivityType;
import com.curefit.uas.pojo.fitnessReport.BaseUpdateFitnessReportBody;
import com.curefit.uas.pojo.fitnessReport.FitnessReportEntry;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import javax.ws.rs.BadRequestException;
import java.util.*;

@Service
@RequiredArgsConstructor
public class UpdateFitnessReportServiceFactory {
    final RollbarService rollbarService;
    final Set<UpdateFitnessReportService> updateFitnessReportServiceSet;
    
    private UpdateFitnessReportService getUpdateFitnessReportServiceForActivityType(ActivityType activityType) {
        for (UpdateFitnessReportService updateFitnessReportService : updateFitnessReportServiceSet) {
            if (updateFitnessReportService.isEligibleActivityType(activityType)) {
                return updateFitnessReportService;
            }
        }
        return null;
    }
    
    public FitnessReportEntry updateFitnessReport(ActivityType activityType, String sqsMsgBodyString, String logTag) throws Exception {
        UpdateFitnessReportService updateFitnessReportService = getUpdateFitnessReportServiceForActivityType(activityType);
        if (updateFitnessReportService == null) {
            rollbarService.error("No UpdateFitnessReportService found for activityType: " + activityType);
            throw new BadRequestException("No UpdateFitnessReportService found for activityType: " + activityType);
        }
        return updateFitnessReportService.processClass(sqsMsgBodyString, logTag);
    }
}
