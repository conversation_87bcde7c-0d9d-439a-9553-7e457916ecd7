package com.curefit.uas.services;

import com.curefit.base.enums.Tenant;
import com.curefit.membership.client.MembershipClient;
import com.curefit.membership.client.MembershipFilter;
import com.curefit.membership.pojo.entry.Membership;
import com.curefit.membership.pojo.entry.Pause;
import com.curefit.membership.types.Status;
import com.curefit.uas.utils.TimeUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class MembershipHelperService {
    MembershipClient membershipClient;
    ObjectMapper objectMapper;
    public static Long getUpdatedPauseEnd(Long actualEnd, Long end) {
        Long pauseEnd = actualEnd;
        if (pauseEnd == null || pauseEnd > end) {
            pauseEnd = end;
        }
        return pauseEnd - TimeUtil.ONE_HOUR_IN_MILLI_SECS;
    }
    public static Long getUpdatedPauseStartForStreak(Long createdOnTime, Long start, Long endMinus1Hour) {
        Long pauseStart = start;
        LocalDate pauseCreatedDate = TimeUtil.getLocalDateFromEpoch(createdOnTime);
        LocalDate pauseStartDate = TimeUtil.getLocalDateFromEpoch(start);
        LocalDate pauseEndDate = TimeUtil.getLocalDateFromEpoch(endMinus1Hour);
        boolean shouldStartPauseInAdvance = ChronoUnit.DAYS.between(pauseCreatedDate, pauseStartDate) <= 1 && ChronoUnit.DAYS.between(pauseCreatedDate, pauseEndDate) >= 1;
        if (shouldStartPauseInAdvance) {
            pauseStart = createdOnTime;
        }
        return pauseStart;
    }
    
    public boolean isAnyActiveMembershipPaused(String userId, String date) throws ExecutionException, InterruptedException {
        long startOfDayEpochMilli = TimeUtil.getStartOfDayEpoch(TimeUtil.getLocalDateFromString(date));
        long endOfDayEpochMilli = TimeUtil.getEndOfDayEpoch(TimeUtil.getLocalDateFromString(date));
        List<Status> statuses = Arrays.asList(Status.PURCHASED, Status.PAUSED);
        MembershipFilter membershipFilter = MembershipFilter.builder()
                .start(startOfDayEpochMilli)
                .end(endOfDayEpochMilli)
                .statuses(statuses)
                .build();
        List<Membership> memberships = membershipClient.getCachedMembershipsForUser(userId, Tenant.CUREFIT_APP.toString(), membershipFilter).get();
        List<List<Pause>> pauses = memberships.stream()
                .map(x -> membershipClient.getMembershipPauses(x.getId()))
                .filter(CollectionUtils::isNotEmpty)
                .toList();
        return pauses.stream().anyMatch(x -> {
            long pauseEnd = getUpdatedPauseEnd(x.get(0).getActualEnd(), x.get(0).getEnd());
            long pauseStart = getUpdatedPauseStartForStreak(x.get(0).getCreatedOn().getTime(), x.get(0).getStart(), pauseEnd);
            return (pauseStart <= startOfDayEpochMilli && pauseEnd >= startOfDayEpochMilli) || pauseStart <= endOfDayEpochMilli && pauseEnd >= endOfDayEpochMilli;
        });
    }
    
    public boolean isAnyEligibleMembershipActiveOnDateForUserStreak(String userId, String eligibilityDateString) {
        try {
            LocalDate eligibilityDate;
            if (StringUtils.isBlank(eligibilityDateString)) eligibilityDate = LocalDate.now();
            else eligibilityDate = TimeUtil.getLocalDateFromString(eligibilityDateString, null);
            
            // TODO:: ADD START AND END DATE TOO IN THE MEMBERSHIP FILTER
//            MembershipFilter membershipFilter = MembershipFilter.builder()
//                    .start().build();
            List<Membership> memberships = membershipClient.getCachedMembershipsForUser(userId, Tenant.CUREFIT_APP.toString(), new MembershipFilter()).get();
            return memberships.stream().anyMatch(membership -> {
                long startOfDayEpochMilli = TimeUtil.getStartOfDayEpoch(eligibilityDate);
                long endOfDayEpochMilli = TimeUtil.getStartOfDayEpoch(eligibilityDate);
                boolean isActive = ((membership.getStart() < startOfDayEpochMilli &&  membership.getEnd() > startOfDayEpochMilli)
                        || (membership.getStart() < endOfDayEpochMilli && membership.getEnd() > endOfDayEpochMilli)) && // membership ends after today
                        membership.getActivePause() == null; // membership is not paused. ToDo:: check for past pauses here too
                return
                        isActive &&
                        membership.getActivePause() == null; // membership is not paused. ToDo:: check for past pauses here too
            });
        } catch (ExecutionException | InterruptedException e) {
            log.error("StreakHelperService::isAnyEligibleMembershipActiveForUserStreak::Error while fetching memberships. userId={}", userId, e);
            return false;
        }
    }
    public List<LocalDate[]> findSortedUnionPausedDateRanges(Long userId, Long startTimeEpoch, Long endTimeEpoch) throws ExecutionException, InterruptedException, JsonProcessingException {
        List<Status> statuses = Arrays.asList(Status.PURCHASED, Status.PAUSED);
        MembershipFilter membershipFilter = MembershipFilter.builder()
                .start(startTimeEpoch)
                .end(endTimeEpoch)
                .statuses(statuses)
                .build();
        
        List<Membership> userMemberships = membershipClient.getCachedMembershipsForUser(
                String.valueOf(userId), Tenant.CUREFIT_APP.toString(), membershipFilter).get();
        
        List<List<Pause>> pauses = userMemberships.stream()
                .map(x -> membershipClient.getMembershipPauses(x.getId()))
                .filter(CollectionUtils::isNotEmpty)
                .toList();
        
        if (pauses.isEmpty()) return new ArrayList<>();
        
        // Step 1: Convert all pauses into individual LocalDates
        Set<LocalDate> pauseDates = pauses.stream()
                .flatMap(membershipPauses -> membershipPauses.stream()
                        .flatMap(pause -> {
                            Long pauseEnd = getUpdatedPauseEnd(pause.getActualEnd(), pause.getEnd());
                            Long pauseStart = getUpdatedPauseStartForStreak(pause.getCreatedOn().getTime(), pause.getStart(), pauseEnd);
                            LocalDate startDate = TimeUtil.getLocalDateFromEpoch(pauseStart);
                            LocalDate endDate = TimeUtil.getLocalDateFromEpoch(pauseEnd);
                            List<LocalDate> dates = new ArrayList<>();
                            while (!startDate.isAfter(endDate)) {
                                dates.add(startDate);
                                startDate = startDate.plusDays(1);
                            }
                            return dates.stream();
                        }))
                .collect(Collectors.toSet());
        
        if (pauseDates.isEmpty()) return new ArrayList<>();
        
        // Step 2: Sort and merge contiguous dates
        List<LocalDate> sortedDates = pauseDates.stream()
                .sorted()
                .toList();
        
        List<LocalDate[]> mergedDateRanges = new ArrayList<>();
        LocalDate rangeStart = sortedDates.get(0);
        LocalDate rangeEnd = rangeStart;
        
        for (int i = 1; i < sortedDates.size(); i++) {
            LocalDate current = sortedDates.get(i);
            if (current.equals(rangeEnd.plusDays(1))) {
                // Extend current range
                rangeEnd = current;
            } else {
                // Finalize current range
                mergedDateRanges.add(new LocalDate[]{rangeStart, rangeEnd});
                rangeStart = current;
                rangeEnd = current;
            }
        }
        // Add last range
        mergedDateRanges.add(new LocalDate[]{rangeStart, rangeEnd});
        
        return mergedDateRanges;
    }
    public List<long[]> findSortedUnionPausedIntervals(Long userId, Long startTimeEpoch, Long endTimeEpoch) throws ExecutionException, InterruptedException, JsonProcessingException {
        List<Status> statuses = Arrays.asList(Status.PURCHASED, Status.PAUSED);
        MembershipFilter membershipFilter = MembershipFilter.builder()
                .start(startTimeEpoch)
                .end(endTimeEpoch)
                .statuses(statuses)
                .build();
        
        List<Membership> userMemberships = membershipClient.getCachedMembershipsForUser(
                String.valueOf(userId), Tenant.CUREFIT_APP.toString(), membershipFilter).get();
        
        List<List<Pause>> pauses = userMemberships.stream()
                .map(x -> membershipClient.getMembershipPauses(x.getId()))
                .filter(CollectionUtils::isNotEmpty)
                .toList();
        
        if (pauses.isEmpty()) return new ArrayList<>();
        
        List<long[]> allPauseIntervals = pauses.stream()
                .flatMap(membershipPauses -> membershipPauses.stream()
                        .map(pause -> {
                            Long pauseEnd = getUpdatedPauseEnd(pause.getActualEnd(), pause.getEnd());
                            Long pauseStart = getUpdatedPauseStartForStreak(pause.getCreatedOn().getTime(), pause.getStart(), pauseEnd);
                            return new long[]{pauseStart, pauseEnd};
                        }))
                .sorted(Comparator.comparingLong(interval -> interval[0])) // sort by start time
                .toList();
        log.debug("pauseIntervals userId: {} epoch: {}", userId, objectMapper.writeValueAsString(allPauseIntervals));
        log.debug("pauseIntervals userId: {} dates: {}", userId, objectMapper.writeValueAsString(allPauseIntervals.stream()
                .map(y -> new String[]{TimeUtil.getLocalDateFromEpoch(y[0]).toString(), TimeUtil.getLocalDateFromEpoch(y[1]).toString()})
                .toList()));
        
        // Merge overlapping intervals
        List<long[]> merged = new ArrayList<>();
        long[] current = allPauseIntervals.get(0);
        
        for (int i = 1; i < allPauseIntervals.size(); i++) {
            long[] next = allPauseIntervals.get(i);
            
            if (current[1] >= next[0]) {
                // Overlapping or adjacent: merge
                current[1] = Math.max(current[1], next[1]);
            } else {
                merged.add(current);
                current = next;
            }
        }
        merged.add(current); // add the last one
        log.debug("pauseIntervals userId: {} merged dates: {}", userId, objectMapper.writeValueAsString(merged.stream()
                .map(y -> new String[]{TimeUtil.getLocalDateFromEpoch(y[0]).toString(), TimeUtil.getLocalDateFromEpoch(y[1]).toString()})
                .toList()));
        return merged;
    }
    //ToDo:: add a filter for benefits and activities
    public List<long[]> findSortedCommonPausedIntervals(Long userId, Long startTimeEpoch, Long endTimeEpoch) throws ExecutionException, InterruptedException, JsonProcessingException {
        List<Status> statuses = Arrays.asList(Status.PURCHASED, Status.PAUSED);
        MembershipFilter membershipFilter = MembershipFilter.builder()
                .start(startTimeEpoch) // Example start epoch time
                .end(endTimeEpoch)   // Example end epoch time
                .statuses(statuses)
                .build();
        List<Membership> userMemberships = membershipClient.getCachedMembershipsForUser(String.valueOf(userId), Tenant.CUREFIT_APP.toString(), membershipFilter).get();
        List<List<Pause>> pauses = userMemberships.stream().map(x -> membershipClient.getMembershipPauses(x.getId())).toList();
        pauses = pauses.stream().filter(CollectionUtils::isNotEmpty).toList();
        if (pauses.isEmpty()) return new ArrayList<>();
        
        // Sort each membership's intervals by start time
        for (List<Pause> membershipPauses : pauses) {
            membershipPauses.sort(Comparator.comparingLong(Pause::getStart));
        }
        List<List<long[]>> pauseIntervals = pauses.stream()
                .map(membershipPauses -> membershipPauses.stream()
                        .map(pause -> {
                            Long pauseEnd = getUpdatedPauseEnd(pause.getActualEnd(), pause.getEnd());
                            Long pauseStart = getUpdatedPauseStartForStreak(pause.getCreatedOn().getTime(), pause.getStart(), pauseEnd);
                            return new long[]{pauseStart, pauseEnd};
                        })
                        .toList())
                .toList();
        
        List<long[]> allMembershipsPausedIntervals = new ArrayList<>(pauseIntervals.get(0));
        // ToDo:: Check logic again
        for (int i = 1; i < pauseIntervals.size(); i++) {
            List<long[]> temp = new ArrayList<>();
            List<long[]> membershipIntervals = pauseIntervals.get(i);
            int m = 0, n = 0;
            
            while (m < allMembershipsPausedIntervals.size() && n < membershipIntervals.size()) {
                long[] interval1 = allMembershipsPausedIntervals.get(m);
                long[] interval2 = membershipIntervals.get(n);
                
                long overlapStart = Math.max(interval1[0], interval2[0]);
                long overlapEnd = Math.min(interval1[1], interval2[1]);
                
                if (overlapStart <= overlapEnd) {
                    temp.add(new long[]{overlapStart, overlapEnd});
                }
                
                if (interval1[1] < interval2[1]) {
                    m++;
                } else {
                    n++;
                }
            }
            
            allMembershipsPausedIntervals = temp;
            if (allMembershipsPausedIntervals.isEmpty()) break; // No common interval left, exit early
        }
        return allMembershipsPausedIntervals;
    }
    
}
