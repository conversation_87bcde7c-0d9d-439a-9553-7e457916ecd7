package com.curefit.uas.services.fitnessReportService;

import com.curefit.cult.models.FitnessReportSummaryPaginationResponse;
import com.curefit.cult.services.impl.CultServiceImpl;
import com.curefit.hamlet.models.request.HamletAllocationRequest;
import com.curefit.hamlet.service.impl.HamletService;
import com.curefit.hamlet.types.Bucket;
import com.curefit.uas.mapper.FitnessReportMapper;
import com.curefit.uas.responses.FitnessReportResponse;
import com.curefit.uas.responses.FitnessReportSummaryResponse;
import com.curefit.uas.responses.FitnessReportTotalClassesAttendedResponse;
import com.curefit.uas.services.ConfigStoreWrapperService;
import com.curefit.uas.utils.JavaClassUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

@RequiredArgsConstructor
@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class FitnessReportTransientService {
    final ConfigStoreWrapperService configStoreWrapperService;
    final ObjectMapper objectMapper;
    final CultServiceImpl cultClient;
    final FitnessReportService fitnessReportService;
    final HamletService hamletService;
    final FitnessReportMapper fitnessReportMapper;
    
    final static String FITNESS_REPORT_EXPERIMENT_ID = "2021";
    
    @PostConstruct
    public void postConstruct() {
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }
    
    private void checkAndLogDifferences(Long userId, String methodName, Object uasResponse, Object cultResponse) {
        if (cultResponse == null) return;
        CompletableFuture.runAsync(() -> {
            try {
                String cultApiResponseString = objectMapper.writeValueAsString(cultResponse);
                String uasResponseString = objectMapper.writeValueAsString(uasResponse);
                if (userId.equals(25441749L)) {
                    log.info("{}::userId={} cultApiResponseString={} uasResponseString={}", methodName, userId, cultApiResponseString, uasResponseString);
                }
                JsonNode uasResponseJsonNode = objectMapper.readTree(uasResponseString);
                JsonNode cultApiResponseJsonNode = objectMapper.readTree(cultApiResponseString);
                List<String> differences = JavaClassUtil.compare("", cultApiResponseJsonNode, uasResponseJsonNode, "$", getFieldNamesToIgnoreComparison());
                if(CollectionUtils.isNotEmpty(differences)) {
                    log.error("checkAndLogDifferences::Differences found in FitnessReportResponse method={} userId={} differences={}", methodName, userId, objectMapper.writeValueAsString(differences));
                } else {
                    log.debug("checkAndLogDifferences::Differences not found in FitnessReportResponse method={} userId={}", methodName, userId);
                }
            } catch(Exception ex) {
                log.error("{}::Method failed in FitnessReportResponse userId={} error={}", methodName, userId, ex.getMessage());
            }
        });
    }
    
    @Async
    public FitnessReportResponse getCachedWeeklyAugmentedResponseForUserAndWeekDate(Long userId, String dateInWeek) throws ExecutionException, InterruptedException {
        boolean isUserPartOfExperimentalRollout = isUserPartOfExperimentalRollout(userId);
        FitnessReportResponse response;
        if (isUserPartOfExperimentalRollout) {
            response = fitnessReportService.getCachedWeeklyAugmentedResponseForUserAndWeekDate(userId, dateInWeek);
        } else {
            com.curefit.cult.models.FitnessReportResponse cultApiResponse = cultClient.getWeeklyFitnessReport(dateInWeek, String.valueOf(userId), false).get();
            response = fitnessReportMapper.map(cultApiResponse);
        }
        
        if (isUserPartOfExperimentalRollout && shouldCompare()) {
            CompletableFuture.runAsync(() -> {
                com.curefit.cult.models.FitnessReportResponse cultApiResponse = null;
                try {
                    cultApiResponse = cultClient.getWeeklyFitnessReport(dateInWeek, String.valueOf(userId), false).get();
                } catch(InterruptedException | ExecutionException e) {
                    throw new RuntimeException(e);
                }
                checkAndLogDifferences(userId, "getCachedWeeklyAugmentedResponseForUserAndWeekDate", response, cultApiResponse);
            });
        }
        
        return response;
    }
    
    
    @Async
    public Map<String, FitnessReportResponse> getBulkResponseForUserIdsAndWeekDate(String weekDate, String commaSeparatedUserIds) {
        try {
            List<String> userIds = Arrays.stream(commaSeparatedUserIds.split(",")).toList();
            Map<String, com.curefit.cult.models.FitnessReportResponse> cultApiResponse = cultClient.getBulkWeeklyFitnessReport(weekDate, userIds).get();
            
            CompletableFuture.runAsync(() -> {
                Map<String, FitnessReportResponse> uasResponse = fitnessReportService.getBulkResponseForUserIdsAndWeekDate(weekDate, commaSeparatedUserIds);
                checkAndLogDifferences(0L, "getBulkResponseForUserIdsAndWeekDate", uasResponse, cultApiResponse);
            });

            return fitnessReportMapper.map(cultApiResponse);
        } catch(InterruptedException | ExecutionException e) {
            throw new RuntimeException(e);
        }
    }
    
    @Async
    public FitnessReportResponse getLatestReportForUserId(Long userId) throws ExecutionException, InterruptedException {
        boolean isUserPartOfExperimentalRollout = isUserPartOfExperimentalRollout(userId);
        FitnessReportResponse response;
        if (isUserPartOfExperimentalRollout) {
            response = fitnessReportService.getLatestReportForUserId(userId);
        } else {
            com.curefit.cult.models.FitnessReportResponse cultApiResponse = cultClient.getLatestWeeklyFitnessReport(userId.toString()).get();
            response = fitnessReportMapper.map(cultApiResponse);
        }
        
        if (isUserPartOfExperimentalRollout && shouldCompare()) {
            CompletableFuture.runAsync(() -> {
                com.curefit.cult.models.FitnessReportResponse cultApiResponse = null;
                try {
                    cultApiResponse = cultClient.getLatestWeeklyFitnessReport(userId.toString()).get();
                    checkAndLogDifferences(userId, "getLatestReportForUserId", response, cultApiResponse);
                } catch(InterruptedException | ExecutionException ignored) {
                }
            });
        }
        return response;
    }
    
    @Async
    public FitnessReportSummaryResponse getFitnessReportSummary(Long userId, String startDate, int limit, int offset) throws ExecutionException, InterruptedException {
        boolean isUserPartOfExperimentalRollout = isUserPartOfExperimentalRollout(userId);
        FitnessReportSummaryResponse response;
        if (isUserPartOfExperimentalRollout) {
            response = fitnessReportService.getFitnessReportSummary(userId, startDate, limit, offset);
        } else {
            FitnessReportSummaryPaginationResponse cultApiResponse = cultClient.getFitnessReportSummaryPaginated(startDate, userId.toString(), limit, offset);
            response = fitnessReportMapper.map(cultApiResponse);
        }
        
        if (isUserPartOfExperimentalRollout && shouldCompare()) {
            CompletableFuture.runAsync(() -> {
                FitnessReportSummaryPaginationResponse cultApiResponse = cultClient.getFitnessReportSummaryPaginated(startDate, userId.toString(), limit, offset);
                checkAndLogDifferences(userId, "getFitnessReportSummary", response, cultApiResponse);
            });
        }
        return response;
    }
    
    @Async
    public FitnessReportTotalClassesAttendedResponse getTotalClassesAttendedForLatestFitnessReportForUserIds(Integer pastNumberOfMonthsToQuery, List<Long> userIds) {
        try {
            com.curefit.cult.models.BulkFitnessReportTotalClassesAttendedMap cultApiResponse = cultClient.getTotalClassesAttendedForLatestFitnessReportForUserIds(userIds.stream().map(Long::intValue).toList()).get();
            
            CompletableFuture.runAsync(() -> {
                FitnessReportTotalClassesAttendedResponse uasResponse = fitnessReportService.getTotalClassesAttendedForLatestFitnessReportForUserIds(pastNumberOfMonthsToQuery, userIds);
                checkAndLogDifferences(0L, "getTotalClassesAttendedForLatestFitnessReportForUserIds", uasResponse, cultApiResponse);
                
            });
            
            return fitnessReportMapper.map(cultApiResponse);
        } catch(InterruptedException | ExecutionException e) {
            throw new RuntimeException(e);
        }
    }
    
    private boolean isUserPartOfExperimentalRollout(Long userId) {
        HamletAllocationRequest hamletAllocationRequest = new HamletAllocationRequest();
        hamletAllocationRequest.setUserId(String.valueOf(userId));
        hamletAllocationRequest.setExperimentIds(Set.of(FITNESS_REPORT_EXPERIMENT_ID));
        Bucket bucket = hamletService.getUserAllocations(hamletAllocationRequest).getBucketForExperiment(FITNESS_REPORT_EXPERIMENT_ID);
        return bucket != null && !bucket.isControlBucket();
    }
    
    private boolean shouldCompare() {
        Double compareRate = configStoreWrapperService.getFitnessReportResponseCompareRate();
        log.debug("checkAndLogDifferences::shouldCompare compareRate={}", compareRate);
        return Math.random() < compareRate;
    }
    
    private List<String> getFieldNamesToIgnoreComparison() {
        return configStoreWrapperService.getFieldNamesToIgnoreComparison();
    }
    
    
}
