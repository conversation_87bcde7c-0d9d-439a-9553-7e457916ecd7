package com.curefit.uas.services.fitnessReportMigration;

import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.uas.services.fitnessReportService.FitnessReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@RequiredArgsConstructor
@Component
@Slf4j
public class FitnessReportMigrationTaskManager {
    private volatile ExecutorService executorService;
    private final FitnessReportService fitnessReportService;
    private final RollbarService rollbarService;
    
    // Create the ExecutorService lazily
    private ExecutorService getExecutorService() {
        if (executorService == null || executorService.isShutdown() || executorService.isTerminated()) {
            synchronized (this) {
                executorService = Executors.newFixedThreadPool(5);
                log.info("ExecutorService created.");
            }
        }
        return executorService;
    }
    
    @PreDestroy
    public void cleanup() {
        if (executorService != null) {
            executorService.shutdown();
            log.info("ExecutorService shutdown.");
        }
    }
    
    public void submitFitnessReportMigrationTask(int iterations, long reqStartingId, long totalEntriesToMigrate, long batchSize, boolean ignoreExistingData) {
        FitnessReportMigrationTask task = new FitnessReportMigrationTask(iterations, reqStartingId, totalEntriesToMigrate, batchSize, ignoreExistingData, fitnessReportService, rollbarService);
        getExecutorService().submit(task);
        cleanup();
    }
    
}
