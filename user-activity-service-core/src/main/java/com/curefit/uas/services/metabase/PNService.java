package com.curefit.uas.services.metabase;

import com.curefit.base.enums.AppTenant;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.iris.models.SendCampaignNotificationsRequest;
import com.curefit.iris.models.UserContext;
import com.curefit.iris.services.spi.CampaignService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
@RequiredArgsConstructor
public class PNService {
    final AthenaService athenaService;
    final MetabaseService metabaseService;
    final CampaignService campaignService;
    final RollbarService rollbarService;
    
//    @Async
//    public void sendPNForMetabaseCard(String cardId) {
//        try {
//            List<SendCampaignNotificationsRequest> sendCampaignNotificationsRequestList = metabaseService.getPNRequestsFromMetabaseCard(cardId);
//
//            sendCampaignNotificationsRequestList.forEach(sendCampaignNotificationsRequest -> {
//                try {
//                    campaignService.sendCampaignMessages(sendCampaignNotificationsRequest, AppTenant.CUREFIT);
//                } catch(BaseException e) {
//                    log.error("sendPNForMetabaseQuery failed for cardId={} userId={} with error={}", cardId, sendCampaignNotificationsRequest.getUserContexts().get(0).getUserId(), e.getMessage());
//                }
//            });
//        } catch(Exception ex) {
//            rollbarService.error(ex, "sendPNForMetabaseCard failed for cardId=" + cardId);
//        }
//    }
    /**
     * Sends push notifications for a Metabase card based on the request body.
     * The request body should contain:
     * - campaignId: The ID of the campaign to send notifications for.
     * - commaSeparatedCreativeIds: A comma-separated string of creative IDs.
     * - userId: The ID of the user to send notifications to.
     *
     * @param requestBody The request body containing campaign and user details.
     */
    public void sendPNForMetabaseCard(Map<String, Object> requestBody) {
        try {
            String campaignId = (String) requestBody.get("campaignId");
            String creativeIds = (String) requestBody.get("commaSeparatedCreativeIds");
            String userId = (String) requestBody.get("userId");
            
            UserContext userContext = new UserContext();
            userContext.setUserId(userId);
            
            SendCampaignNotificationsRequest sendCampaignNotificationsRequest = new SendCampaignNotificationsRequest();
            sendCampaignNotificationsRequest.setUserContexts(List.of(userContext));sendCampaignNotificationsRequest.setCampaignId(campaignId);
            sendCampaignNotificationsRequest.setCreativeIds(
                    Arrays.stream(creativeIds.split(","))
                            .map(String::trim)
                            .toList()
            );
            campaignService.sendCampaignMessages(sendCampaignNotificationsRequest, AppTenant.CUREFIT);
        } catch (Exception ex) {
            rollbarService.error(ex);
        }
    }
}
