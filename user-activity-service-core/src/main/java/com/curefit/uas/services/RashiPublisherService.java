package com.curefit.uas.services;

import com.curefit.base.enums.AppTenant;
import com.curefit.common.data.exception.BaseException;
import com.curefit.rashi.client.RashiClient;
import com.curefit.rashi.enums.UserEventType;
import com.curefit.rashi.pojo.UserEventEntry;
import com.curefit.uas.entities.streaks.StreakEntity;
import com.curefit.uas.pojo.streak.activityDetails.BaseActivityDetails;
import com.curefit.uas.utils.TimeUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.json.simple.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Date;
import java.util.UUID;

@Service
@Slf4j
public class RashiPublisherService {
    @Autowired
    RashiClient rashiClient;
    
    @Autowired
    ObjectMapper objectMapper;
    
    @Async
    public void publishRashiForUserStreak(Long userId, StreakEntity streakEntity) {
        JSONObject body = new JSONObject();
        try {
            if (streakEntity == null) return;
            JSONObject deleteObject = new JSONObject();
            deleteObject.put("$delete", 1L);
            
            if (streakEntity.getFirstActivityDate() != null) {
                body.put("user_streak_first_activity_date", TimeUtil.getStartOfDayEpoch(streakEntity.getFirstActivityDate()));
            } else {
                body.put("user_streak_first_activity_date", deleteObject);
            }
            
            if (streakEntity.getLastActivityDate() != null) {
                body.put("user_streak_last_activity_date", TimeUtil.getStartOfDayEpoch(streakEntity.getLastActivityDate()));
            } else {
                body.put("user_streak_last_activity_date", deleteObject);
            }
            
            if (streakEntity.getStreakCount() > 0 && streakEntity.getTentativeStreakBreakDate() != null) {
                body.put("user_streak_break_date", TimeUtil.getStartOfDayEpoch(streakEntity.getTentativeStreakBreakDate()));
            }
            
            body.put("user_streak_paused", StringUtils.isNotBlank(streakEntity.getStreakPauseId()));
            body.put("user_streak_count", streakEntity.getStreakCount());
            body.put("user_streak_best_count", streakEntity.getMaxStreakCount());
            body.put("user_streak_version", streakEntity.getStreakVersion());
            if (!streakEntity.getStreakConfigId().equals("control_3_active_4_rest_days_with_membership_pause")) {
                rashiClient.publishUserEvent(UserEventType.USER_PROFILE_EVENT, "USER_STREAK_UPDATED", new Date(), String.valueOf(userId), body, AppTenant.CUREFIT, UUID.randomUUID().toString());
            }
        } catch (Exception ex) {
            try {
                log.error("Error while publishing rashi event for publishRashiForUserStreak for user={} with error = {} for streakEntity={}", userId, ex.getMessage(), objectMapper.writeValueAsString(streakEntity));
            } catch(JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }
    }
    
    
    @Async
    public void publishRashiForUserStreakPause(Long userId, StreakEntity streakEntity, LocalDate pauseDate, LocalDate unpauseDate) {
        try {
            publishRashiForUserStreakPauseStartAndEnd(userId, streakEntity, pauseDate, unpauseDate);
            
            JSONObject eventBody = new JSONObject();
            eventBody.put("userId", userId);
            eventBody.put("pauseDate", TimeUtil.getStartOfDayEpoch(pauseDate));
            eventBody.put("unpauseDate", TimeUtil.getStartOfDayEpoch(unpauseDate));
            UserEventEntry userEventEntry = new UserEventEntry(
                    userId,
                    UserEventType.USER_ACTIVITY_EVENT,
                    "USER_STREAK_PAUSED",
                    new Date(),
                    eventBody,
                    AppTenant.CUREFIT
            );
            if (!streakEntity.getStreakConfigId().equals("control_3_active_4_rest_days_with_membership_pause")) {
                this.rashiClient.publishUserEvent(userEventEntry, AppTenant.CUREFIT, null);
            }
        } catch(Exception ex) {
            log.error("Error while publishing rashi event for publishRashiForUserStreakPause = {}", ex.getMessage());
        }
    }
    
    @Async
    public void publishRashiForUserStreakUnPause(Long userId, StreakEntity streakEntity, LocalDate pauseDate, LocalDate unpauseDate) {
        try {
            publishRashiForUserStreakPauseStartAndEnd(userId, streakEntity, pauseDate, unpauseDate);
            
            JSONObject eventBody = new JSONObject();
            eventBody.put("userId", userId);
            eventBody.put("pauseDate", TimeUtil.getStartOfDayEpoch(pauseDate));
            eventBody.put("unpauseDate", TimeUtil.getStartOfDayEpoch(unpauseDate));
            UserEventEntry userEventEntry = new UserEventEntry(
                    userId,
                    UserEventType.USER_ACTIVITY_EVENT,
                    "USER_STREAK_UNPAUSED",
                    new Date(),
                    eventBody,
                    AppTenant.CUREFIT
            );
            if (!streakEntity.getStreakConfigId().equals("control_3_active_4_rest_days_with_membership_pause")) {
                this.rashiClient.publishUserEvent(userEventEntry, AppTenant.CUREFIT, null);
            }
        } catch(Exception ex) {
            log.error("Error while publishing rashi event for publishRashiForUserStreakUnPause = {}", ex.getMessage());
        }
    }
    
    private void publishRashiForUserStreakPauseStartAndEnd(Long userId, StreakEntity streakEntity, LocalDate pauseDate, LocalDate unpauseDate) throws BaseException, JsonProcessingException {
        JSONObject body = new JSONObject();
        body.put("user_streak_streak_pause_date", TimeUtil.getStartOfDayEpoch(pauseDate));
        body.put("user_streak_streak_unpause_date", TimeUtil.getStartOfDayEpoch(unpauseDate));
        if (!streakEntity.getStreakConfigId().equals("control_3_active_4_rest_days_with_membership_pause")) {
            rashiClient.publishUserEvent(UserEventType.USER_PROFILE_EVENT, "USER_STREAK_UPDATED", new Date(), String.valueOf(userId), body, AppTenant.CUREFIT, UUID.randomUUID().toString());
        }
    }
    
    @Async
    public void publishRashiEventForFirstUserStreak(Long userId, BaseActivityDetails streakFirstActivity, String streakConfigId) {
        try {
            JSONObject eventBody = new JSONObject();
            eventBody.put("userId", userId);
            eventBody.put("firstActivityIdempotenceKey", streakFirstActivity.getIdempotenceKey());
            eventBody.put("startDate", TimeUtil.getStartOfDayEpoch(streakFirstActivity.getDate()));
            eventBody.put("firstActivityType", streakFirstActivity.getActivityType());
            UserEventEntry userEventEntry = new UserEventEntry(
                    userId,
                    UserEventType.USER_ACTIVITY_EVENT,
                    "USER_STREAK_FIRST_STREAK",
                    new Date(),
                    eventBody,
                    AppTenant.CUREFIT
            );
            if (!streakConfigId.equals("control_3_active_4_rest_days_with_membership_pause")) {
                this.rashiClient.publishUserEvent(userEventEntry, AppTenant.CUREFIT, null);
            }
        } catch(Exception ex) {
            log.error("Error while publishing rashi event for publishRashiEventForFirstUserStreak = {}", ex.getMessage());
        }
    }
    
    @Async
    public void publishRashiEventForNewUserStreak(Long userId, BaseActivityDetails newStreakFirstActivity, int newStreakVersion, String streakConfigId) {
        try {
            JSONObject eventBody = new JSONObject();
            eventBody.put("userId", userId);
            eventBody.put("firstActivityIdempotenceKey", newStreakFirstActivity.getIdempotenceKey());
            eventBody.put("startDate", TimeUtil.getStartOfDayEpoch(newStreakFirstActivity.getDate()));
            eventBody.put("firstActivityType", newStreakFirstActivity.getActivityType());
            eventBody.put("newStreakVersion", newStreakVersion);
            UserEventEntry userEventEntry = new UserEventEntry(
                    userId,
                    UserEventType.USER_ACTIVITY_EVENT,
                    "USER_STREAK_NEW_STREAK",
                    new Date(),
                    eventBody,
                    AppTenant.CUREFIT
            );
            if (!streakConfigId.equals("control_3_active_4_rest_days_with_membership_pause")) {
                this.rashiClient.publishUserEvent(userEventEntry, AppTenant.CUREFIT, null);
            }
        } catch(Exception ex) {
            log.error("Error while publishing rashi event for publishRashiEventForFirstUserStreak = {}", ex.getMessage());
        }
    }
    
//    @Async
//    public void publishRashiEventForStreakBroken(@NonNull  StreakEntity oldStreak) {
//        try {
//            JSONObject eventBody = new JSONObject();
//            eventBody.put("userId", oldStreak.getUserId());
//            eventBody.put("streakEndDate", oldStreak.getTentativeStreakBreakDate());
//            eventBody.put("streakVersion", oldStreak.getStreakVersion());
//            eventBody.put("streakCount", oldStreak.getStreakCount());
//            eventBody.put("firstActivityDate", oldStreak.getFirstActivityDate());
//            eventBody.put("lastActivityDate", oldStreak.getLastActivityDate());
//
//            UserEventEntry userEventEntry = new UserEventEntry(
//                    oldStreak.getUserId(),
//                    UserEventType.USER_ACTIVITY_EVENT,
//                    "USER_STREAK_BROKEN",
//                    new Date(),
//                    eventBody,
//                    AppTenant.CUREFIT
//            );
//            this.rashiClient.publishUserEvent(userEventEntry, AppTenant.CUREFIT, null);
//        } catch(Exception ex) {
//            log.error("Error while publishing rashi event for publishRashiEventForStreakBroken = {}", ex.getMessage());
//        }
//    }
}
