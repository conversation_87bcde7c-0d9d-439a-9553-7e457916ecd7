package com.curefit.uas.services.fitnessReportService;

import com.curefit.common.data.exception.BaseException;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.lock.LockService;
import com.curefit.cult.models.ClassDetails;
import com.curefit.cult.models.WorkoutCount;
import com.curefit.cult.models.WorkoutDetails;
import com.curefit.hercules.client.HerculesService;
import com.curefit.hercules.pojo.BodyPart;
import com.curefit.metricservice.client.MetricClient;
import com.curefit.metricservice.models.UserMetricValue;
import com.curefit.uas.constants.AppConfigKey;
import com.curefit.uas.constants.LockKey;
import com.curefit.uas.entities.FitnessReportEntity;
import com.curefit.uas.mapper.CommonMapper;
import com.curefit.uas.pojo.fitnessReport.FitnessReportBenefit;
import com.curefit.uas.pojo.fitnessReport.FitnessReportBodyPart;
import com.curefit.uas.pojo.fitnessReport.FitnessReportInterval;
import com.curefit.uas.pojo.fitnessReport.FitnessReportSubData;
import com.curefit.uas.repository.mongo.FitnessReportRepository;
import com.curefit.uas.services.ConfigStoreWrapperService;
import com.curefit.uas.utils.FitnessReportConstants;
import com.curefit.uas.utils.TimeUtil;
import com.curefit.ufs.pojo.response.UserWodDetailForFitnessReportResponse;
import com.curefit.ufs.services.UfsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.stream.Stream;

@Slf4j
@Service
@RequiredArgsConstructor
public class UpdateFitnessReportHelperService {
    private final HerculesService herculesService;
    private final ConfigStoreWrapperService configStore;
    private final RollbarService rollbarService;
    private final MetricClient metricClient;
    private final UfsService ufsService;
    private final LockService lockService;
    private final FitnessReportRepository fitnessReportRepository;
    private final CommonMapper commonMapper;
    
    List<FitnessReportEntity> getPreviousNEntitiesSortedNewToOld(Long userId, String weekMonday, int limit) {
        Pageable pageable = PageRequest.of(0, limit, Sort.by(Sort.Order.desc("startDate")));
        return fitnessReportRepository.findAllForUserIdAndStartDateLessThanEqual(userId, weekMonday, FitnessReportInterval.WEEKLY, pageable);
    }
   
    boolean isValidWodId(String wodId) {
        try {
            return herculesService.simpleWodClient().getSimpleWodById(wodId) != null;
        } catch(BaseException ignored) {
            return false;
        }
    }
    FitnessReportSubData getUFSPlanDataForFitnessReport(Long userId, FitnessReportSubData dataToBeProcessed, ClassDetails classDetails) throws Exception {
        List<FitnessReportBenefit> benefits = new ArrayList<>();
        List<FitnessReportBodyPart> bodyParts = new ArrayList<>();
        List<WorkoutCount> workoutCounts = new ArrayList<>();
        // Doubt: What does this note mean in cult-api?
        // NOTE: Adding 6h to Handle Edge Case from User Fitness Service
        long currentDateEpochTime = TimeUtil.getEpochFromDate(classDetails.getDate()) + FitnessReportConstants.SIX_HOURS_EPOCH_TIME_DIFFERENCE;
        UserWodDetailForFitnessReportResponse userWodDetailForFitnessReportResponse = ufsService.userWod().getUserWodDetailsForFitnessReport(String.valueOf(userId), "1", String.valueOf(currentDateEpochTime)).get();
        List<FitnessReportBodyPart> defaultBodyParts = configStore.getFitnessReportBodyParts();
        
        if (userWodDetailForFitnessReportResponse.getUserWodAvailable()) {
            Map<String, List<String>> userFitnessServiceGoalIdToBenefitMap = configStore.getUserFitnessServiceGoalIdToBenefits();
            String goalId = userWodDetailForFitnessReportResponse.getGoalEntry().getId().toString();
            List<String> benefitsNames = userFitnessServiceGoalIdToBenefitMap.get(goalId);
            if (benefitsNames == null) {
                throw new Exception("benefitsNames is null for GoalId " + goalId + " in Fitness Report Processing");
            }
            benefits = getUpdatedBenefitsCount(dataToBeProcessed != null && dataToBeProcessed.getBenefits() != null ? dataToBeProcessed.getBenefits() : new ArrayList<>(), benefitsNames);
            List<FitnessReportBodyPart> classBodyParts = userWodDetailForFitnessReportResponse.getBodyParts().stream().map(x -> new FitnessReportBodyPart(x.get_id(), x.getName(), x.getName(), x.isActive(), x.isDisplay())).toList();
            bodyParts = getUpdatedBodyParts(dataToBeProcessed != null && dataToBeProcessed.getBodyParts() != null ? dataToBeProcessed.getBodyParts() : defaultBodyParts, classBodyParts);
            WorkoutDetails workoutDetail = new WorkoutDetails();
            workoutDetail.setId(FitnessReportConstants.FITNESS_REPORT_GYMFIT_ID);
            workoutDetail.setName(userWodDetailForFitnessReportResponse.getWorkoutName());
            
            workoutCounts = FitnessReportUtils.generateWorkoutCounts(dataToBeProcessed != null && dataToBeProcessed.getWorkoutCounts() != null ? dataToBeProcessed.getWorkoutCounts() : new ArrayList<>(), workoutDetail, null);
        }
        
        return new FitnessReportSubData(workoutCounts != null ? workoutCounts : new ArrayList<>(),
                benefits != null ? benefits : new ArrayList<>(),
                bodyParts != null ? bodyParts : new ArrayList<>());
    }
    
    List<Object> getUpdatedClassListForUFSWodCompletion(List<Object> currentClassList, ClassDetails classDetails, String wodIdString) {
        String classId = classDetails.getId().toString();
        return currentClassList.stream()
                .map(item -> {
                    if (item instanceof String) {
                        String itemStr = item.toString();
                        return itemStr.contains(classId) ? itemStr + wodIdString : itemStr;
                    } else return item;
                })
                .toList();
    }
    
    List<FitnessReportEntity> getNextEntries(Long userId, String weekMonday) {
        Pageable pageable = PageRequest.of(0, Integer.MAX_VALUE, Sort.by(Sort.Order.asc("startDate")));
        return fitnessReportRepository.findEntriesByUserIdAfterStartDate(userId, weekMonday, FitnessReportInterval.WEEKLY, pageable);
    }
    void propagateClassBackfillToNextWeek(FitnessReportEntity currentWeek, List<FitnessReportEntity> nextEntries, Long userId) throws IllegalAccessException {
        if (CollectionUtils.isEmpty(nextEntries)) {
            return;
        }
        boolean isStreakBroken = false;
        String nextWeekMonday = TimeUtil.getMondayOfNextWeek(currentWeek.getStartDate());
        FitnessReportEntity nextWeek = FitnessReportUtils.getMatchingEntryWithStartDate(nextEntries, nextWeekMonday);
        if (nextWeek == null) {
            isStreakBroken = true;
            nextWeek = FitnessReportUtils.getEntryOnOrAfter(nextEntries, nextWeekMonday);
        }
        String lockKey = LockKey.FITNESS_REPORT_USER_INTERVAL_START_DATE.getValue() + userId + "_" + FitnessReportInterval.WEEKLY.toString() + "_" + nextWeek.getStartDate(),
                lock = null;
        try {
            lock = lockService.acquire(lockKey).get();
            nextWeek = fitnessReportRepository.findByIdSecondaryRead(nextWeek.getId());
            
            int streak = nextWeek.getStreak();
            if (!isStreakBroken) {
                streak = currentWeek.getStreak() + 1;
            }
            nextWeek.setTotalClassesAttended(nextWeek.getTotalClassesAttended() + 1);
            nextWeek.setMaxClassesAttended(Math.max(currentWeek.getMaxClassesAttended(), nextWeek.getMaxClassesAttended()));
            nextWeek.setStreak(streak);
            nextWeek.setMaxWeeklyStreak(Math.max(streak, Math.max(nextWeek.getMaxWeeklyStreak(), currentWeek.getMaxWeeklyStreak())));
            fitnessReportRepository.save(nextWeek);
            propagateClassBackfillToNextWeek(nextWeek, nextEntries.subList(1, nextEntries.size()),userId);
        } catch (Exception e) {
            log.error("Error while back-filling FitnessReport for user {}, reportId {}", userId, nextWeek.getId(), e);
            throw e;
        } finally {
            if (StringUtils.isNotBlank(lock)) {
                lockService.release(lockKey, lock);
            }
        }
    }
    public float safeGetMetValue(String wodId) {
        float defaultMetValue = 3.0f;
        try {
            if (StringUtils.isBlank(wodId)) {
                return defaultMetValue;
            }
            float metValue = Float.parseFloat(herculesService.simpleWodClient().getCombinedMetValueBySimpleWodId(wodId).getMetValueCount());
            String errorMsg = metValue == 0 ? ("0 met value for wodId" + wodId) : (metValue > 10 ? ("very high met value for wodId" + wodId) : null);
            if (errorMsg != null) {
                log.error(errorMsg);
//                boolean logRollbarForFitnessReport = configStore.getBooleanValueForConfigKey(AppConfigKey.FITNESS_REPORT_ENABLE_ROLLBAR, false);
//                if (logRollbarForFitnessReport) {
//                    rollbarService.error(errorMsg);
//                }
            }
            return metValue;
        } catch(Exception e) {
            String errorMsg = "Error in safeGetMetValue for wodId = " + wodId;
            log.error("{}{}", errorMsg, e.getMessage(), e);
            rollbarService.error(e, errorMsg);
            return defaultMetValue;
        }
    }
    public UserMetricValue getMetricValue(Long userId, long metricId) {
        try {
            return metricClient.getLatestUserMetricValue(String.valueOf(userId), metricId);
        } catch(Exception e) {
            log.error("Error in getMetricValue for userId={} metricId={} is {}", userId, metricId, e.getMessage());
        }
        return null;
    }

    // This logs gym workout or if wod completed event is raised(with checkinId)
    private FitnessReportSubData getGymfitDataForFitnessReport(Long userId, FitnessReportEntity fitnessReportEntry, WorkoutDetails workoutDetails, ClassDetails classDetails, String ufsCompletedWodId) throws Exception {
        List<FitnessReportBodyPart> defaultBodyParts = configStore.getFitnessReportBodyParts();
        List<String> benefitsNames = configStore.getListOfStringsValueForConfigKey(AppConfigKey.GYM_WORKOUT_BENEFITS, new ArrayList<>());
        List<FitnessReportBenefit> benefits = getUpdatedBenefitsCount(fitnessReportEntry != null && fitnessReportEntry.getBenefits() != null ? fitnessReportEntry.getBenefits() : new ArrayList<>(), benefitsNames);
        List<FitnessReportBodyPart> bodyParts = getBodyParts(fitnessReportEntry != null && fitnessReportEntry.getBodyParts() != null ? fitnessReportEntry.getBodyParts() : defaultBodyParts, classDetails.getWodId());
        List<WorkoutCount> workoutCounts = FitnessReportUtils.generateWorkoutCounts(fitnessReportEntry != null && fitnessReportEntry.getWorkoutCounts() != null ? fitnessReportEntry.getWorkoutCounts() : new ArrayList<>(), workoutDetails, classDetails);
        FitnessReportSubData fitnessReportSubData = new FitnessReportSubData(workoutCounts, benefits, bodyParts);
        if (StringUtils.isNotBlank(ufsCompletedWodId)) {
//            if (fitnessReportEntry == null) {
//                fitnessReportEntry = new FitnessReportEntity(new ArrayList<>(), new ArrayList<>(), new ArrayList<>());
//            }
            FitnessReportSubData dataToBeProcessed = new FitnessReportSubData(new ArrayList<>(), new ArrayList<>(), new ArrayList<>());
            if (fitnessReportEntry != null) {
                dataToBeProcessed.setBenefits(fitnessReportEntry.getBenefits());
                dataToBeProcessed.setWorkoutCounts(fitnessReportEntry.getWorkoutCounts());
                dataToBeProcessed.setBodyParts(fitnessReportEntry.getBodyParts());
            }
            if (benefits != null) {
                dataToBeProcessed.setBenefits(benefits);
            }
            if (workoutCounts != null) {
                dataToBeProcessed.setWorkoutCounts(workoutCounts);
            }
            dataToBeProcessed.setBodyParts(bodyParts);
            
            fitnessReportSubData = getUFSPlanDataForFitnessReport(userId, dataToBeProcessed, classDetails);
        }
        return fitnessReportSubData;
    }
    public FitnessReportSubData getDataForFitnessReport(Long userId, WorkoutDetails workoutDetails, FitnessReportEntity fitnessReportEntity,
                                                        ClassDetails classDetails, String ufsCompletedWodId) throws Exception {
        if (workoutDetails != null) {
            if (FitnessReportConstants.FITNESS_REPORT_GYMFIT_ID.equals(workoutDetails.getId())) {
                return getGymfitDataForFitnessReport(userId, fitnessReportEntity, workoutDetails, classDetails, ufsCompletedWodId);
            } else if (FitnessReportConstants.FITNESS_REPORT_STEPS_ID.equals(workoutDetails.getId())) {
                return getStepsDataForFitnessReport(fitnessReportEntity, workoutDetails, classDetails);
            }
        }
        List<FitnessReportBodyPart> defaultBodyParts = configStore.getFitnessReportBodyParts();
        List<FitnessReportBenefit> benefits = getBenefits(fitnessReportEntity != null && fitnessReportEntity.getBenefits() != null ? fitnessReportEntity.getBenefits() : new ArrayList<>(), classDetails.getWodId(), workoutDetails);
        List<FitnessReportBodyPart> bodyParts = getBodyParts(fitnessReportEntity != null && fitnessReportEntity.getBodyParts() != null ? fitnessReportEntity.getBodyParts() : defaultBodyParts, classDetails.getWodId());
        List<WorkoutCount> workoutCounts = FitnessReportUtils.generateWorkoutCounts(fitnessReportEntity != null && fitnessReportEntity.getWorkoutCounts() != null ? fitnessReportEntity.getWorkoutCounts() : new ArrayList<>(), workoutDetails, classDetails);
        return new FitnessReportSubData(workoutCounts, benefits, bodyParts);
    }
    private FitnessReportSubData getStepsDataForFitnessReport(FitnessReportEntity fitnessReportEntry, WorkoutDetails workoutDetails, ClassDetails classDetails) throws Exception {
        List<FitnessReportBodyPart> defaultBodyParts = configStore.getFitnessReportBodyParts();
        List<String> benefitsNames = configStore.getListOfStringsValueForConfigKey(AppConfigKey.STEP_BENEFITS, new ArrayList<>());
        List<FitnessReportBenefit> benefits = getUpdatedBenefitsCount(fitnessReportEntry != null && fitnessReportEntry.getBenefits() != null ? fitnessReportEntry.getBenefits() : new ArrayList<>(), benefitsNames);
        List<FitnessReportBodyPart> bodyParts = getBodyParts(fitnessReportEntry != null && fitnessReportEntry.getBodyParts() != null ? fitnessReportEntry.getBodyParts() : defaultBodyParts, FitnessReportConstants.FITNESS_REPORT_STEPS_DEFAULT_WOD_ID);
        List<WorkoutCount> workoutCounts = FitnessReportUtils.generateWorkoutCounts(fitnessReportEntry != null && fitnessReportEntry.getWorkoutCounts() != null ? fitnessReportEntry.getWorkoutCounts() : new ArrayList<>(), workoutDetails, classDetails);
        return new FitnessReportSubData(workoutCounts, benefits, bodyParts);
    }
    private static List<FitnessReportBenefit> getUpdatedBenefitsCount(List<FitnessReportBenefit> benefits, List<String> classBenefits) {
        for (String classBenefit : classBenefits) {
            Optional<FitnessReportBenefit> benefitEntry = benefits.stream()
                    .filter(benefit -> benefit.getName().equals(classBenefit))
                    .findFirst();
            if (benefitEntry.isPresent()) {
                FitnessReportBenefit entry = benefitEntry.get();
                entry.setCount(entry.getCount()+1);
                entry.setName(classBenefit);
            } else {
                benefits.add(new FitnessReportBenefit(classBenefit, 1));
            }
        }
        benefits.sort((a, b) -> Integer.compare(b.getCount(), a.getCount()));
        return benefits;
    }
    private List<FitnessReportBenefit> getBenefits(List<FitnessReportBenefit> benefits, String wodId, WorkoutDetails workoutDetails) {
        // ToDo: The return type should be List<BenefitType> only and this mapping should not be required. Let's check during testing
        List<String> classBenefits = safeGetBenefitTitles(wodId);
        if (StringUtils.isBlank(wodId) && workoutDetails != null && FitnessReportConstants.FITNESS_REPORT_GYMFIT_ID.equals(workoutDetails.getId())) {
            classBenefits = configStore.getListOfStringsValueForConfigKey(AppConfigKey.GYM_WORKOUT_BENEFITS, new ArrayList<>());
        }
        List<String> finalClassBenefits = classBenefits;
        return getUpdatedBenefitsCount(benefits, finalClassBenefits);
    }
    private List<FitnessReportBodyPart> getUpdatedBodyParts(List<FitnessReportBodyPart> bodyParts, List<FitnessReportBodyPart> classBodyParts) throws NullPointerException {
        Map<String, List<FitnessReportBodyPart>> musclesToBodyPartMap = configStore.getFitnessReportPrimaryMusclesToBodyPartsMap();
        classBodyParts = classBodyParts.stream()
                .flatMap(cbp -> {
                    List<FitnessReportBodyPart> mappedBodyParts = musclesToBodyPartMap.get(cbp.get_id());
                    if (mappedBodyParts != null && !bodyParts.isEmpty()) {
                        return mappedBodyParts.stream();
                    }
                    return Stream.of(cbp);
                })
                .toList();
        
        List<FitnessReportBodyPart> finalClassBodyParts = classBodyParts;
        bodyParts.forEach(bodyPart -> {
            if (finalClassBodyParts.stream().anyMatch((cbp -> cbp.get_id().equals(bodyPart.get_id())))) {
                bodyPart.setIsActive(true);
            }
        });
        String fullBodyId = configStore.getStringValueForConfigKey(AppConfigKey.FITNESS_REPORT_FULL_BODY_ID, "");
        long fullBodyCount = bodyParts.stream().filter(bp -> bp.get_id().equals(fullBodyId) && bp.getIsActive()).count();
        long nonFullBodyCount = bodyParts.stream().filter(bp -> !bp.get_id().equals(fullBodyId) && bp.getIsActive()).count();
        if (nonFullBodyCount > 0) {
            bodyParts.forEach(bp -> {
                bp.setDisplay(!bp.get_id().equals(fullBodyId));
            });
        } else if (fullBodyCount > 0) {
            bodyParts.forEach(bp -> {
                bp.setDisplay(bp.get_id().equals(fullBodyId));
            });
        }
        bodyParts.sort((a,b) -> Boolean.compare(b.getIsActive(), a.getIsActive()));
        return bodyParts;
    }
    
    private List<FitnessReportBodyPart> getBodyParts(List<FitnessReportBodyPart> bodyParts, String wodId) throws NullPointerException, ExecutionException, InterruptedException {
        return getUpdatedBodyParts(bodyParts, safeGetBodyParts(wodId));
    }
    
    private List<FitnessReportBodyPart> safeGetBodyParts(String wodId) {
        List<FitnessReportBodyPart> defaultBodyParts = new ArrayList<>();
        try {
            if (StringUtils.isBlank(wodId)) {
                return defaultBodyParts;
            }
            List<BodyPart> bodyParts = herculesService.simpleWodClient().getBodyPartsByWodId(wodId);
            if (bodyParts.isEmpty()) {
                String errorMsg = "empty body parts for wodId " + wodId;
                log.warn(errorMsg);
            }
            return bodyParts.stream().map(commonMapper::transform).toList();
        } catch (Exception e) {
            String errorMsg = "Error in safeGetBodyParts for wodId = " + wodId;
            log.error(errorMsg, e);
            rollbarService.error(e, errorMsg);
            return defaultBodyParts;
        }
    }
    
    
    private List<String> safeGetBenefitTitles(String wodId) {
        List<String> defaultBenefits = new ArrayList<>();
        try {
            if (StringUtils.isBlank(wodId)) {
                return defaultBenefits;
            }
            List<String> benefits = herculesService.simpleWodClient().getBenefitTitlesBySimpleWodId(wodId);
            if (benefits.isEmpty()) {
                String errorMsg = "empty benefits for wodId " + wodId;
                log.warn(errorMsg);
            }
            return benefits;
        } catch (Exception e) {
            String errorMsg = "Error in safeGetBenefits for wodId = " + wodId;
            log.error(errorMsg, e);
            rollbarService.error(e, errorMsg);
            return defaultBenefits;
        }
    }
    
}
