package com.curefit.uas.services.fitnessReportService;

import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.store.KeyValueStore;
import com.curefit.uas.mapper.FitnessReportMapper;
import com.curefit.uas.pojo.fitnessReport.FitnessReportEntry;
import com.curefit.uas.pojo.fitnessReport.FitnessReportInterval;
import com.curefit.uas.repository.mongo.FitnessReportRepository;
import com.curefit.uas.responses.FitnessReportResponse;
import com.curefit.uas.services.ConfigStoreWrapperService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Slf4j
public class FitnessReportCacheService {
    final FitnessReportRepository fitnessReportRepository;
    final UpdateFitnessReportHelperService updateFitnessReportHelperService;
    final FitnessReportMapper fitnessReportMapper;
    final ObjectMapper objectMapper;
    final RollbarService rollbarService;
    final ConfigStoreWrapperService configStoreWrapperService;
    KeyValueStore keyValueStore;
    FitnessReportService fitnessReportService;
    
    
    @Autowired
    @Qualifier("defaultRedisKeyValueStore")
    public void setKeyValueStore(KeyValueStore keyValueStore) {
        this.keyValueStore = keyValueStore;
    }
    
    @Autowired
    public void setFitnessReportService(FitnessReportService fitnessReportService) { this.fitnessReportService = fitnessReportService; }
    
    @CacheEvict(
            value = "FitnessReportService-getCachedWeeklyAugmentedResponseForUserAndWeekMonday",
            cacheManager = "fitnessReportCacheManager",
            key = "#userId + '_' + #mondayOfWeek")
    public static void evictCachedWeeklyAugmentedResponseForUserAndWeekMonday(String userId, String mondayOfWeek) {
        // This method will evict the cache entry
    }
    
    @Cacheable(
            value = "FitnessReportService-getCachedWeeklyAugmentedResponseForUserAndWeekMonday",
            cacheManager = "fitnessReportCacheManager",
            key = "#userId" + "_" + "#mondayOfWeek")
    FitnessReportResponse getCachedWeeklyAugmentedResponseForUserAndWeekMonday(long userId, String mondayOfWeek) {
        FitnessReportEntry fitnessReportEntry = fitnessReportMapper.map(
                fitnessReportRepository.findAllByUserIdAndStartDateAndInterval(userId, mondayOfWeek, FitnessReportInterval.WEEKLY)
        );
        FitnessReportResponse fitnessReportResponse = FitnessReportResponse.builder()
                .fitnessReport(fitnessReportEntry)
                .build();
        fitnessReportService.augmentFitnessReportResponse(fitnessReportResponse, userId, mondayOfWeek);
        return fitnessReportResponse;
    }
    
    
    
    
}
