package com.curefit.uas.services.streak;

import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.hamlet.models.request.HamletAllocationRequest;
import com.curefit.hamlet.service.impl.HamletService;
import com.curefit.hamlet.types.Bucket;
import com.curefit.logging.commons.MongoReadPref;
import com.curefit.logging.models.ActivityDS;
import com.curefit.logging.models.ActivityTypeDS;
import com.curefit.logging.models.request.ActivityDSSearchRequest;
import com.curefit.logging.models.request.DateQueryRange;
import com.curefit.logging.models.request.SortField;
import com.curefit.logging.service.impl.LoggingService;
import com.curefit.membership.types.MembershipEvent;
import com.curefit.segmentation.client.cache.SegmentationCacheClient;
import com.curefit.segmentation.client.pojo.SegmentSet;
import com.curefit.uas.entities.streaks.*;
import com.curefit.uas.enums.streaks.StreakRestDaysResetInterval;
import com.curefit.uas.mapper.StreakMapper;
import com.curefit.uas.pojo.StreakActivityRewardConfig;
import com.curefit.uas.pojo.streak.activityDetails.BaseActivityDetails;
import com.curefit.uas.repository.mongo.streaks.*;
import com.curefit.uas.responses.StreakActivityResponse;
import com.curefit.uas.services.ConfigStoreWrapperService;
import com.curefit.uas.services.MembershipHelperService;
import com.curefit.uas.services.RashiPublisherService;
import com.curefit.uas.utils.TimeUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import javassist.NotFoundException;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class StreakHelperService {
    ConfigStoreWrapperService configStore;
    SegmentationCacheClient segmentationCacheClient;
    LoggingService loggingService;
    StreakMapper streakMapper;
    
    IStreakRepository streakRepository;
    IStreakConfigRepository streakConfigRepository;
    IStreakPauseRepository streakPauseRepository;
    IBrokenStreakRepository brokenStreakRepository;
    IStreakActivityLogRepository streakActivityLogRepository;
    IStreakPseudoActivityRepository streakDummyActivityRepository;
    HamletService hamletService;
    MembershipHelperService membershipHelperService;
    ActivityStreakEvaluatorService activityStreakEvaluatorService;
    
    RollbarService rollbarService;
    ObjectMapper objectMapper;
    RashiPublisherService rashiPublisherService;
    private static final String STREAK_EXPERIMENT_ID = "2008";
    
    /**
         * Resets or initializes the user's current streak based on the given reset date and streak configuration.
         * - If the current streak is active and not eligible to break, it is returned as-is.
         * - If the streak is eligible to break (based on tentativeStreakBreakDate), it is saved to broken streaks and reset.
         * - If no streak exists, a new streak is initialized.
         * - Updates streak versioning, carries forward max streak data, and recalculates the tentative break date.
     */
    @Transactional(transactionManager = "mongoDbTransactionManager", rollbackFor = Exception.class, timeout = 300)
    public StreakEntity resetCurrentStreakForUser(Long userId, String resetDate, StreakEntity userCurrentStreak, @NotNull StreakConfigEntity streakConfig) {
        // If no date provided, use current date
        if (StringUtils.isBlank(resetDate)) resetDate = LocalDate.now().toString();
        
        if (userCurrentStreak != null && !userId.equals(userCurrentStreak.getUserId())) {
            rollbarService.error(String.format("Wrong streak being reset for wrong userId=%s and streakUserId=%s", userId, userCurrentStreak.getUserId().toString()));
            throw new NullPointerException();
        }
        
        boolean hasStreakStarted = userCurrentStreak != null // streak is present
                && userCurrentStreak.getStreakCount() > 0 // streak has already started
                && StringUtils.isNotBlank(userCurrentStreak.getLastActivityDate());// activity is logged in streak
        
        boolean isStreakUnPaused = userCurrentStreak != null && StringUtils.isBlank(userCurrentStreak.getStreakPauseId()); // streak is not paused
        
        boolean shouldBreakCurrentStreak = hasStreakStarted && isStreakUnPaused
                && StringUtils.isNotBlank(userCurrentStreak.getTentativeStreakBreakDate())
                && TimeUtil.compareDate(resetDate, userCurrentStreak.getTentativeStreakBreakDate()) > 0; // evaluation date is after tentative streak break date
        
        if (hasStreakStarted && isStreakUnPaused && !shouldBreakCurrentStreak) {
            // streak exists and should not be reset/broken due to tentativeStreakBreakDate after the given reset date
            return userCurrentStreak;
        }
        
        
        if (shouldBreakCurrentStreak) { // break and reset streak
            
            // Delete streak if saved in db
            if (userCurrentStreak.getId() != null) {
                streakRepository.deleteById(userCurrentStreak.getId());
            }
            
            BrokenStreakEntity brokenStreakEntity = streakMapper.mapNonMongoFields(userCurrentStreak);
            log.info("Breaking Streak for userId={} resetDate={} brokenStreakEntity={} {}", userId, resetDate, brokenStreakEntity.getStreakCount(), brokenStreakEntity.getStreakVersion());
            brokenStreakRepository.save(brokenStreakEntity);
            
            StreakEntity existingUserStreak = userCurrentStreak;
            
            userCurrentStreak = new StreakEntity(userId);
            userCurrentStreak.setStreakConfigId(streakConfig.getConfigId());
            userCurrentStreak.setStreakVersion(existingUserStreak.getStreakVersion() + 1);
            userCurrentStreak.setMaxStreakVersion(existingUserStreak.getMaxStreakVersion());
            userCurrentStreak.setMaxStreakCount(existingUserStreak.getMaxStreakCount());
        } else if (!hasStreakStarted) {
            // else block for when streak is not active and should not be broken
            
            // if streak does not exist, create a new one
            if (userCurrentStreak == null) {
                userCurrentStreak = new StreakEntity(userId);
            }
            
            userCurrentStreak.setStreakConfigId(streakConfig.getConfigId());
            
            BrokenStreakEntity brokenStreakEntity = brokenStreakRepository.findFirstByUserIdOrderByStreakVersionDesc(userId).orElse(null);
            if (brokenStreakEntity != null) {
                userCurrentStreak.setStreakVersion(brokenStreakEntity.getStreakVersion() + 1);
                userCurrentStreak.setMaxStreakCount(brokenStreakEntity.getMaxStreakCount());
                userCurrentStreak.setMaxStreakVersion(brokenStreakEntity.getMaxStreakVersion());
            } else {
                userCurrentStreak.setStreakVersion(0);
            }
        }
            
        userCurrentStreak.setStreakConfigId(streakConfig.getConfigId());
        
        // add rest days quota to the reset date and figure out new tentativeStreakBreakDate
        String tentativeStreakBreakDate = TimeUtil.getLocalDateFromString(resetDate).plusDays(streakConfig.getRestDaysQuota()).toString();
        userCurrentStreak.setTentativeStreakBreakDate(
                getUpdatedStreakBreakDateForStreakConfig(resetDate, tentativeStreakBreakDate, userCurrentStreak, streakConfig)
        );
        
        return userCurrentStreak;
    }
    /*
            resetDate - if null, use today's date
     */
    String getUpdatedStreakBreakDateForStreakConfig(String activityDate, String newStreakBreakDateString, StreakEntity userCurrentStreak, StreakConfigEntity streakConfig) {
        
        // handle if rest days reset based on some interval
        if (streakConfig.getRestDaysResetInterval() != null && streakConfig.getRestDaysResetInterval().equals(StreakRestDaysResetInterval.WEEKLY)) {
            LocalDate newStreakBreakDate = TimeUtil.getLocalDateFromString(newStreakBreakDateString, null);
            
            boolean isOldAndNewTentativeStreakBreakDaysInDifferentWeeks = false;
            if (userCurrentStreak != null) {
                String tentativeStreakBreakDate = userCurrentStreak.getTentativeStreakBreakDate();
                if(StringUtils.isNotBlank(tentativeStreakBreakDate)) {
                    LocalDate oldDate = TimeUtil.getLocalDateFromString(tentativeStreakBreakDate, null);
                    boolean isSameWeek = newStreakBreakDate.get(WeekFields.ISO.weekOfYear()) == oldDate.get(WeekFields.ISO.weekOfYear());
                    boolean isSameYear = newStreakBreakDate.get(WeekFields.ISO.weekBasedYear()) == oldDate.get(WeekFields.ISO.weekBasedYear());
                    isOldAndNewTentativeStreakBreakDaysInDifferentWeeks = ! (isSameWeek && isSameYear);
                }
            }
            
            LocalDate activityLocalDate = TimeUtil.getLocalDateFromString(activityDate, null);
            boolean activityNotInSameWeekAsNewStreakBreakDate = !(newStreakBreakDate.get(WeekFields.ISO.weekOfYear()) == activityLocalDate.get(WeekFields.ISO.weekOfYear()) &&
                    newStreakBreakDate.get(WeekFields.ISO.weekBasedYear()) == activityLocalDate.get(WeekFields.ISO.weekBasedYear()));
            
            
            if (isOldAndNewTentativeStreakBreakDaysInDifferentWeeks || activityNotInSameWeekAsNewStreakBreakDate) {
                // not in same week
                int daysSinceLastMonday = newStreakBreakDate.getDayOfWeek().getValue() - 1;
                newStreakBreakDateString = newStreakBreakDate
                        .minusDays(daysSinceLastMonday)
                        .plusDays(streakConfig.getRestDaysQuota())
                        .toString();
            }
        }
        return newStreakBreakDateString;
    }
    
    static Long findMinimumRewardThresholdScore(List<StreakActivityRewardConfig> input) {
        if (CollectionUtils.isEmpty(input)) return null;
        List<StreakActivityRewardConfig> list = new ArrayList<>(input);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        list.sort(Comparator.comparing(StreakActivityRewardConfig::getActivityScore));
        if (list.get(0) == null || list.get(0).getActivityScore() == 0) {
            return null;
        }
        return list.get(0).getActivityScore();
    }
    
    // todo see if this logic is correctly working
    static StreakActivityRewardConfig findHighestEarnedRewardConfig(List<StreakActivityRewardConfig> input, long activityScore) {
        List<StreakActivityRewardConfig> list = new ArrayList<>(input);
        int index = Collections.binarySearch(list, new StreakActivityRewardConfig(0, activityScore),
                Comparator.comparing(StreakActivityRewardConfig::getActivityScore));
        if (index >= 0) {
            return list.get(index);
        } else if (index == -1) {
            return null;
        } else {
            return list.get(-index - 2);
        }
    }
    
    private Bucket getBucketForUserId(String userId) {
        HamletAllocationRequest hamletAllocationRequest = new HamletAllocationRequest();
        hamletAllocationRequest.setSkipAssignmentLog(false);
        hamletAllocationRequest.setUserId(userId);
        hamletAllocationRequest.setExperimentIds(Set.of(STREAK_EXPERIMENT_ID));
        return hamletService.getUserAllocations(hamletAllocationRequest).getBucketForExperiment(STREAK_EXPERIMENT_ID);
    }
    private String getStreakSegmentNameForUser(String userId) {
        SegmentSet<String> userSegmentNames = segmentationCacheClient.getUserSegments(userId);
        Map<String, String> userSegmentNameToStreakConfigIdMap = configStore.getUserSegmentNameToStreakConfigIdMap();
        List<String> segmentNamesInStreakConfig = new ArrayList<>(userSegmentNameToStreakConfigIdMap.keySet().stream().toList());
        segmentNamesInStreakConfig.removeIf(x -> ! userSegmentNames.contains(x));
        if (CollectionUtils.isNotEmpty(segmentNamesInStreakConfig)) {
            return userSegmentNameToStreakConfigIdMap.get(segmentNamesInStreakConfig.get(0));
        } else return null;
    }
    StreakConfigEntity getStreakConfigForUser(String userId) {
        String streakConfigIdForUser = getStreakSegmentNameForUser(userId);
        Bucket bucket = getBucketForUserId(userId);
        boolean isUserPartOfExperimentTreatment = bucket != null && !bucket.isControlBucket();
        boolean isUserPartOfExperimentControl = bucket != null && bucket.isControlBucket();
        log.debug("getStreakConfigForUser isUserPartOfExperimentTreatment userId={} isUserPartOfExperimentRollout={} isUserPartOfExperimentTreatment={} isUserPartOfExperimentControl={} streakConfigIdForUser={}", userId, isUserPartOfExperimentTreatment, isUserPartOfExperimentTreatment, isUserPartOfExperimentControl, streakConfigIdForUser);
        
        if (StringUtils.isBlank(streakConfigIdForUser) && isUserPartOfExperimentTreatment) {
            streakConfigIdForUser = "3_active_4_rest_days_with_membership_pause";
        } if (StringUtils.isBlank(streakConfigIdForUser) && isUserPartOfExperimentControl) {
            streakConfigIdForUser = "control_3_active_4_rest_days_with_membership_pause";
        }
        return streakConfigRepository.findByConfigId(streakConfigIdForUser).orElse(null);
    }
    
    boolean shouldProcessPause(Long userId, StreakEntity currentStreak, StreakConfigEntity streakConfig) {
        return userId != null && currentStreak != null && streakConfig != null;
    }
    StreakPauseEntity pauseCurrentStreak(@NonNull Long userId, MembershipEvent eventType, @NonNull StreakEntity currentStreak) throws ExecutionException, InterruptedException, JsonProcessingException {
        LocalDate today = LocalDate.now();
        
        List<LocalDate[]> pausedDateRanges = membershipHelperService.findSortedUnionPausedDateRanges(userId, TimeUtil.getStartOfDayEpoch(today), TimeUtil.getEndOfDayEpoch(today));
        
        LocalDate[] activePauseRange = pausedDateRanges.stream().filter(x -> {
            boolean isPauseIntervalActiveForToday = !x[0].isAfter(today) && !x[1].isBefore(today);
            return isPauseIntervalActiveForToday;
        }).findFirst().orElse(null);
        
        
        try {
            log.info("consumeMembershipPause::pauseCurrentStreak userId={} and today_date={} activePauseRange={} pausedDateRanges={}", userId, today, objectMapper.writeValueAsString(activePauseRange), objectMapper.writeValueAsString(pausedDateRanges));
        } catch (Exception ex) {
            log.warn("consumeMembershipPause::pauseCurrentStreak userId={} with silent error={}", userId, ex.getMessage());
        }
        if (activePauseRange != null) {
            StreakPauseEntity activePause = streakPauseRepository.findByUserIdAndIsPauseActive(userId, true).orElse( StreakPauseEntity.builder()
                    .userId(userId)
                    .pausedStreakVersion(currentStreak.getStreakVersion())
                    .pausedStreakConfigId(currentStreak.getStreakConfigId())
                    .membershipsPaused(0)
                            .startDate(activePauseRange[0].toString())
                            .endDate(activePauseRange[1].toString())
                    .build());
            LocalDate newPauseStartDate = activePauseRange[0];
            LocalDate newPauseEndDate = activePauseRange[1];
            
            
            if (newPauseStartDate.isBefore(TimeUtil.getLocalDateFromString(activePause.getStartDate()))) {
                activePause.setStartDate(newPauseStartDate.toString());
            }
            
            if (newPauseEndDate.isAfter(TimeUtil.getLocalDateFromString(activePause.getEndDate()))) {
                activePause.setEndDate(newPauseEndDate.toString());
            }
            if (eventType.equals(MembershipEvent.PAUSED)) activePause.setMembershipsPaused(activePause.getMembershipsPaused() + 1);
            
            LocalDate activePauseStartDate = TimeUtil.getLocalDateFromString(activePause.getStartDate());
            LocalDate activePauseEndDate = TimeUtil.getLocalDateFromString(activePause.getEndDate());
            boolean isPauseActiveNow = !activePauseStartDate.isAfter(today) && !activePauseEndDate.isBefore(today);
            activePause.setPauseActive(isPauseActiveNow);
            
            activePause = streakPauseRepository.save(activePause);
            
            if (isPauseActiveNow) {
                currentStreak.setStreakPauseId(activePause.getId());
                streakRepository.save(currentStreak);
                rashiPublisherService.publishRashiForUserStreakPause(userId, currentStreak, activePauseStartDate, activePauseEndDate);
            }
            log.info("consumeMembershipPause::pauseCurrentStreak was paused userId={} and today_date={} streakEntity={} pauseEntity=[isPauseActiveNow={} pauseStart={} pauseEnd={} membershipsPaused={}] ", userId, today, currentStreak.getStreakPauseId(), activePause.isPauseActive(), activePause.getStartDate(), activePause.getEndDate(), activePause.getMembershipsPaused());
            return activePause;
        } else {
            log.error("consumeMembershipPause::pauseCurrentStreak was not paused as membership had no pauses userId={} eventType={} ", userId, eventType);
            return null;
        }
    }
    StreakPauseEntity unpauseCurrentStreak(@NonNull Long userId, MembershipEvent eventType, @NonNull StreakEntity currentStreak, @NonNull StreakConfigEntity streakConfig) throws ExecutionException, InterruptedException, JsonProcessingException {
        LocalDate today = LocalDate.now();
        
        List<LocalDate[]> pausedDateRanges = membershipHelperService.findSortedUnionPausedDateRanges(userId, TimeUtil.getStartOfDayEpoch(today), TimeUtil.getEndOfDayEpoch(today));
        
        LocalDate[] activePauseRange = pausedDateRanges.stream().filter(x -> {
            boolean isPauseIntervalActiveForToday = x[0].isBefore(today) && x[1].isAfter(today);
            return isPauseIntervalActiveForToday;
        }).findFirst().orElse(null);
        
        StreakPauseEntity activePause = streakPauseRepository.findByUserIdAndIsPauseActive(userId, true).orElse(null);
        
        try {
            log.info("consumeMembershipPause::unpauseCurrentStreak userId={} and today_date={} activePauseRange={} pausedDateRanges={} activePause={}", userId, today, objectMapper.writeValueAsString(activePauseRange), objectMapper.writeValueAsString(pausedDateRanges), objectMapper.writeValueAsString(activePause));
        } catch (Exception ex) {
            log.warn("consumeMembershipPause::unpauseCurrentStreak userId={} with silent error={}", userId, ex.getMessage());
        }
        if (activePauseRange == null && activePause != null) {
            activePause.setPauseActive(false);
            activePause.setEndDate(today.toString());
            
            LocalDate activePauseStartDate = TimeUtil.getLocalDateFromString(activePause.getStartDate());
            LocalDate activePauseEndDate = TimeUtil.getLocalDateFromString(activePause.getEndDate());
            
            if (activePauseEndDate.equals(activePauseStartDate) && activePauseEndDate.equals(today) && StringUtils.isNotBlank(activePause.getId())) {
                streakPauseRepository.deleteById(activePause.getId());
            } else {
                activePause = streakPauseRepository.save(activePause);
                updateStreakBreakDateDueToPause(currentStreak, streakConfig, activePauseStartDate, activePauseEndDate);
            }
            rashiPublisherService.publishRashiForUserStreakUnPause(userId, currentStreak, activePauseStartDate, activePauseEndDate);
            currentStreak.setStreakPauseId(null);
            streakRepository.save(currentStreak);
            log.info("consumeMembershipPause::unpauseCurrentStreak was unpaused userId={} and today_date={} streakEntity={} pauseEntity=[isPauseActiveNow={} pauseStart={} pauseEnd={} membershipsPaused={}] ", userId, today, currentStreak.getStreakPauseId(), activePause.isPauseActive(), activePause.getStartDate(), activePause.getEndDate(), activePause.getMembershipsPaused());
            return activePause;
        }
        log.warn("consumeMembershipPause::unpauseCurrentStreak was not unpaused userId={} and today_date={} activePauseRange={} pausedDateRanges={} activePause={}", userId, today, objectMapper.writeValueAsString(activePauseRange), objectMapper.writeValueAsString(pausedDateRanges), objectMapper.writeValueAsString(activePause));
        return null;
    }
    
    String cleanupStreaksAndActivitiesAfterDateAndGetNewRecomputeDate(Long userId, String recomputeDate) {
        // fetch user streak within this period and delete them
        StreakEntity userActiveStreak = streakRepository.findByUserId(userId).orElse(null);
        if (userActiveStreak != null) {
            // if user had a past activity in current streak, reset recomputeDate to firstactivitydate of the streak
            if (StringUtils.isNotBlank(userActiveStreak.getFirstActivityDate()) && TimeUtil.isDate1AfterDate2(recomputeDate, userActiveStreak.getFirstActivityDate())) {
                recomputeDate = userActiveStreak.getFirstActivityDate();
            }
            streakRepository.delete(userActiveStreak);
        }
        // fetch all expired streaks within this period and delete them
        List<BrokenStreakEntity> brokenStreaks = brokenStreakRepository.findAllByUserIdAndLastActivityDateGreaterThanEqual(userId, recomputeDate);
        if (CollectionUtils.isNotEmpty(brokenStreaks)) {
            for(BrokenStreakEntity brokenStreak : brokenStreaks) {
                if (TimeUtil.isDate1AfterDate2(recomputeDate, brokenStreak.getFirstActivityDate())) {
                    recomputeDate = brokenStreak.getFirstActivityDate();
                }
            }
            brokenStreakRepository.deleteAll(brokenStreaks);
        }
        // fetch and delete all pauses in this period
        List<StreakPauseEntity> streakPauseEntities = streakPauseRepository.findPausesBetweenIncludingDates(userId, recomputeDate, LocalDate.now().toString());
        streakPauseRepository.deleteAll(streakPauseEntities);
        // fetch and delete all streak activity logs within this period
        List<StreakActivityLogEntity> activityLogs = streakActivityLogRepository.findAllByUserIdAndActivityDateGreaterThanEqual(userId, recomputeDate);
        streakActivityLogRepository.deleteAll(activityLogs);
        return recomputeDate;
    }
    
    StreakEntity updateAndSaveStreakEntityWithActivitiesInPeriod(
            @NotNull String userId, String startDate, @NotNull String endDate, BaseActivityDetails activityDetails,
            @NotNull StreakEntity streakEntity, @NotNull(message = "No Streak Config Present") StreakConfigEntity streakConfig,
            boolean reconcile
    ) throws Exception {
        if (streakConfig == null) {
            String errorMsg = "Streak Config Not Present for userId = " + userId + " and startDate = " + startDate + " and endDate = " + endDate + " and activity_id = " + activityDetails.getIdempotenceKey();
            Exception ex = new NotFoundException(errorMsg);
            rollbarService.error(ex, errorMsg);
            throw ex;
        } else if (streakEntity == null) {
            String errorMsg = "Streak Not Present for userId = " + userId + " and startDate = " + startDate + " and endDate = " + endDate + " and activity_id = " + activityDetails.getIdempotenceKey();
            Exception ex = new NotFoundException(errorMsg);
            rollbarService.error(ex, errorMsg);
            throw ex;
        }
        
        // Handle startDate for period of evaluation
        if (StringUtils.isBlank(startDate)) {
            if (StringUtils.isNotBlank(streakEntity.getLastActivityDate())) {
                startDate = streakEntity.getLastActivityDate();
            } else {
                LocalDate endLocalDate = TimeUtil.getLocalDateFromString(endDate),
                        defaultStartDate = LocalDate.now();
                if (endLocalDate.isBefore(defaultStartDate)) {
                    defaultStartDate = endLocalDate;
                }
                startDate = defaultStartDate.toString();
//                startDate = TimeUtil.getDayRelativeToDate(endDate, DayOfWeek.MONDAY.getValue(), 0);
            }
        }
        List<ActivityTypeDS> activityTypeDSList = streakConfig.getActivityRewardConfigMap().keySet().stream().map(ActivityTypeDS::valueOf).toList();
        List<BaseActivityDetails> activityDetailsListToProcess = getMissedActivitiesDetailsForPeriod(userId, activityDetails, startDate, endDate, activityTypeDSList);
        if (activityDetails != null && activityTypeDSList.contains(activityDetails.getActivityType())) activityDetailsListToProcess.add(activityDetails);
        Set<String> uniqueKeys = new HashSet<>();
        activityDetailsListToProcess = activityDetailsListToProcess.stream()
                .filter(activity -> uniqueKeys.add(activity.getIdempotenceKey()))
                .collect(Collectors.toList());
        activityDetailsListToProcess.sort(Comparator.comparing(BaseActivityDetails::getDate));
        String activityIdempotenceKey = activityDetails != null ? activityDetails.getIdempotenceKey() : null;
        log.info("Streak updateAndSaveStreakEntityWithActivitiesInPeriod pre userId={} activityId={} startDate={} endDate={} reconcile={} activityDetailsListToProcess.size={} streakEntity={} {} {}", userId, activityIdempotenceKey, startDate, endDate, reconcile, activityDetailsListToProcess.size(), streakEntity.getStreakCount(), streakEntity.getStreakVersion(), streakEntity.getTentativeStreakBreakDate());
        
        if (reconcile) {
            streakEntity = reconcileAndGetStreak(userId, streakEntity, streakConfig, startDate, endDate, activityDetailsListToProcess);
        } else {
            for (BaseActivityDetails activityDetailsToProcess : activityDetailsListToProcess) {
                streakEntity = activityStreakEvaluatorService.processActivityAndReturnStreak(userId, activityDetailsToProcess, streakConfig, streakEntity);
            }
        }
        log.info("Streak updateAndSaveStreakEntityWithActivitiesInPeriod post userId={} activityId={} startDate={} endDate={} reconcile={} activityDetailsListToProcess.size={} streakEntity={} {} {}", userId, activityIdempotenceKey, startDate, endDate, reconcile, activityDetailsListToProcess.size(), streakEntity.getStreakCount(), streakEntity.getStreakVersion(), streakEntity.getTentativeStreakBreakDate());
        return streakRepository.save(streakEntity);
    }
    private StreakEntity reconcileAndGetStreak(String userId, StreakEntity streakEntity, StreakConfigEntity streakConfig, String startDate, String endDate, List<BaseActivityDetails> activityDetailsListToProcess) throws ExecutionException, InterruptedException, JsonProcessingException {
        // fetch membership pause durations within this period
        long startEpochMilli = TimeUtil.getStartOfDayEpoch(TimeUtil.getLocalDateFromString(startDate, null));
        long endEpochMilli = TimeUtil.getStartOfDayEpoch(TimeUtil.getLocalDateFromString(endDate, null));
        List<LocalDate[]> pauseIntervals = membershipHelperService.findSortedUnionPausedDateRanges(Long.valueOf(userId), startEpochMilli, endEpochMilli);
        int streakVersion = streakEntity.getStreakVersion();
        LocalDate currentDate = LocalDate.now();
        AtomicReference<StreakPauseEntity> activePause = new AtomicReference<>(null);
        List<StreakPauseEntity> pauseEntities = pauseIntervals.stream().map(x -> {
            LocalDate pauseStartDate = x[0];
            LocalDate pauseEndDate = x[1];
            StreakPauseEntity entity = StreakPauseEntity.builder()
                    .userId(Long.valueOf(userId))
                    .startDate(pauseStartDate.toString())
                    .endDate(pauseEndDate.toString())
                    .pausedStreakConfigId(streakConfig.getConfigId())
                    .pausedStreakVersion(streakVersion)
                    .membershipsPaused(1)
                    .build();
            if (!currentDate.isBefore(pauseStartDate) && !currentDate.isAfter(pauseEndDate)) {
                entity.setPauseActive(true);
                activePause.set(entity);
            }
            return entity;
        }).toList();
        
        pauseEntities = streakPauseRepository.saveAll(pauseEntities);
        
        LocalDate nextPauseStartDate = null, nextPauseEndDate = null;
        String nextPauseEntityId =  null;
        int p = 0, pn = pauseEntities.size();
        // Note: find pause before processing activities and then process activities till pauseEnd and then look for next pause duration
        if (CollectionUtils.isNotEmpty(activityDetailsListToProcess)) {
            int n = activityDetailsListToProcess.size(), i = 0;
            while (i<n || p<pn) {
                // Process Pauses
                if (p<pn) {
                    nextPauseStartDate = TimeUtil.getLocalDateFromString(pauseEntities.get(p).getStartDate());
                    nextPauseEndDate = TimeUtil.getLocalDateFromString(pauseEntities.get(p).getEndDate());
                    nextPauseEntityId = pauseEntities.get(p).getId();
                } else {
                    nextPauseStartDate = null;
                    nextPauseEndDate = null;
                    nextPauseEntityId = null;
                }
                // process until next activity date is more than current pause start date, i.e. until pause starts
                while (i < n &&
                        (nextPauseStartDate == null ||
                                TimeUtil.getLocalDateFromString(activityDetailsListToProcess.get(i).getDate(), null).isBefore(nextPauseStartDate))
                ) {
                    BaseActivityDetails activityDetailsToProcess = activityDetailsListToProcess.get(i);
                    streakEntity = activityStreakEvaluatorService.processActivityAndReturnStreak(userId, activityDetailsToProcess, streakConfig, streakEntity);
                    i++;
                }
                
                // activate the pause
                if (nextPauseStartDate != null && nextPauseEndDate != null) {
                    // ignore all activities within the pause
                    while (i<n) {
                        LocalDate activityLocalDate = TimeUtil.getLocalDateFromString(activityDetailsListToProcess.get(i).getDate());
                        boolean isActivityDuringPause = !nextPauseStartDate.isAfter(activityLocalDate) && !activityLocalDate.isAfter(nextPauseEndDate);
//                        if (isActivityDuringPause) i++;
                        if (isActivityDuringPause) {
                            BaseActivityDetails activityDetailsToProcess = activityDetailsListToProcess.get(i);
                            streakEntity = activityStreakEvaluatorService.processActivityAndReturnStreak(userId, activityDetailsToProcess, streakConfig, streakEntity);
                            i++;
                        }
                        else break;
                    }
                    
                    updateStreakBreakDateDueToPause(streakEntity, streakConfig, nextPauseStartDate, nextPauseEndDate);
//                    boolean isPauseActiveCurrently = !LocalDate.now().isBefore(nextPauseStartDate)
//                            && !LocalDate.now().isAfter(nextPauseEndDate);
                    boolean isPauseActiveCurrently = pauseEntities.get(p).isPauseActive();
                    if (isPauseActiveCurrently) {
                        streakEntity.setStreakPauseId(nextPauseEntityId);
                    }
                    p++;
                } else {
                    streakEntity.setStreakPauseId(null);
                }
            }
        }
        return streakEntity;
    }
    
    void updateStreakBreakDateDueToPause(StreakEntity streakEntity, StreakConfigEntity streakConfig, LocalDate pauseStartDate, LocalDate pauseEndDate) {
        String newTentativeStreakBreakDay;
        if (
                streakEntity == null
                        || pauseEndDate == null
                        || pauseStartDate == null
                        || (// Note: streak break date is not set
                                StringUtils.isNotBlank(streakEntity.getTentativeStreakBreakDate())
                                        && (// Note: Streak Break Date is not within any pauses
                                                pauseStartDate.isAfter(TimeUtil.getLocalDateFromString(streakEntity.getTentativeStreakBreakDate()))
                                                    || pauseEndDate.isBefore(TimeUtil.getLocalDateFromString(streakEntity.getTentativeStreakBreakDate())
                                )
                        )
        )) return;
        else if (StringUtils.isBlank(streakEntity.getTentativeStreakBreakDate())) {
            newTentativeStreakBreakDay = pauseEndDate.plusDays(streakConfig.getRestDaysQuota() + 1).toString();
        } else {
            String currentTentativeStreakBreakDateString = streakEntity.getTentativeStreakBreakDate();
            LocalDate currentTentativeStreakBreakDate = TimeUtil.getLocalDateFromString(currentTentativeStreakBreakDateString, null);
            if (!pauseStartDate.isAfter(pauseEndDate)) {
                newTentativeStreakBreakDay = currentTentativeStreakBreakDate.plusDays((1 + ChronoUnit.DAYS.between(pauseStartDate, pauseEndDate))).toString();
            } else {
                newTentativeStreakBreakDay = currentTentativeStreakBreakDateString;
            }
        }
        streakEntity.setTentativeStreakBreakDate(
                getUpdatedStreakBreakDateForStreakConfig(pauseEndDate.plusDays(1).toString(), newTentativeStreakBreakDay, streakEntity, streakConfig)
        );
    }
    
    List<BaseActivityDetails> getMissedActivitiesDetailsForPeriod(String userId, BaseActivityDetails currentActivityDetails, @NotBlank String startDate, String endDate, List<ActivityTypeDS> activityTypeDSList) throws IOException {
        List<BaseActivityDetails> activityDetailsListToProcess = new ArrayList<>();
        
        // Find missed logged activities for user from activitystore in logging-service
        ActivityDSSearchRequest activityDSSearchRequest = new ActivityDSSearchRequest();
        activityDSSearchRequest.setUserId(List.of(userId));
//        activityDSSearchRequest.setShow(List.of(true)); // not sure what show means here
        activityDSSearchRequest.setActivityType(activityTypeDSList);
        List<SortField> sortFields = new ArrayList<>();
        SortField sortField = new SortField();
        sortField.setField("date");
        sortField.setOrder(1);
        sortFields.add(sortField);
        activityDSSearchRequest.setSortFields(sortFields);
        DateQueryRange dateQueryRange = new DateQueryRange();
        dateQueryRange.setGte(startDate);
        dateQueryRange.setLte(endDate);
        activityDSSearchRequest.setDateRange(dateQueryRange);
        List<ActivityDS> missedActivities = loggingService.getActivities(activityDSSearchRequest, MongoReadPref.ANALYTICS).getActivities();
        if (CollectionUtils.isNotEmpty(missedActivities)) {
            for (ActivityDS missedActivity : missedActivities) {
                try {
                    activityDetailsListToProcess.add(activityStreakEvaluatorService.getActivityDetailsFromActivityDS(missedActivity));
                } catch(Exception ex) {
                    rollbarService.error(ex, "Error in getActivityDetailsFromActivityDS for activity id: " + missedActivity.getIdempotenceKey() + " body: " + objectMapper.writeValueAsString(missedActivity));
                }
            }
        }
        
        activityDetailsListToProcess.addAll(getDummyActivitiesForUserAndDateRange(userId, startDate, endDate));
        
        activityDetailsListToProcess = filterAndGetOnlyMissedActivities(userId, currentActivityDetails, startDate, endDate, activityDetailsListToProcess);
        
        activityDetailsListToProcess.sort(Comparator.comparing(BaseActivityDetails::getDate));
        return activityDetailsListToProcess;
    }
    private List<BaseActivityDetails> filterAndGetOnlyMissedActivities(String userId, BaseActivityDetails currentActivityDetails, String startDate, String endDate, List<BaseActivityDetails> activityDetailsListToProcess) {
        List<String> idempotenceKeysAlreadyProcessed = new ArrayList<>();
        if (currentActivityDetails != null) {
            idempotenceKeysAlreadyProcessed.add(currentActivityDetails.getIdempotenceKey());
        }
        if (StringUtils.isBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            startDate = endDate;
        } else if (StringUtils.isBlank(endDate) && StringUtils.isNotBlank(startDate)) {
            endDate = startDate;
        }
        if (StringUtils.isNotBlank(endDate) && StringUtils.isNotBlank(startDate)) {
            idempotenceKeysAlreadyProcessed = streakActivityLogRepository.findAllByUserIdAndDateInRange(Long.valueOf(userId), startDate, endDate).stream().map(StreakActivityLogEntity::getActivityIdempotenceKey).toList();
        }
        
        
        Map<String, BaseActivityDetails> idempotenceIdBaseActivityDetailsMap = activityDetailsListToProcess.stream()
                .collect(Collectors.toMap(BaseActivityDetails::getIdempotenceKey, activityDetails -> activityDetails, (existing, replacement) -> replacement));
        
        for (String idempotenceKey: idempotenceKeysAlreadyProcessed) {
            idempotenceIdBaseActivityDetailsMap.remove(idempotenceKey);
        }
        
        return new ArrayList<>(idempotenceIdBaseActivityDetailsMap.values());
    }
    private List<BaseActivityDetails> getDummyActivitiesForUserAndDateRange(String userId, String startDate, String endDate) {
        List<StreakPseudoActivityEntity> streakPseudoActivityEntities = streakDummyActivityRepository.findAllByUserIdAndDateInRange(userId, startDate, endDate);
        return streakPseudoActivityEntities.stream().map(activityStreakEvaluatorService::getActivityDetailsFromStreakPseudoActivity).toList();
    }
    
    /*
    inputs:
        streakEntity - current streak to be broken
    process:
        converts active streak to broken streak
        reassigns activities to broken streak
*/
    public void breakStreakIfApplicableAndUpdateEntities(Long userId, String evaluationDate, StreakEntity userCurrentStreak, StreakConfigEntity streakConfig) {
    
    }
    
   
    boolean isActivityInPastAndProcessingMissed(BaseActivityDetails currentActivityDetails) {
        String latestProcessedActivityDate = null;//getLatestActivityProcessedDate(userCurrentStreak, latestBrokenStreak).orElse(null);
        List<StreakActivityLogEntity> streakActivityLogEntities = streakActivityLogRepository
                .findByUserId(Long.valueOf(currentActivityDetails.getUserId()), PageRequest.of(0, 1, Sort.by(Sort.Order.desc("activityDate"))));
        if (CollectionUtils.isNotEmpty(streakActivityLogEntities)) {
            latestProcessedActivityDate = streakActivityLogEntities.get(0).getActivityDate();
        }
        if (latestProcessedActivityDate == null) return false;
        
        boolean isActivityInPastAndProcessingMissed = TimeUtil.getLocalDateFromString(currentActivityDetails.getDate())
                .isBefore(TimeUtil.getLocalDateFromString(latestProcessedActivityDate));
        if (isActivityInPastAndProcessingMissed) log.info("isActivityInPastAndProcessingMissed for userId={} idempotence_key={} date={}",
                currentActivityDetails.getUserId(), currentActivityDetails.getIdempotenceKey(), currentActivityDetails.getDate());
        return isActivityInPastAndProcessingMissed;
    }
    private Optional<String> getLatestActivityProcessedDate(@NotNull StreakEntity userCurrentStreak, BrokenStreakEntity latestBrokenStreak) {
        if (userCurrentStreak != null && userCurrentStreak.getLastActivityDate() != null) return Optional.of(userCurrentStreak.getLastActivityDate());
        else if (latestBrokenStreak != null && latestBrokenStreak.getLastActivityDate() != null) return Optional.of(latestBrokenStreak.getLastActivityDate());
        return Optional.empty();
    }
    
    Integer calculateRemainingRestDaysForUser(StreakEntity streakEntity, StreakConfigEntity streakConfig, LocalDate currentDate, LocalDate mondayOfCurrentWeek, LocalDate sundayOfCurrentWeek) {
        Long userId = streakEntity.getUserId();
        Set<String> activeDatesInWeek = new HashSet<>();
        try {
            List<StreakActivityLogEntity> streakActivityLogEntities = streakActivityLogRepository.findAllByUserIdAndDateInRange(userId, mondayOfCurrentWeek.toString(), sundayOfCurrentWeek.toString());
            for (StreakActivityLogEntity streakActivityLogEntity: streakActivityLogEntities) {
                activeDatesInWeek.add(streakActivityLogEntity.getActivityDate());
            }
        } catch (Exception ex) {
            rollbarService.error(ex, "Error while calculating weekly streak activities for user: " + userId + " and mondayOfWeek = " + mondayOfCurrentWeek.toString());
        }
        if (StringUtils.isNotBlank(streakEntity.getStreakPauseId()) || streakEntity.getStreakCount() <= 0) {
            return null;
        }
        String firstActivityDateStr = streakEntity.getFirstActivityDate();
        boolean hasStreakStartedThisWeek = StringUtils.isNotBlank(firstActivityDateStr) &&
                !TimeUtil.getLocalDateFromString(firstActivityDateStr).isBefore(mondayOfCurrentWeek);
        LocalDate startDateOfStreakForThisWeek = hasStreakStartedThisWeek ? TimeUtil.getLocalDateFromString(firstActivityDateStr) : mondayOfCurrentWeek;
        
        int activeDaysInCurrentWeek = activeDatesInWeek.size();
        
        Integer userStreakDayEndHour = configStore.getUserStreakDayEndHour();
        boolean isTodayConsideredInStreakCalculation = activeDatesInWeek.contains(currentDate.toString()) ||
                LocalTime.of(userStreakDayEndHour, 0).isBefore(LocalDateTime.now().toLocalTime());
        
        int daysPassedInCurrentWeek = Math.toIntExact(ChronoUnit.DAYS.between(startDateOfStreakForThisWeek, currentDate));
        if (isTodayConsideredInStreakCalculation) {
            daysPassedInCurrentWeek++;
        }
        
        int restDaysUsedInCurrentWeek = Math.max(0,
                Math.min(daysPassedInCurrentWeek - activeDaysInCurrentWeek, streakConfig.getRestDaysQuota()));
        
        Integer remainingRestDays = streakConfig.getRestDaysQuota() - restDaysUsedInCurrentWeek;
        
        // check for discrepancy in remaining rest days and streak break date
        LocalDate tentativeStreakBreakDate = TimeUtil.getLocalDateFromString(streakEntity.getTentativeStreakBreakDate());
        
        int daysToStreakBreakDate = Math.max(0,
                (int) ChronoUnit.DAYS.between(currentDate, tentativeStreakBreakDate) - (
                        isTodayConsideredInStreakCalculation ? 1 : 0
                )
        );
        
        LocalDate streakEndDateIncludingRemainingRestDays = currentDate
                .plusDays(remainingRestDays + (isTodayConsideredInStreakCalculation ? 1 : 0));
        if (streakEndDateIncludingRemainingRestDays.isAfter(tentativeStreakBreakDate) ||
                (
                        streakEndDateIncludingRemainingRestDays.isBefore(tentativeStreakBreakDate) && !streakEndDateIncludingRemainingRestDays.isAfter(sundayOfCurrentWeek)
                )
        ) {
            log.error("Discrepancy in calculating tentativeStreakBreakDate for " +
                            "userId={} tentativeStreakBreakDate={} remainingRestDays={} " +
                            "daysToStreakBreakDate={} currentDate={} mondayOfCurrentWeek={} " +
                            "hasStreakStartedThisWeek={} isTodayConsideredInStreakCalculation={} " +
                            "daysPassedInCurrentWeek={} activeDaysInCurrentWeek={}",
                    streakEntity.getUserId(),
                    streakEntity.getTentativeStreakBreakDate(),
                    remainingRestDays,
                    daysToStreakBreakDate,
                    currentDate,
                    mondayOfCurrentWeek,
                    hasStreakStartedThisWeek,
                    isTodayConsideredInStreakCalculation,
                    daysPassedInCurrentWeek,
                    activeDaysInCurrentWeek);
        }
        
        return remainingRestDays;
    }
    List<StreakActivityResponse> getEligibleActivitiesWithEndDateScore(String userId, LocalDate currentDate, StreakConfigEntity streakConfig) {
        List<StreakActivityResponse> eligibleActivitiesWithEndDateScore = null;
        // create a map of activity to its highest scored entity for the day
        List<StreakActivityLogEntity> streakActivityLogEntities = streakActivityLogRepository.findAllByUserIdAndActivityDate(Long.valueOf(userId), currentDate.toString());
        Map<String, StreakActivityLogEntity> activityLogEntityMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(streakActivityLogEntities)) {
            streakActivityLogEntities.forEach(streakActivityLogEntity -> {
                if(
                        activityLogEntityMap.get(streakActivityLogEntity.getActivityType().toString()) == null ||
                                activityLogEntityMap.get(streakActivityLogEntity.getActivityType().toString()).getActivityScore() < streakActivityLogEntity.getActivityScore()
                ) {
                    activityLogEntityMap.put(streakActivityLogEntity.getActivityType().toString(), streakActivityLogEntity);
                }
            });
        }
        
        eligibleActivitiesWithEndDateScore = new ArrayList<>();
        
        List<StreakActivityResponse> finalEligibleActivitiesWithEndDateScore = eligibleActivitiesWithEndDateScore;
        streakConfig.getActivityRewardConfigMap().keySet().forEach(activityTypeDS -> {
            StreakActivityResponse streakActivityResponse = new StreakActivityResponse();
            streakActivityResponse.setActivityType(ActivityTypeDS.valueOf(activityTypeDS));
            
            List<StreakActivityRewardConfig> streakActivityRewardConfigs = streakConfig.getActivityRewardConfigMap().get(activityTypeDS);
            Long activityMinThresholdScore = StreakHelperService.findMinimumRewardThresholdScore(streakActivityRewardConfigs);
            
            streakActivityResponse.setActivityMinThresholdScore(activityMinThresholdScore);
            if(activityMinThresholdScore != null && activityLogEntityMap.get(activityTypeDS) != null) {
                streakActivityResponse.setActivityScore(activityLogEntityMap.get(activityTypeDS).getActivityScore());
            }
            finalEligibleActivitiesWithEndDateScore.add(streakActivityResponse);
        });
        return eligibleActivitiesWithEndDateScore;
    }
    
}
