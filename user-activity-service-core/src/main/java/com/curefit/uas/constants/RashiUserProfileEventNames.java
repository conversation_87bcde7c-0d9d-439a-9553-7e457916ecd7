package com.curefit.uas.constants;

public enum RashiUserProfileEventNames {
//    IS_TRIAL_USER("IS_TRIAL_USER"),
//    LIVE_PT_GOAL("LIVE_PT_GOAL"),
//    CULT_GOAL("CULT_GOAL"),
//    CITY_UPDATED("city_updated"),
//    FITNESS_READING_TAKEN("fitness_reading_taken"),
//    PACK_UPDATED("pack_changed"),
//    SELECT_PACK_UPDATED("SELECT_PACK_CHANGED"),
//    USER_TRIALS_COUNT_UPDATED("trials_count_updated"),
//    CULT_REWARD_STATUS("cult_reward_status"),
//    PREFERRED_WORKOUT_DATA("cult_preferred_workout_updated"),
//    JUNIOR_WORKOUT_DETAILS("cult_junior_workout_updated"),
//    EXPIRY_EXTENSION_DATA("expiration_extension_data"),
//    CULT_OCB_DATA_UPDATED("cult_ocb_data_updated"),
//    USER_DETAILS_UPDATED("user_details_updated"),
//    USER_WALKED_IN("user_was_walked_in"),
//    USER_LOCALITY_PREFERENCE_UPDATED("user_locality_preference_updated"),
//    USER_SETTING_PREFERENCE_UPDATED("user_setting_preference_updated"),
//    USER_PARQ_STATUS("parq_status"),
//    ACCOUNTABILITY_PARTNER("accountability_partner"),
//    HOME_PAGE_PREFERENCE_UPDATED("home_page_preference_updated"),
//    FITNESS_REPORT_WEEKLY_ACTIVE_DAYS_DATE_UPDATED("fitness_report_weekly_active_days_date_updated"),
//    CULT_USER_PROPERTY_UPDATED("cult_user_property_updated"),
//    USER_YEARLY_FITNESS_ATTRIBUTE_UPDATED("user_yearly_fitness_attribute_updated"),
//    PAUSE_UPDATE_EVENT("pause_update_event"),
//    HABIT_BUILDING_EVENT("habit_building_event"),
    FITNESS_REPORT_WEEKLY_ACTIVE_DAYS_UPDATED("fitness_report_weekly_active_days_updated");
    
    private final String value;
    
    RashiUserProfileEventNames(String value) {
        this.value = value;
    }
    
    public String getValue() {
        return value;
    }
}