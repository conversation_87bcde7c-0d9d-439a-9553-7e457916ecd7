package com.curefit.uas.constants;

import lombok.Getter;

import java.util.concurrent.locks.Lock;

@Getter
public enum LockKey {
    FITNESS_REPORT_USER("fitness_report_user_id__"),
    FITNESS_REPORT_USER_INTERVAL_START_DATE("fitness_report_user_id_interval_start_date__");
    
    private final String value;
    
    LockKey(String value) {
        this.value = value;
    }
    
    public String toString(LockKey lockKey) {
        return lockKey.getValue();
    }
}
