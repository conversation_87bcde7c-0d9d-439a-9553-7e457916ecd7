package com.curefit.uas.constants;

public enum RashiUserActivityEventName {
//    CLASS_BOOKED_EVENT_NAME("CULT_CLASS_BOOKED"),
//    TRIAL_CLASS_BOOKED_EVENT_NAME("CULT_TRIAL_BOOKED"),
//    CLASS_CANCELLED_EVENT_NAME("CULT_CLASS_CANCELLED"),
//    TRIAL_CLASS_CANCELLED_EVENT_NAME("CULT_TRIAL_CANCELLED"),
//    CLASS_ATTENDED_EVENT_NAME("cult_class_attended"),
//    TRIAL_CLASS_ATTENDED_EVENT_NAME("CULT_TRIAL_ATTENDED"),
//    PAID_CLASS_ATTENDED_EVENT_NAME("CULT_PAID_CLASS_ATTENDED"),
//    CLASS_MISSED_EVENT_NAME("CULT_CLASS_MISSED"),
//    CLASS_WAITLIST_CONFIRMED_NAME("CULT_CLASS_WAITLIST_CONFIRMED"),
//    CLASS_WAITLIST_REJECTED_NAME("CULT_CLASS_WAITLIST_REJECTED"),
//    TRIAL_MISSED_EVENT_NAME("CULT_TRIAL_MISSED"),
//    PACK_PURCHASED_EVENT_NAME("CULT_PACK_PURCHASED"),
//    PACK_EXPIRED_EVENT_NAME("CULT_PACK_EXPIRED"),
//    VACCINE_MEMBER_REGISTERED_EVENT_NAME("VACCINE_MEMBER_REGISTERED"),
//    VACCINE_FIRST_BENEFICIARY_REGISTERED_EVENT_NAME("VACCINE_FIRST_BENEFICIARY_REGISTERED"),
//    VACCINE_SECOND_BENEFICIARY_REGISTERED_EVENT_NAME("VACCINE_SECOND_BENEFICIARY_REGISTERED"),
//    VACCINE_SLOT_MEMBER_ACCEPTED("VACCINE_SLOT_MEMBER_ACCEPTED"),
//    VACCINE_SLOT_BENEFICIARY_ACCEPTED("VACCINE_SLOT_BENEFICIARY_ACCEPTED"),
//    VACCINE_SLOT_MEMBER_REJECTED("VACCINE_SLOT_MEMBER_REJECTED"),
//    VACCINE_SLOT_BENEFICIARY_REJECTED("VACCINE_SLOT_BENEFICIARY_REJECTED"),
//    VACCINE_BENEFICIARY_OTP_VERIFIED("VACCINE_BENEFICIARY_OTP_VERIFIED"),
//    VACCINE_SELF_OTP_VERIFIED("VACCINE_SELF_OTP_VERIFIED"),
//    VACCINE_BENEFICIARY_ID_VERIFIED("VACCINE_BENEFICIARY_ID_VERIFIED"),
//    VACCINE_SELF_ID_VERIFIED("VACCINE_SELF_ID_VERIFIED"),
//    VACCINE_BENEFICIARY_PHOTO_UPLOADED("VACCINE_BENEFICIARY_PHOTO_UPLOADED"),
//    VACCINE_SELF_PHOTO_UPLOADED("VACCINE_SELF_PHOTO_UPLOADED"),
//    VACCINE_BENEFICIARY_VACCINATED("VACCINE_BENEFICIARY_MARKED_VACCINATED"),
//    VACCINE_SELF_VACCINATED("VACCINE_SELF_MARKED_VACCINATED"),
//    FITNESS_READING_TAKEN("fitness_reading_taken"),
//    MEMBERSHIP_CANCELLED_EVENT("membership_cancelled"),
//    CULT_REWARD_SCHEME_STARTED_EVENT("cult_reward_scheme_started"),
//    CULT_REWARD_SCHEME_ACTIVATED_EVENT("cult_reward_scheme_activated"),
//    WALK_IN_EVENT("walk_in"),
//    BOOKING_CANCELLED("cult_booking_cancelled"),
//    BOOKING_DROPOUT("cult_booking_dropout"),
//    CULT_MEMBERSHIP_PAUSED("cult_membership_paused"),
//    MIND_MEMBERSHIP_PAUSED("mind_membership_paused"),
//    MEMBERSHIP_UNPAUSED("membership_unpaused"),
//    SPOT_OFFER_ACTIVATED("CULT_SPOT_OFFER_ACTIVATED"),
//    USER_PARQ_DETAILS_SUCCESS("USER_PARQ_DETAILS_SUCCESS"),
//    USER_PARQ_DETAILS_FAILURE("USER_PARQ_DETAILS_FAILURE"),
//    CULT_MOMENTS_PUBLISHED("CULT_MOMENTS_PUBLISHED"),
//    USER_POSTURE_FEEDBACK_EVENT_NAME("USER_POSTURE_FEEDBACK_RECEIVED"),
//    CONFLICTING_MEMBERSHIP_BROKEN("conflicting_membership_broken"),
//    HOME_PAGE_PREFERENCE_UPDATED("home_page_preference_updated"),
//    HABIT_FORMED("habit_formed"),
    FITNESS_WEEKLY_REPORT_GENERATED("FITNESS_WEEKLY_REPORT_GENERATED");
    
    private final String value;
    
    RashiUserActivityEventName(String value) {
        this.value = value;
    }
    
    public String getValue() {
        return value;
    }
}