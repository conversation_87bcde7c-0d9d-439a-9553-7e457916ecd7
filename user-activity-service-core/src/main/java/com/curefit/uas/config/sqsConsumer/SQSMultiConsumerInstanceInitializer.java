package com.curefit.uas.config.sqsConsumer;

import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.commons.messaging.consumer.BaseSqsConsumer;
import com.curefit.uas.cache.UASLockService;
import com.curefit.uas.consumers.ActivityStoreSqsConsumer;
import com.curefit.uas.consumers.fitnessReport.UpdateFitnessReportSqsConsumer;
import com.curefit.uas.services.ConfigStoreWrapperService;
import com.curefit.uas.services.fitnessReportService.UpdateFitnessReportServiceFactory;
import com.curefit.uas.services.streak.ActivityStreakEvaluatorService;
import com.curefit.uas.services.streak.StreakService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Initializes and manages multiple SQS consumer instances based on dynamic configuration.
 * Handles enabling/disabling consumers, periodic status checks, and graceful shutdown.
 *
 * <p>Supported consumers are defined in the {@link ConsumerType} enum. Configuration is fetched
 * from the config store and consumers are started/stopped accordingly.</p>
 *
 * NOTE: This class now uses a scalable registry pattern for SQS consumers. To add a new consumer, simply add a new ConsumerDescriptor to the consumerDescriptors list.
 */
@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
@EnableScheduling
public class SQSMultiConsumerInstanceInitializer {
    private static final String CONSUMER_ENABLED_CONFIG_KEY = "consumerEnabledConfig";

    @Autowired
    ObjectMapper objectMapper;
    
    @Autowired
    UpdateFitnessReportServiceFactory updateFitnessReportServiceFactory;

    @Autowired
    StreakService streakService;

    @Autowired
    ActivityStreakEvaluatorService activityStreakEvaluatorService;

    @Autowired
    RollbarService rollbarService;
    
    @Autowired
    UASLockService lockService;

    @Autowired
    ConfigStoreWrapperService configStoreWrapperService;
    
    @Value("${app.environment}")
    String appEnvironment;
    
    @Value("${consumer.update-fitness-report.queueUrl}")
    String updateFitnessReportQueueUrl;
    
    @Value("${consumer.activity-store.queueUrl}")
    String activityStoreQueueUrl;

    // List of all registered consumer descriptors. Add new consumers here to scale.
    private final Map<String, ConsumerDescriptor> consumerDescriptorMap;

    {
        // Instance initializer block to set up the consumer descriptor map
        consumerDescriptorMap = Map.of(
            "activityStoreConsumer",
            new ConsumerDescriptor(
                "activityStoreConsumer",
                (config, consumerId) -> new ActivityStoreSqsConsumer(
                    consumerId,
                    activityStoreQueueUrl,
                    config.getBatchSize(),
                    config.getParallelProcessingBatchSize(),
                    streakService,
                    objectMapper,
                    rollbarService,
                    lockService,
                    activityStreakEvaluatorService
                ),
                new ConcurrentHashMap<>()
            ),
            "updateFitnessReportConsumer",
            new ConsumerDescriptor(
                "updateFitnessReportConsumer",
                (config, consumerId) -> new UpdateFitnessReportSqsConsumer(
                    consumerId,
                    updateFitnessReportQueueUrl,
                    config.getBatchSize(),
                    config.getParallelProcessingBatchSize(),
                    objectMapper,
                    updateFitnessReportServiceFactory,
                    rollbarService
                ),
                new ConcurrentHashMap<>()
            )
            // Add new consumers here!
        );
    }

    /**
     * Periodically checks and updates the status of all registered SQS consumers based on config.
     * Uses the consumerDescriptors list for generic handling.
     */
    @Scheduled(fixedDelayString = "${sqs.consumer.status.check.interval:60000}")
    public void checkAndUpdateConsumerStatus() {
        try {
            // Fetch consumer configuration from config store
            Map<String, SQSMultiConsumerQueueConfig> configMap = configStoreWrapperService.getConfigValue(CONSUMER_ENABLED_CONFIG_KEY);
            if (configMap == null || configMap.isEmpty()) {
                log.warn("No consumer enabled configs found in config store for CONSUMER_ENABLED_CONFIG_KEY={} consumerConfigMap={}", CONSUMER_ENABLED_CONFIG_KEY, objectMapper.writeValueAsString(configMap));
                return;
            }

            // Iterate over all registered consumer descriptors
            for (String key : configMap.keySet()) {
                ConsumerDescriptor descriptor = consumerDescriptorMap.get(key);
                if (descriptor == null) continue;
                SQSMultiConsumerQueueConfig config = configMap.get(key);
                boolean enabled = config != null && config.getEnabledEnvs().contains(appEnvironment);
                Map<String, BaseSqsConsumer> map = descriptor.getConsumerMap();
                if (enabled && map.isEmpty()) {
                    for (int i = 0; i < config.getConsumerCountPerPod(); i++) {
                        String consumerId = generateConsumerId(descriptor.getConfigKey(), i);
                        try {
                            map.put(consumerId, descriptor.getConsumerFactory().apply(config, consumerId));
                        } catch (Exception e) {
                            handleConsumerInitializationError(descriptor.getConfigKey(), consumerId, "", e);
                        }
                    }
                } else if (!enabled && !map.isEmpty()) {
                    shutDownConsumersForMap(map);
                }
            }
        } catch (Exception e) {
            log.error("Error in periodic consumer status check", e);
            rollbarService.error(e, "Error in periodic consumer status check");
        }
    }

    /**
     * Generates a unique consumer ID based on queue URL and a suffix.
     * @param queueUrl The SQS queue URL
     * @param suffix The suffix (index or UUID)
     * @return The generated consumer ID
     */
    private String generateConsumerId(String queueUrl, Object suffix) {
        String queueName = queueUrl.substring(queueUrl.lastIndexOf('/') + 1);
        return queueName + "_" + suffix;
    }

    /**
     * Handles errors during consumer initialization and logs them.
     * @param consumerType The type of consumer
     * @param consumerId The consumer ID
     * @param queueUrl The queue URL
     * @param e The exception thrown
     */
    private void handleConsumerInitializationError(String consumerType, String consumerId, String queueUrl, Exception e) {
        String errorMessage = String.format("Failed to start %s consumerId:%s for queue:%s", consumerType, consumerId, queueUrl);
        log.error(errorMessage, e);
        rollbarService.error(e, errorMessage);
    }

    /**
     * Shuts down all consumers gracefully. Called during bean destruction.
     */
    @PreDestroy
    public void shutDownAllConsumers() {
        for (ConsumerDescriptor descriptor : consumerDescriptorMap.values()) {
            shutDownConsumersForMap(descriptor.getConsumerMap());
        }
    }

    /**
     * Shuts down all consumers in the provided map.
     * @param sqsConsumerMap The map of consumer IDs to consumer instances
     * @param <T> The type of BaseSqsConsumer
     */
    public <T extends BaseSqsConsumer> void shutDownConsumersForMap(Map<String, T> sqsConsumerMap) {
        sqsConsumerMap.keySet().forEach(consumerId -> {
            try {
                BaseSqsConsumer consumer = sqsConsumerMap.get(consumerId);
                log.info("Shutting down consumerId:{}", consumerId);
                consumer.shutDown();
            } catch (Exception e) {
                log.error("Failed to shutdown consumerId:{}", consumerId, e);
                rollbarService.error(e, "Failed to shutdown consumerId:" + consumerId);
            }
        });
    }
}
