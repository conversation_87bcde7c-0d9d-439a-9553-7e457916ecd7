package com.curefit.uas.config.sqsConsumer;

import com.curefit.commons.messaging.consumer.BaseSqsConsumer;
import lombok.Getter;

import java.util.Map;
import java.util.function.BiFunction;

/**
 * Descriptor for a consumer type, holding config key, factory, and consumer map.
 */

@Getter
public class ConsumerDescriptor {
    private final String configKey;
    private final BiFunction<SQSMultiConsumerQueueConfig, String, BaseSqsConsumer> consumerFactory;
    private final Map<String, BaseSqsConsumer> consumerMap;

    public ConsumerDescriptor(String configKey,
                             BiFunction<SQSMultiConsumerQueueConfig, String, BaseSqsConsumer> consumerFactory,
                             Map<String, BaseSqsConsumer> consumerMap) {
        this.configKey = configKey;
        this.consumerFactory = consumerFactory;
        this.consumerMap = consumerMap;
    }
}
