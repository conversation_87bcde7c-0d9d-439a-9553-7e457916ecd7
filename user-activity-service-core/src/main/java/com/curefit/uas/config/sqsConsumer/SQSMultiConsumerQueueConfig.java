package com.curefit.uas.config.sqsConsumer;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = lombok.AccessLevel.PRIVATE)
@Validated
public class SQSMultiConsumerQueueConfig {
//    @NotBlank
//    String queueUrl = "";
    
//    boolean enabled = false;
    int batchSize = 10;
    int parallelProcessingBatchSize = 1;
    int consumerCountPerPod = 1;
    List<String> enabledEnvs = new ArrayList<>();
}
