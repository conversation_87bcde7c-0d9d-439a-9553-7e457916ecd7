package com.curefit.uas.mapper;

import com.curefit.hercules.pojo.BodyPart;
import com.curefit.uas.entities.UserImageEntity;
import com.curefit.uas.pojo.entries.UserImageEntry;
import com.curefit.uas.pojo.fitnessReport.FitnessReportBodyPart;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface CommonMapper {
    FitnessReportBodyPart transform(BodyPart bodyPart);
    BodyPart transform(FitnessReportBodyPart entry);
    UserImageEntry transform (UserImageEntity entity);
    UserImageEntity transform (UserImageEntry entry);
}
