package com.curefit.uas.publisher;

import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.SendMessageRequest;
import com.curefit.commons.integrations.rollbar.RollbarService;
import com.curefit.uas.pojo.fitnessReport.FitnessReportHabitAndMilestonesSqsMsg;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

@Configuration
@Slf4j
public class FitnessReportHabitAndMilestonesPublisher {
    private static final String LOG_TAG = FitnessReportHabitAndMilestonesPublisher.class.getName();
    @Autowired
    ObjectMapper objectMapper;
    @Autowired
    RollbarService rollbarService;
    @Autowired
    AmazonSQS amazonSQS;
    @Value("${consumer.fitness-report-habit-and-milestones.queueUrl}")
    String queueName;
    
    public void pushFitnessReportHabitAndMilestonesSqsMsg(FitnessReportHabitAndMilestonesSqsMsg fitnessReportHabitAndMilestonesSqsMsg) throws IOException {
        try {
            SendMessageRequest sendMessageRequest = new SendMessageRequest()
                    .withQueueUrl(queueName)
                    .withMessageBody(objectMapper.writeValueAsString(fitnessReportHabitAndMilestonesSqsMsg));
            amazonSQS.sendMessage(sendMessageRequest);
            log.info("{} :: Message request: {}", LOG_TAG, objectMapper.writeValueAsString(sendMessageRequest));
        } catch (JsonProcessingException e) {
            String errorStr = "Exception while pushing product to SQS from pushFitnessReportHabitAndMilestonesSqsMsg for userId: " + fitnessReportHabitAndMilestonesSqsMsg.getUserId();
            log.error(LOG_TAG + " :: " + errorStr);
            rollbarService.error(new Throwable(LOG_TAG + " :: " + errorStr + " :: " + e.getMessage()));
        }
        
    }
}
