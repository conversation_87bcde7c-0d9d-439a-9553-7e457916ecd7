package com.curefit.uas.repository.mongo;

import com.curefit.commons.sf.repository.BaseMongoRepository;
import com.curefit.uas.entities.FitnessReportEntity;
import com.curefit.uas.pojo.fitnessReport.FitnessReportInterval;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Repository
public interface FitnessReportRepository extends BaseMongoRepository<FitnessReportEntity> {
    
    // ToDo:: Test all repo methods
    Map<String, Integer> getTotalClassesAttendedForLatestFitnessReportForUserIds(List<Long> userIds, int pastNumberOfMonthsToQuery);
    List<FitnessReportEntity> findAllForUserIdAndStartDateLessThanEqual(Long userId, String weekMonday, FitnessReportInterval interval, Pageable pageable);
    FitnessReportEntity findAllByUserIdAndStartDateAndInterval(Long userId, String weekMonday, FitnessReportInterval interval);
    FitnessReportEntity findLastEntryBeforeDateForUserId(Long userId, String date);
    List<FitnessReportEntity> findEntriesByUserIdAfterStartDate(Long userId, String startDate, FitnessReportInterval interval, Pageable pageable);
    FitnessReportEntity getPreviousActiveWeekFitnessReport(Long userId, String startDate);
    FitnessReportEntity getNextActiveWeekFitnessReport(Long userId, String startDate);
    List<FitnessReportEntity> findAfterDate(Long userId, String date);
    FitnessReportEntity findByIdSecondaryRead(String id);
    
    FitnessReportEntity findByUserIdAndId(Long userId, String id);
    
    List<FitnessReportEntity> findByUserId(Long userId, Pageable pageable);
    FitnessReportEntity findTopByUserIdOrderByIdDesc(Long userId);
    
    List<FitnessReportEntity> findByUserIdsAndStartDate(List<Long> userIds, String startDate);
    
    void deleteById(int id);
    
    List<FitnessReportEntity> findAllByMySqlIdIn(List<Long> mySqlIds);
    Optional<FitnessReportEntity> findTopByOrderByMySqlIdDesc();
    List<FitnessReportEntity> findAllProjectionsForUserBetweenDatesInclusive(Long userId, String startDateFrom, String startDateTo, FitnessReportInterval interval);
    
    List<FitnessReportEntity> findProjectedFieldsForAllForUserIdAndStartDateLessThanEqual(Long userId, String weekMonday, FitnessReportInterval interval, Pageable pageable);
    List<FitnessReportEntity> findByMySqlIdBetweenInclusive(Long startingEntityId, Long endingEntityId);
    
    FitnessReportEntity findFirstByUserIdAndIntervalAndRetrieveIdAndStartDateAndEndDateAndTimezone(Long userId, FitnessReportInterval interval, Pageable pageable);
}
