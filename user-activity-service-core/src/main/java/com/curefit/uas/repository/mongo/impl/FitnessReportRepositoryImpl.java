package com.curefit.uas.repository.mongo.impl;

import com.curefit.uas.entities.FitnessReportEntity;
import com.curefit.uas.pojo.fitnessReport.FitnessReportInterval;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class FitnessReportRepositoryImpl {
    @Autowired
    @Qualifier("mongoTemplate")
    MongoTemplate secondaryReadTemplate;
    
//    @Autowired
//    @Qualifier("primaryReadWriteMongoTemplate")
//    MongoTemplate primaryTemplate;
    public Map<String, Integer> getTotalClassesAttendedForLatestFitnessReportForUserIds(List<Long> userIds, int pastNumberOfMonthsToQuery) {
        LocalDate startDate = LocalDate.now().minusMonths(pastNumberOfMonthsToQuery);
        
        // Build the aggregation pipeline
        MatchOperation matchStage = Aggregation.match(
                Criteria.where("userId").in(userIds)
                        .and("startDate").gte(startDate)
                        .and("deletedAt").is(null)
        );
        
        SortOperation sortStage = Aggregation.sort(Sort.by(Sort.Direction.DESC, "startDate"));
        
        GroupOperation groupStage = Aggregation.group("userId")
                .first("totalClassesAttended").as("totalClassesAttended");
        
        ProjectionOperation projectStage = Aggregation.project()
                .and("_id").as("userId")
                .andInclude("totalClassesAttended");
        
        Aggregation aggregation = Aggregation.newAggregation(
                matchStage,
                sortStage,
                groupStage,
                projectStage
        );
        
        List<FitnessReportEntity> results = secondaryReadTemplate.aggregate(
                aggregation,
                "fitness_report", // collection name
                FitnessReportEntity.class
        ).getMappedResults();
        
        // Map userId to totalClassesAttended
        return results.stream().collect(Collectors.toMap(
                r -> r.getUserId().toString(),
                FitnessReportEntity::getTotalClassesAttended
        ));
    }
    public List<FitnessReportEntity> findAllForUserIdAndStartDateLessThanEqual(Long userId, String weekMonday,
                                                                      FitnessReportInterval interval, Pageable pageable) {
        Query query = new Query(
                Criteria.where("userId").is(userId)
                        .and("deletedAt").is(null)
                        .and("startDate").lte(weekMonday)
                        .and("interval").is(interval)
        );
        query.with(pageable.getSort());
        query.with(PageRequest.of(pageable.getPageNumber(), pageable.getPageSize()));
        return secondaryReadTemplate.find(query, FitnessReportEntity.class);
    }
    public List<FitnessReportEntity> findProjectedFieldsForAllForUserIdAndStartDateLessThanEqual(Long userId, String weekMonday,
                                                                                         FitnessReportInterval interval, Pageable pageable) {
        Query query = new Query(
                Criteria.where("userId").is(userId)
                        .and("deletedAt").is(null)
                        .and("startDate").lte(weekMonday)
                        .and("interval").is(interval)
        );
        query.with(pageable.getSort());
        query.with(PageRequest.of(pageable.getPageNumber(), pageable.getPageSize()));
        query.fields()
                .include("id")
                .include("startDate")
                .include("endDate")
                .include("userId")
                .include("'streak'");
        return secondaryReadTemplate.find(query, FitnessReportEntity.class);
    }
    public List<FitnessReportEntity> findAllProjectionsForUserBetweenDatesInclusive(Long userId, String startDateFrom, String startDateTo, FitnessReportInterval interval) {
        Query query = new Query(
                Criteria.where("userId").is(userId)
                        .and("startDate").gte(startDateFrom).lte(startDateTo)
                        .and("interval").is(interval)
                        .and("deletedAt").is(null)
        );
        query.with(Sort.by(Sort.Order.desc("startDate")));
        query.fields()
                .include("startDate")
                .include("endDate")
                .include("id")
                .include("userId")
                .include("interval")
                .include("classesAttended")
                .include("caloriesBurned")
                .include("timezone")
                .include("workoutCounts");
        return secondaryReadTemplate.find(query, FitnessReportEntity.class);
    }
    public FitnessReportEntity findLastEntryBeforeDateForUserId(Long userId, String date) {
        Query query = new Query(
                Criteria.where("userId").is(userId)
                        .and("deletedAt").is(null)
                        .and("startDate").lt(date)
                        .and("interval").is(FitnessReportInterval.WEEKLY)
        );
        query.with(Sort.by(Sort.Order.desc("startDate")));
        query.with(PageRequest.of(0, 1));
        return secondaryReadTemplate.findOne(query, FitnessReportEntity.class);
    }
    public List<FitnessReportEntity> findEntriesByUserIdAfterStartDate(Long userId, String startDate,
                                                                       FitnessReportInterval interval, Pageable pageable) {
        Query query = new Query(
                Criteria.where("userId").is(userId)
                        .and("startDate").gt(startDate)
                        .and("deletedAt").is(null)
                        .and("interval").is(interval)
        );
        query.with(pageable.getSort());
        query.with(PageRequest.of(pageable.getPageNumber(), pageable.getPageSize()));
        return secondaryReadTemplate.find(query, FitnessReportEntity.class);
    }
    public FitnessReportEntity getPreviousActiveWeekFitnessReport(Long userId, String startDate) {
        Query query = new Query(
                Criteria.where("userId").is(userId)
                        .and("startDate").lt(startDate)
                        .and("interval").is(FitnessReportInterval.WEEKLY.toString())
                        .and("deletedAt").is(null)
        );
        query.with(Sort.by(Sort.Order.desc("startDate")));
//        query.with(PageRequest.of(0, 1));
        return secondaryReadTemplate.findOne(query, FitnessReportEntity.class);
    }
    public FitnessReportEntity getNextActiveWeekFitnessReport(Long userId, String startDate) {
        Query query = new Query(
                Criteria.where("userId").is(userId)
                        .and("startDate").gt(startDate)
                        .and("interval").is(FitnessReportInterval.WEEKLY.toString())
                        .and("deletedAt").is(null)
        );
        query.with(Sort.by(Sort.Order.asc("startDate")));
//        query.with(PageRequest.of(0, 1));
        return secondaryReadTemplate.findOne(query, FitnessReportEntity.class);
    }
    
    public FitnessReportEntity findByIdSecondaryRead(String id) {
        Query query = new Query(Criteria.where("id").is(id));
        return secondaryReadTemplate.findOne(query, FitnessReportEntity.class);
    }
    
    public FitnessReportEntity findByUserIdAndId(Long userId, String id) {
        Query query = new Query(
                Criteria.where("userId").is(userId)
                        .and("id").is(id)
        );
        return secondaryReadTemplate.findOne(query, FitnessReportEntity.class);
    }
    
    public List<FitnessReportEntity> findByUserId(Long userId, Pageable pageable) {
        Query query = new Query(Criteria.where("userId").is(userId));
        query.with(pageable);
        return secondaryReadTemplate.find(query, FitnessReportEntity.class);
    }
    
    public List<FitnessReportEntity> findByUserIdsAndStartDate(List<Long> userIds, String startDate) {
        Query query = new Query(
                Criteria.where("userId").in(userIds)
                        .and("startDate").is(startDate)
                        .and("deletedAt").is(null)
        );
        return secondaryReadTemplate.find(query, FitnessReportEntity.class);
    }
    
    public void deleteById(String id) {
        Query query = new Query(Criteria.where("id").is(id));
        secondaryReadTemplate.remove(query, FitnessReportEntity.class);
    }
    
    public FitnessReportEntity findFirstByUserIdAndIntervalAndRetrieveIdAndStartDateAndEndDateAndTimezone(
            Long userId, FitnessReportInterval interval, Pageable pageable) {
        Query query = new Query(
                Criteria.where("userId").is(userId)
                        .and("deletedAt").is(null)
                        .and("interval").is(interval)
        );
        query.fields()
                .include("id")
                .include("startDate")
                .include("endDate")
                .include("timezone");
        
        query.with(pageable.getSort());
        query.with(PageRequest.of(pageable.getPageNumber(), pageable.getPageSize()));
        
        return secondaryReadTemplate.findOne(query, FitnessReportEntity.class);
    }
    
    public List<FitnessReportEntity> findAfterDate(Long userId, String date) {
        Query query = new Query(
                Criteria.where("userId").is(userId)
                        .and("startDate").gte(date)
                        .and("deletedAt").is(null)
        );
        return secondaryReadTemplate.find(query, FitnessReportEntity.class);
    }
    public List<FitnessReportEntity> findByMySqlIdBetweenInclusive(Long startingEntityId, Long endingEntityId) {
        Criteria criteria = Criteria.where("mySqlId").gte(startingEntityId).lte(endingEntityId);
        Query query = new Query(criteria);
        return secondaryReadTemplate.find(query, FitnessReportEntity.class);
    }
    
}
