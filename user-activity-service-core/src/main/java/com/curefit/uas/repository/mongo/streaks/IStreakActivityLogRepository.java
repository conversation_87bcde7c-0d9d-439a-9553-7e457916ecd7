package com.curefit.uas.repository.mongo.streaks;

import com.curefit.commons.sf.repository.BaseMongoRepository;
import com.curefit.uas.entities.streaks.StreakActivityLogEntity;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IStreakActivityLogRepository extends BaseMongoRepository<StreakActivityLogEntity> {
    Optional<StreakActivityLogEntity> findFirstByActivityIdempotenceKey(String activityIdempotenceKey);
    List<StreakActivityLogEntity> findAllByActivityIdempotenceKeyIn(List<String> activityIdempotenceKeys);

    List<StreakActivityLogEntity> findAllByUserIdAndActivityDate(Long userId, String activityDate);
    
    @Query("{ 'userId' : ?0, 'activityDate' : { $gte : ?1, $lte : ?2 } }")
    List<StreakActivityLogEntity> findAllByUserIdAndDateInRange(Long userId, String startDate, String endDate);
    
    @Query("{ 'userId' : ?0, 'activityDate' : { $gte : ?1 } }")
    List<StreakActivityLogEntity> findAllByUserIdAndActivityDateGreaterThanEqual(Long userId, String date);
    
    List<StreakActivityLogEntity> findByUserId(Long userId, Pageable pageable);
}
