package com.curefit.uas.repository.mongo.impl;


import com.curefit.uas.entities.UserImageEntity;
import com.curefit.uas.enums.UserImageTag;
import com.curefit.uas.mapper.CommonMapper;
import com.curefit.uas.pojo.entries.UserImageEntry;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class UserImageRepositoryImpl {

    private final CommonMapper commonMapper;
    private final MongoTemplate secondaryReadTemplate;

    public List<UserImageEntry> findAllByUserIdAndTag(Long userId, UserImageTag tag) {
        Query query = new Query(
                Criteria.where("userId").is(userId)
                        .and("deletedAt").is(null)
                        .and("tag").is(tag)
        );

        List<UserImageEntity> entities = secondaryReadTemplate.find(query, UserImageEntity.class);
        return entities.stream()
                .map(commonMapper::transform)
                .collect(Collectors.toList());
    }
}