package com.curefit.uas.repository.jpa;

import com.curefit.commons.sf.repository.BaseMySQLRepository;
import com.curefit.uas.entities.UserTaskMappingEntity;

import java.util.List;

public interface IUserTaskMappingRepository extends BaseMySQLRepository<UserTaskMappingEntity> {

    List<UserTaskMappingEntity> findAllByUserIdAndState(Long userId, String state);

    UserTaskMappingEntity findFirstByUserIdAndTaskId(Long userId, Long taskId);
}
