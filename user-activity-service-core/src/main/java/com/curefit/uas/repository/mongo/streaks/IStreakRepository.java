package com.curefit.uas.repository.mongo.streaks;

import com.curefit.commons.sf.repository.BaseMongoRepository;
import com.curefit.uas.entities.streaks.StreakEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface IStreakRepository extends BaseMongoRepository<StreakEntity> {
    Optional<StreakEntity> findByUserId(Long userId);
    
    @Query("""
    {
      "streakCount": { "$gt": 0 },
      "tentativeStreakBreakDate": { "$ne": null, "$lt": ?0 },
      "streakPauseId": null
    }
    """)
    Page<StreakEntity> findStreaksToBreakOnDate(@Param("date") String date, Pageable pageable);
}
