package com.curefit.uas.repository.mongo.streaks;

import com.curefit.commons.sf.repository.BaseMongoRepository;
import com.curefit.uas.entities.streaks.StreakConfigEntity;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IStreakConfigRepository extends BaseMongoRepository<StreakConfigEntity> {
    Optional<StreakConfigEntity> findByConfigId(String configId);
    List<StreakConfigEntity> findAllByConfigIdIn(List<String> configIds);
}
