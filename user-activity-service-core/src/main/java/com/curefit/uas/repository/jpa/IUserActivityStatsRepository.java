package com.curefit.uas.repository.jpa;

import com.curefit.commons.sf.repository.BaseMySQLRepository;
import com.curefit.uas.entities.UserActivityStatsEntity;

import java.util.Optional;

public interface IUserActivityStatsRepository extends BaseMySQLRepository<UserActivityStatsEntity> {

    Optional<UserActivityStatsEntity> findTopByConfigIdAndActivityIdOrderByIdDesc(Long configId, String activityId);

    Optional<UserActivityStatsEntity> findByConfigIdAndActivityId(Long configId, String activityId);
}
