package com.curefit.uas.repository.mongo.streaks;

import com.curefit.commons.sf.repository.BaseMongoRepository;
import com.curefit.uas.entities.streaks.StreakPseudoActivityEntity;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IStreakPseudoActivityRepository extends BaseMongoRepository<StreakPseudoActivityEntity> {
    Optional<StreakPseudoActivityEntity> findByIdempotenceKey(String idempotenceKey);
    List<StreakPseudoActivityEntity> findAllByUserId(String userId);
    
    @Query("{ 'userId' : ?0, 'date' : { $gte : ?1, $lte : ?2 } }")
    List<StreakPseudoActivityEntity> findAllByUserIdAndDateInRange(String userId, String startDate, String endDate);
}
