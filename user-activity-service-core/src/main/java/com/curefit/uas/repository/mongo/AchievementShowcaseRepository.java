package com.curefit.uas.repository.mongo;

import com.curefit.uas.entities.AchievementShowcaseEntity;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Achievement Showcase operations
 */
public interface AchievementShowcaseRepository {
    
    /**
     * Save an achievement showcase entity
     */
    AchievementShowcaseEntity save(AchievementShowcaseEntity entity);
    
    /**
     * Save multiple achievement showcase entities
     */
    List<AchievementShowcaseEntity> saveAll(List<AchievementShowcaseEntity> entities);
    
    /**
     * Find by user ID
     */
    List<AchievementShowcaseEntity> findByUserId(Long userId, Pageable pageable);
    
    /**
     * Find by report date
     */
    List<AchievementShowcaseEntity> findByReportDate(String reportDate, Pageable pageable);
    
    /**
     * Find by user ID and report date
     */
    Optional<AchievementShowcaseEntity> findByUserIdAndReportDate(Long userId, String reportDate);
    
    /**
     * Count by report date
     */
    long countByReportDate(String reportDate);
    
    /**
     * Delete by Athena task ID
     */
    void deleteByAthenaTaskId(String athenaTaskId);
}
