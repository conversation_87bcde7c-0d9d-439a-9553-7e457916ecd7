package com.curefit.uas.repository.mongo;

import com.curefit.commons.sf.repository.BaseMongoRepository;
import com.curefit.uas.entities.FitnessReportEntity;
import com.curefit.uas.entities.UserImageEntity;
import com.curefit.uas.enums.UserImageTag;
import com.curefit.uas.pojo.entries.UserImageEntry;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserImageRepository extends BaseMongoRepository<UserImageEntity> {
    List<UserImageEntry> findAllByUserIdAndTag(Long userId, UserImageTag tag);
}
