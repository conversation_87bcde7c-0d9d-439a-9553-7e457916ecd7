package com.curefit.uas.repository.mongo.streaks;

import com.curefit.commons.sf.repository.BaseMongoRepository;
import com.curefit.uas.entities.streaks.BrokenStreakEntity;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IBrokenStreakRepository extends BaseMongoRepository<BrokenStreakEntity> {
    Optional<BrokenStreakEntity> findFirstByUserIdOrderByStreakVersionDesc(Long userId);
    
    @Query("{ 'userId' : ?0, 'lastActivityDate' : { $gte : ?1 } }")
    List<BrokenStreakEntity> findAllByUserIdAndLastActivityDateGreaterThanEqual(Long userId, String date);
    
    @Query("""
    {
      "userId": ?0,
      "$or": [
        {
          "firstActivityDate": { "$gte": ?1, "$lte": ?2 }
        },
        {
          "lastActivityDate": { "$gte": ?1, "$lte": ?2 }
        },
        {
          "$and": [
            { "firstActivityDate": { "$lte": ?1 } },
            { "lastActivityDate": { "$gte": ?1 } }
          ]
        },
        {
          "$and": [
            { "firstActivityDate": { "$lte": ?2 } },
            { "lastActivityDate": { "$gte": ?2 } }
          ]
        }
      ]
    }
    """)
    List<BrokenStreakEntity> findBrokenStreaksBetweenStartAndEndDate(Long userId, String startDate, String endDate);
}
