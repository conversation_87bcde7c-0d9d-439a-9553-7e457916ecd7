package com.curefit.uas.repository.mongo.streaks;

import com.curefit.commons.sf.repository.BaseMongoRepository;
import com.curefit.uas.entities.streaks.StreakPauseEntity;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface IStreakPauseRepository extends BaseMongoRepository<StreakPauseEntity> {
    @Query("""
    {
      "userId": ?0,
      "$or": [
        { "startDate": { "$lte": ?1 }, "endDate": { "$gte": ?1 } },
        { "startDate": { "$gte": ?1, "$lte": ?2 } }
      ]
    }
    """)
    List<StreakPauseEntity> findPausesBetweenIncludingDates(
            Long userId,String startDate, String endDate
    );
    
    @Query("""
        {
            "userId": ?0,
            "startDate": { "$lte": ?1 },
            "endDate": { "$gt": ?2 }
        }
        """)
    List<StreakPauseEntity> findPauseOnDate(Long userId, String dateToCheck);
    
    Optional<StreakPauseEntity> findByUserIdAndIsPauseActive(Long userId, boolean isPauseActive);
}
