package com.curefit.uas.repository.jpa;

import com.curefit.commons.sf.repository.BaseMySQLRepository;
import com.curefit.uas.entities.UserActivityStatsCohortConfigEntity;
import com.curefit.uas.types.UserActivityType;

import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface IUserActivityStatsCohortConfigRepository extends BaseMySQLRepository<UserActivityStatsCohortConfigEntity> {

    Optional<UserActivityStatsCohortConfigEntity> findTopByActivityTypeAndSegmentNameInOrderByIdDesc(UserActivityType activityType, Set<String> segmentNames);

    List<UserActivityStatsCohortConfigEntity> findByActivityTypeInAndSegmentNameIn(List<UserActivityType> activityTypes, Set<String> segmentNames);

    List<UserActivityStatsCohortConfigEntity> findAllByActiveIsTrue();

}