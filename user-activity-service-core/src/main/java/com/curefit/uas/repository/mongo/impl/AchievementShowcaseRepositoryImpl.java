package com.curefit.uas.repository.mongo.impl;

import com.curefit.uas.entities.AchievementShowcaseEntity;
import com.curefit.uas.repository.mongo.AchievementShowcaseRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * MongoDB repository implementation for Achievement Showcase operations
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AchievementShowcaseRepositoryImpl implements AchievementShowcaseRepository {
    
    @Qualifier("primaryReadWriteMongoTemplate")
    private final MongoTemplate primaryReadWriteTemplate;
    
    @Qualifier("mongoTemplate")
    private final MongoTemplate secondaryReadTemplate;
    
    @Override
    public AchievementShowcaseEntity save(AchievementShowcaseEntity entity) {
        log.debug("ASR: Saving achievement showcase for userId: {}", entity.getUserId());
        return primaryReadWriteTemplate.save(entity);
    }
    
    @Override
    public List<AchievementShowcaseEntity> saveAll(List<AchievementShowcaseEntity> entities) {
        log.info("ASR: Saving {} achievement showcase records", entities.size());
        return (List<AchievementShowcaseEntity>) primaryReadWriteTemplate.insertAll(entities);
    }
    
    @Override
    public List<AchievementShowcaseEntity> findByUserId(Long userId, Pageable pageable) {
        Query query = new Query(Criteria.where("userId").is(userId));
        query.with(pageable);
        return secondaryReadTemplate.find(query, AchievementShowcaseEntity.class);
    }
    
    @Override
    public List<AchievementShowcaseEntity> findByReportDate(String reportDate, Pageable pageable) {
        Query query = new Query(Criteria.where("reportDate").is(reportDate));
        query.with(pageable);
        return secondaryReadTemplate.find(query, AchievementShowcaseEntity.class);
    }
    
    @Override
    public Optional<AchievementShowcaseEntity> findByUserIdAndReportDate(Long userId, String reportDate) {
        Query query = new Query(
                Criteria.where("userId").is(userId)
                        .and("reportDate").is(reportDate)
        );
        AchievementShowcaseEntity result = secondaryReadTemplate.findOne(query, AchievementShowcaseEntity.class);
        return Optional.ofNullable(result);
    }
    
    @Override
    public long countByReportDate(String reportDate) {
        Query query = new Query(Criteria.where("reportDate").is(reportDate));
        return secondaryReadTemplate.count(query, AchievementShowcaseEntity.class);
    }
    
    @Override
    public void deleteByAthenaTaskId(String athenaTaskId) {
        Query query = new Query(Criteria.where("athenaTaskId").is(athenaTaskId));
        primaryReadWriteTemplate.remove(query, AchievementShowcaseEntity.class);
        log.info("ASR: Deleted achievement showcase records for athenaTaskId: {}", athenaTaskId);
    }

    @Override
    public AchievementShowcaseEntity upsert(AchievementShowcaseEntity entity) {
        Query query = new Query(Criteria.where("userId").is(entity.getUserId()));

        Update update = new Update()
                .set("classesAttended", entity.getClassesAttended())
                .set("calories", entity.getCalories())
                .set("totalMinutesWorked", entity.getTotalMinutesWorked())
                .set("weeksActive", entity.getWeeksActive())
                .set("favorateFormat1", entity.getFavorateFormat1())
                .set("favorateFormat1Classes", entity.getFavorateFormat1Classes())
                .set("formats", entity.getFormats())
                .set("favorateFormat2", entity.getFavorateFormat2())
                .set("favorateFormat3", entity.getFavorateFormat3())
                .set("squadFriendsCount", entity.getSquadFriendsCount())
                .set("trainerName", entity.getTrainerName())
                .set("trainerClasses", entity.getTrainerClasses())
                .set("packUtilisationCount", entity.getPackUtilisationCount())
                .set("countryPercentile", entity.getCountryPercentile())
                .set("citiesCount", entity.getCitiesCount())
                .set("favouriteCity1", entity.getFavouriteCity1())
                .set("favouriteCity2", entity.getFavouriteCity2())
                .set("favouriteCity3", entity.getFavouriteCity3())
                .set("centersVisited", entity.getCentersVisited())
                .set("reportDate", entity.getReportDate())
                .set("athenaTaskId", entity.getAthenaTaskId())
                .set("querySource", entity.getQuerySource())
                .set("processedAt", entity.getProcessedAt())
                .set("lastModifiedOn", System.currentTimeMillis());

        // Set createdOn only if it's a new document
        update.setOnInsert("createdOn", System.currentTimeMillis());
        update.setOnInsert("createdBy", "system");

        FindAndModifyOptions options = new FindAndModifyOptions()
                .returnNew(true)
                .upsert(true);

        AchievementShowcaseEntity result = primaryReadWriteTemplate.findAndModify(
                query, update, options, AchievementShowcaseEntity.class);

        log.debug("ASR: Upserted achievement showcase for userId: {}", entity.getUserId());
        return result;
    }

    @Override
    public List<AchievementShowcaseEntity> upsertAll(List<AchievementShowcaseEntity> entities) {
        log.info("ASR: Upserting {} achievement showcase records", entities.size());
        List<AchievementShowcaseEntity> results = new ArrayList<>();

        for (AchievementShowcaseEntity entity : entities) {
            try {
                AchievementShowcaseEntity result = upsert(entity);
                results.add(result);
            } catch (Exception e) {
                log.error("ASR: Failed to upsert entity for userId: {}", entity.getUserId(), e);
                // Continue with other entities
            }
        }

        return results;
    }
}
