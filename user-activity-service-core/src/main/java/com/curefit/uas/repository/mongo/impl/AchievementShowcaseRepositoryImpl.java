package com.curefit.uas.repository.mongo.impl;

import com.curefit.uas.entities.AchievementShowcaseEntity;
import com.curefit.uas.repository.mongo.AchievementShowcaseRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * MongoDB repository implementation for Achievement Showcase operations
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AchievementShowcaseRepositoryImpl implements AchievementShowcaseRepository {
    
    @Qualifier("primaryReadWriteMongoTemplate")
    private final MongoTemplate primaryReadWriteTemplate;
    
    @Qualifier("mongoTemplate")
    private final MongoTemplate secondaryReadTemplate;
    
    @Override
    public AchievementShowcaseEntity save(AchievementShowcaseEntity entity) {
        log.debug("ASR: Saving achievement showcase for userId: {}", entity.getUserId());
        return primaryReadWriteTemplate.save(entity);
    }
    
    @Override
    public List<AchievementShowcaseEntity> saveAll(List<AchievementShowcaseEntity> entities) {
        log.info("ASR: Saving {} achievement showcase records", entities.size());
        return (List<AchievementShowcaseEntity>) primaryReadWriteTemplate.insertAll(entities);
    }
    
    @Override
    public List<AchievementShowcaseEntity> findByUserId(Long userId, Pageable pageable) {
        Query query = new Query(Criteria.where("userId").is(userId));
        query.with(pageable);
        return secondaryReadTemplate.find(query, AchievementShowcaseEntity.class);
    }
    
    @Override
    public List<AchievementShowcaseEntity> findByReportDate(String reportDate, Pageable pageable) {
        Query query = new Query(Criteria.where("reportDate").is(reportDate));
        query.with(pageable);
        return secondaryReadTemplate.find(query, AchievementShowcaseEntity.class);
    }
    
    @Override
    public Optional<AchievementShowcaseEntity> findByUserIdAndReportDate(Long userId, String reportDate) {
        Query query = new Query(
                Criteria.where("userId").is(userId)
                        .and("reportDate").is(reportDate)
        );
        AchievementShowcaseEntity result = secondaryReadTemplate.findOne(query, AchievementShowcaseEntity.class);
        return Optional.ofNullable(result);
    }
    
    @Override
    public long countByReportDate(String reportDate) {
        Query query = new Query(Criteria.where("reportDate").is(reportDate));
        return secondaryReadTemplate.count(query, AchievementShowcaseEntity.class);
    }
    
    @Override
    public void deleteByAthenaTaskId(String athenaTaskId) {
        Query query = new Query(Criteria.where("athenaTaskId").is(athenaTaskId));
        primaryReadWriteTemplate.remove(query, AchievementShowcaseEntity.class);
        log.info("ASR: Deleted achievement showcase records for athenaTaskId: {}", athenaTaskId);
    }
}
