package com.curefit.uas.controllerAdvice;

import com.curefit.commons.integrations.rollbar.RollbarService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import javax.servlet.http.HttpServletRequest;

@RestControllerAdvice
public class FitnessReportExceptionHandler extends ResponseEntityExceptionHandler {
    @Autowired
    RollbarService rollbarService;
    
    @ExceptionHandler(Exception.class)
    public void handleException(Exception ex, HttpServletRequest request) throws Exception {
        if (request.getRequestURI().contains("/fitnessReport")) {
            rollbarService.error(ex, "Fitness Report API Failed for request uri = " + request.getRequestURI());
        }
        throw ex;
    }
}
