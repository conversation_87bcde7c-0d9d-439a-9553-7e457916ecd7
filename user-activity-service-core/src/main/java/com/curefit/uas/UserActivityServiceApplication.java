package com.curefit.uas;

import com.curefit.metricservice.Application;
import com.curefit.metricservice.utils.RestTemplateBean;
import com.curefit.uas.utils.TimeUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.context.annotation.*;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

import java.util.TimeZone;

@SpringBootApplication(exclude = {RedisAutoConfiguration.class})
@PropertySource("classpath:application-${spring.profiles.active}.properties")
@ComponentScan(
        basePackages = {
                "com.curefit.uas",
                "com.curefit.commons.integrations.rollbar",
                "com.curefit.commons.client",
                "com.curefit.segmentation",
                "com.curefit.rashi",
                "com.curefit.gymfit.client",
                "com.curefit.logging",
                "com.curefit.hercules",
                "com.curefit.cult",
                "com.curefit.ufs",
                "com.curefit.commons.sf",
                "com.curefit.commons.sf.util",
                "com.curefit.socialservice",
                "com.curefit.athena.service",
                "com.curefit.membership",
                "com.curefit.metricservice",
                "com.curefit.hamlet",
                "com.curefit.iris",
                "com.curefit.metabase",
                "com.curefit.athena.client", "com.curefit.athena.service.client", "com.curefit.trino.client"
        }, excludeFilters = @ComponentScan.Filter(
                    type = FilterType.ASSIGNABLE_TYPE,
                    classes = {RestTemplateBean.class, Application.class}
            ))
@Slf4j
@Configuration
@EnableWebMvc
@EnableJpaRepositories("com.curefit.uas.repository.jpa")
@ConfigurationPropertiesScan("com.curefit.uas.config")
public class UserActivityServiceApplication {

    public static void main(String[] args) {
        TimeZone.setDefault(TimeZone.getTimeZone(TimeUtil.INDIA_TIMEZONE_ID_STRING));
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        SpringApplication.run(UserActivityServiceApplication.class, args);
    }

    @Bean
    @Primary
    public ObjectMapper objectMapper(Jackson2ObjectMapperBuilder builder) {
        ObjectMapper mapper = builder.build();
        mapper.registerModule(new JavaTimeModule());
        mapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        return mapper;
    }

}
