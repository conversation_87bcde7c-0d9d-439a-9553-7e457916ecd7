app.environment=${ENVIRONMENT}
app.name=${APP_NAME}
spring.profiles.active=${ENVIRONMENT}
aws.region=ap-south-1
curefit.aws.region=ap-south-1
rollbar.disabled=${ROLLBAR_DISABLED:false}
#logging.level.org.springframework=${LOGGING_LEVEL_SPRING:INFO}
spring.jpa.hibernate.ddl-auto=update
log-enabled.distributed-lock=${LOG_ENABLED_DISTRIBUTED_LOCK:false}
# ConfigStore Doorman
configstore.url=${CONFIG_STORE_URL:http://config-store.stage.cure.fit.internal/}
configstore.apiKey=${CONFIG_STORE_KEY:user-activity-service-stage-access}
configstore.enabled=${CONFIG_STORE_ENABLED:true}

## Actuator
# custom actuator base path: use root mapping `/` instead of default `/actuator/`
management.endpoints.web.base-path=/

# prometheus
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.prometheus.enabled=true
management.endpoints.metrics.enable.root=true
management.prometheus.metrics.export.enabled=true
management.prometheus.metrics.export.descriptions=true

# override endpoint name for health check: `/health` => `/status`
management.endpoints.web.path-mapping.health=/status



## Mongo
spring.data.mongodb.uri=mongodb+srv://${MONGO_USER}:${MONGO_PASS}@${MONGO_CLUSTER:stage-shard-0}/user-activity-service?ssl=true&authSource=admin&retryWrites=true&w=majority&connectTimeoutMS=30000&compressors=zlib&appname=${APP_NAME}&readPreference=primary
mongo.replicas=3
spring.data.mongodb.auto-index-creation=true
entity-listener.mongo.enabled=${MONGO_ENTITY_LISTENER_ENABLED:false}

## sqs
# membership-updates-queue
consumer.membership-updates.queueUrl = ${MEMBERSHIP_UPDATES_QUEUE_URL:https://sqs.ap-south-1.amazonaws.com/035243212545/stage-uas-membership-updates-queue}
consumer.membership-updates.enabled=${MEMBERSHIP_UPDATES_CONSUMER_ENABLED:false}
consumer.membership-updates.batchSize=${MEMBERSHIP_UPDATES_BATCH_SIZE:10}

# uas-activity-store-queue
consumer.activity-store.queueUrl = ${UAS_ACTIVITY_STORE_QUEUE_URL:https://sqs.ap-south-1.amazonaws.com/035243212545/stage-uas-activity-store-queue}
consumer.activity-store.enabled=${UAS_ACTIVITY_STORE_CONSUMER_ENABLED:false}
consumer.activity-store.batchSize=${UAS_ACTIVITY_STORE_BATCH_SIZE:10}
consumer.activity-store.parallelProcessingBatchSize=${UAS_ACTIVITY_STORE_PARALLEL_PROCESS_BATCH_SIZE:1}
consumer.activity-store.consumerCountPerPod=${UAS_ACTIVITY_STORE_CONSUMER_COUNT_PER_POD:1}

consumer.gymfit-checkin.queueUrl = ${GYMFIT_CHECKIN_QUEUE_URL:https://sqs.ap-south-1.amazonaws.com/035243212545/stage-uas-gymfit-checkin-events}
consumer.athena-notification.queueUrl  = ${ATHENA_NOTIFICATION_QUEUE_URL:https://sqs.ap-south-1.amazonaws.com/035243212545/stage-uas-athena-service-notifications}
spring.main.allow-bean-definition-overriding=true
rashi.sns.topicArn=arn:aws:sns:ap-south-1:035243212545:production-rashi-user-events

## Make this env specific
consumer.update-fitness-report.queueUrl = ${UPDATE_FITNESS_REPORT_QUEUE_URL:https://sqs.ap-south-1.amazonaws.com/035243212545/stage-update-fitness-report}
consumer.update-fitness-report.enabled=${UPDATE_FITNESS_REPORT_CONSUMER_ENABLED:false}
consumer.update-fitness-report.batchSize=${UPDATE_FITNESS_REPORT_BATCH_SIZE:10}
consumer.update-fitness-report.parallelProcessingBatchSize=${UPDATE_FITNESS_REPORT_PARALLEL_PROCESS_BATCH_SIZE:1}
consumer.update-fitness-report.consumerCountPerPod=${UPDATE_FITNESS_REPORT_CONSUMER_COUNT_PER_POD:1}
consumer.live-update-fitness-report.queueUrl = ${LIVE_UPDATE_FITNESS_REPORT_QUEUE_URL:https://sqs.ap-south-1.amazonaws.com/035243212545/stage-live-class-attended}
consumer.play-update-fitness-report.queueUrl = ${PLAY_UPDATE_FITNESS_REPORT_QUEUE_URL:https://sqs.ap-south-1.amazonaws.com/035243212545/play_session_attended_stage}
consumer.ufs-update-fitness-report.queueUrl = ${UFS_UPDATE_FITNESS_REPORT_QUEUE_URL:https://sqs.ap-south-1.amazonaws.com/035243212545/stage-ufs-gym-wod-completion-events}
consumer.cult-class-update-fitness-report.queueUrl = ${CULT_CLASS_UPDATE_FITNESS_REPORT_QUEUE_URL:https://sqs.ap-south-1.amazonaws.com/035243212545/stage_update_fitness_history}
consumer.fitness-report-habit-and-milestones.queueUrl = ${FITNESS_REPORT_HABIT_AND_MILESTONES:https://sqs.ap-south-1.amazonaws.com/035243212545/stage-fitness-report-habit-and-milestones}

lombok.toString.callSuper = true

## metabase-service
metabaseBaseurl: https://metabase.curefit.co
metabaseUserName:${METABASE_USERNAME}
metabasePassword:${METABASE_PASSWORD}
metabaseConnectionTimeout: 600000
