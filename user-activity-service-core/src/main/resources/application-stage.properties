# Log
logging.file.path=logs/user-activity-service
logging.level.com.curefit=DEBUG
logging.level.org.springframework.web.filter.CommonsRequestLoggingFilter=DEBUG
logfile.path=logs/user-activity-service

server.undertow.accesslog.directory=/logs/user-activity-service
server.undertow.accesslog.enabled=true
server.undertow.accesslog.pattern=%t %a "%r" %s (%D ms)
server.port=5050

## Rollbar
rollbar.accessToken=c4c04cf95f4b45cbb8a71faf103a927c

# Actuator
# custom actuator base path: use root mapping `/` instead of default `/actuator/`
management.endpoints.web.base-path=

#Host
user-activity-service.url=http://user-activity-service.stage.cure.fit.internal

# override endpoint name for health check: `/health` => `/status`
management.endpoints.web.path-mapping.health=/status

spring.sleuth.log.slf4j.whitelisted-mdc-keys=X-Request-Id
spring.sleuth.propagation-keys=X-Request-Id

spring.flyway.enabled=true
spring.main.allow-circular-references = true


external.metric.service.baseUrl=http://metrics.production.cure.fit.internal
#rashi
#curefit.rashi.redis.host=platforms-cache.stage.cure.fit.internal
#rashi.userEvents.sns.topicArn=arn:aws:sns:ap-south-1:035243212545:stage-rashi-user-events
#services.rashi.baseUrl=http://rashi.stage.cure.fit.internal

# Mysql
spring.datasource.url = **************************************************************************************************************************************************************************************************************************************
spring.datasource.username =curefit
spring.datasource.password =cure!123

spring.data.redis.repositories.enabled=false

#segmentation-service
curefit.segmentation.redis.host=platforms-segmentation-cache.stage.cure.fit.internal
curefit.segmentation.redis.port=6379
curefit.segmentation.redis.userSetNamePrefix=user_
curefit.segmentation.redis.clusterEnabled=true
curefit.segmentation.redis.timeoutMs=500
curefit.segmentation.redis.maxSize=200
curefit.segmentation.redis.minIdle= 10
curefit.segmentation.redis.maxIdle= 50
#curefit.segmentation.redis.segmentNameToSegmentIdKey=segmentNamesToIds
curefit.services.segmentation-service.baseUrl=http://segmentation.stage.cure.fit.internal
curefit.services.segmentation-service.apiKey=e93b2a9e-1c40-4ff0-8466-41a5f31bad4f
curefit.services.segmentation-service.client.sqs.enabled=true
curefit.services.segmentation-service.rollbar.enabled=${SEGMENTATION_SERVICE_ROLLBAR_ENABLED:false}
curefit.sqs.queue.name.usage-metrics-queue=stage-segmentation-usage-metrics

# membership-service
membership-service.url=http://membership-service.stage.cure.fit.internal
membership-service.agent-host=${APP_NAME}

#loggingService
loggingService.baseUrl: http://logging-service.stage.cure.fit.internal
loggingService.redis.host: curefit-cf-api-cache.stage.cure.fit.internal
loggingService.redis.port: 6379
loggingService.redis.clusterEnabled: true
loggingService.redis.timeoutMs: 500

#Cult
cult.baseUrl=http://cultapi.stage.internal.cult.fit
cult.redis.host=curefit.y66lea.0001.aps1.cache.amazonaws.com
cult.redis.port=6379
cult.redis.clusterEnabled=false
cult.redis.timeoutMs=500
cult.redis.maxSize=50

hercules.baseUrl=http://hercules.stage.cure.fit.internal
hercules.apiKey=MFM4kAiMG0yfpW9x3q9xo6sPT4zX792W

#userFitnessService
ufs.baseUrl: http://user-fitness-service.stage.cure.fit.internal
ufs.apiKey: random-replace-once-provided
ufs.timeout: 10000

#redis
redis.host=cult-stage-cache.y66lea.0001.aps1.cache.amazonaws.com
redis.port=6379
redis.clusterEnabled=true
redis.timeoutMs=30000
redis.maxSize=50
redis.ttl=172800
redis.locks.namespace=user-activity-service

redis.default.host = curefit.y66lea.0001.aps1.cache.amazonaws.com
redis.default.port = 6379
redis.default.clusterEnabled = false
redis.default.timeoutMs = 500
redis.default.maxSize = 1000
redis.default.minIdle = 10
redis.default.maxIdle = 20

#rashi
services.rashi.baseUrl=http://rashi.stage.cure.fit.internal

#Rashi
curefit.rashi.redis.host=platforms-cache.stage.cure.fit.internal
curefit.rashi.redis.port=6379
curefit.rashi.redis.clusterEnabled=true
curefit.rashi.userEventsQueue=arn:aws:sns:ap-south-1:035243212545:stage-rashi-user-events

# Athena
athena-service.base.url=http://athena-service.stage.cure.fit.internal
athena-service.queryClient=TRINO

# Hamlet
external.hamlet.apiKey=f7e2f93d-c089-4132-84c7-a5b54bad57e9
external.hamlet.baseUrl=http://hamlet.stage.cure.fit.internal
external.hamlet.baseUrlV2=http://hamlet-v2.stage.cure.fit.internal

activity-logging.baseUrl=http://user-activity-service.stage.cure.fit.internal

#iris
iris.requestQueue: stage-iris-campaign
iris.baseUrl: http://iris.stage.cure.fit.internal
#trino
trino-api.baseUrl=http://bigquery-reports.stage.cure.fit.internal
trino-api.apiKey=${TRINO_API_KEY}
# social-service
redis.social-service.redis.host=curefit.y66lea.ng.0001.aps1.cache.amazonaws.com
redis.social-service.redis.clusterEnabled=false
