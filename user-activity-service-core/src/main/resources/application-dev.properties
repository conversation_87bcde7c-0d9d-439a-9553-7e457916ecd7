#dev
# Log
logging.file.path=logs/user-activity-service
logging.level.com.curefit=DEBUG
logging.level.org.springframework.web.filter.CommonsRequestLoggingFilter=DEBUG
logfile.path=logs/user-activity-service

## Rollbar
#rollbar.accessToken=${ROLLBAR_TOKEN}

#Host
user-activity-service.url=http://user-activity-service.stage.cure.fit.internal


# Actuator
# custom actuator base path: use root mapping `/` instead of default `/actuator/`
management.endpoints.web.base-path=

# override endpoint name for health check: `/health` => `/status`
management.endpoints.web.path-mapping.health=/status


# Mysql
spring.datasource.url = ********************************************************************************
spring.datasource.username=${MYSQL_USER}
spring.datasource.password=
spring.jpa.generate-ddl=true
spring.jpa.hibernate.ddl-auto=update

spring.sleuth.log.slf4j.whitelisted-mdc-keys=X-Request-Id
spring.sleuth.propagation-keys=X-Request-Id

spring.flyway.enabled=false
spring.main.allow-circular-references = true

aws.sqs.names.cfsFormSubmitted = ""
aws.sns.topicArns.rashi.userEvents = ""

#rashi
services.rashi.baseUrl=http://rashi.stage.cure.fit.internal

server.port=5050

external.metric.service.baseUrl=http://metrics.production.cure.fit.internal
#segmentation
curefit.segmentation.redis.userSetNamePrefix= user_
curefit.segmentation.redis.host= localhost
curefit.segmentation.redis.port= 6379
curefit.segmentation.redis.clusterEnabled= false
curefit.segmentation.redis.timeoutMs= 500
curefit.segmentation.redis.maxSize= 200
curefit.segmentation.redis.minIdle= 10
curefit.segmentation.redis.maxIdle= 50
curefit.services.segmentation-service.baseUrl=http://localhost:5001/
curefit.services.segmentation-service.apiKey=e93b2a9e-1c40-4ff0-8466-41a5f31bad4f
curefit.services.segmentation-service.client.sqs.enabled=true
curefit.services.segmentation-service.rollbar.enabled=${SEGMENTATION_SERVICE_ROLLBAR_ENABLED:false}
curefit.sqs.queue.name.usage-metrics-queue=stage-segmentation-usage-metrics

# membership-service
membership-service.url=http://localhost:8080
membership-service.agent-host=${APP_NAME}

#loggingService
loggingService.baseUrl: http://localhost:5070
loggingService.redis.host: localhost
loggingService.redis.port: 6379
loggingService.redis.clusterEnabled: false
loggingService.redis.timeoutMs: 4500

#cult-redis
cult.redis.host=localhost
cult.redis.port=6379
cult.redis.clusterEnabled=false
cult.redis.timeoutMs=4500
cult.redis.maxSize=50

cult.baseUrl=http://localhost:5000
cult.apiKey=66f7jWoQ3INWMlyye81Cm5q7z6q9LJfvF3mDR6zL

hercules.baseUrl=http://localhost:9009
hercules.apiKey=MFM4kAiMG0yfpW9x3q9xo6sPT4zX792W

#userFitnessService
ufs.baseUrl: http://localhost:5030
ufs.apiKey: random-replace-once-provided
ufs.timeout: 10000

#redis
redis.host=localhost
redis.port=6379
redis.clusterEnabled=true
redis.timeoutMs=30000
redis.maxSize=50
redis.ttl=172800
redis.locks.namespace=user-activity-service

redis.default.host = localhost
redis.default.port = 6379
redis.default.clusterEnabled = false
redis.default.timeoutMs = 4500
redis.default.maxSize = 20
redis.default.minIdle = 2
redis.default.maxIdle = 5

# Athena
athena-service.base.url=http://athena-service.stage.cure.fit.internal
athena-service.queryClient=TRINO
external.metric.service.baseUrl=http://metrics.production.cure.fit.internal

# Hamlet
external.hamlet.apiKey=f7e2f93d-c089-4132-84c7-a5b54bad57e9
external.hamlet.baseUrl=http://hamlet.stage.cure.fit.internal
external.hamlet.baseUrlV2=http://hamlet-v2.stage.cure.fit.internal

#iris
iris.requestQueue: stage-iris-campaign
iris.baseUrl: http://iris.stage.cure.fit.internal
#trino
trino-api.baseUrl=http://bigquery-reports.stage.cure.fit.internal
trino-api.apiKey=${TRINO_API_KEY}
# social-service
social-cache.redisHost=localhost
redis.social-service.redis.clusterEnabled=true
