# Log
logging.file.path=logs/user-activity-service
logging.level.com.curefit=INFO
logging.level.org.springframework.web.filter.CommonsRequestLoggingFilter=DEBUG
logfile.path=logs/user-activity-service

## Rollbar
rollbar.accessToken=b3cea602d13e47b4a8f67085e17d623d

#Host
user-activity-service.url=http://user-activity-service.production.cure.fit.internal

spring.sleuth.log.slf4j.whitelisted-mdc-keys=X-Request-Id
spring.sleuth.propagation-keys=X-Request-Id

spring.flyway.enabled=false
spring.main.allow-circular-references = true    
external.metric.service.baseUrl=http://metrics.production.cure.fit.internal

# Mysql
spring.datasource.url = *************************************************************************************************************************************************************************************************************************************************
spring.datasource.username =user_activity_service_rw
spring.datasource.password =cnTT9HwRXYbOFjF2

#rashi
#curefit.rashi.redis.host=platforms-cache.production.cure.fit.internal
#rashi.userEvents.sns.topicArn=arn:aws:sns:ap-south-1:035243212545:stage-rashi-user-events

spring.data.redis.repositories.enabled=false

## downstream service configs

#segmentation
curefit.segmentation.redis.host=platforms-segmentation-cache.production.cure.fit.internal
curefit.segmentation.redis.port=6379
curefit.segmentation.redis.userSetNamePrefix=user_
curefit.segmentation.redis.clusterEnabled=true
curefit.segmentation.redis.timeoutMs=500
curefit.segmentation.redis.maxSize=200
curefit.segmentation.redis.minIdle= 10
curefit.segmentation.redis.maxIdle= 50
curefit.segmentation.redis.segmentNameToSegmentIdKey=segmentNamesToIds
curefit.services.segmentation-service.baseUrl=http://segmentation.production.cure.fit.internal
curefit.services.segmentation-service.apiKey=11f241cd-181f-410e-ad49-b6e683276a54
curefit.services.segmentation-service.client.sqs.enabled=true
curefit.services.segmentation-service.rollbar.enabled=${SEGMENTATION_SERVICE_ROLLBAR_ENABLED:false}
curefit.sqs.queue.name.usage-metrics-queue=production-segmentation-usage-metrics

# membership-service
membership-service.url=http://membership-service.production.cure.fit.internal
membership-service.agent-host=${APP_NAME}

server.undertow.accesslog.directory=/logs/user-activity-service
server.undertow.accesslog.enabled=true
server.undertow.accesslog.pattern=%t %a "%r" %s (%D ms)
server.port=5050

#Cult
cult.baseUrl=http://cultapi.production.internal.cult.fit
cult.redis.host=prod-eatfit-redis-primary.production.cure.fit.internal
cult.redis.port=6379
cult.redis.clusterEnabled=false
cult.redis.timeoutMs=500
cult.redis.maxSize=50

#loggingService
loggingService.baseUrl: http://logging-service.production.cure.fit.internal
loggingService.redis.host: curefit-cf-api-cache.production.cure.fit.internal
loggingService.redis.port: 6379
loggingService.redis.clusterEnabled: true
loggingService.redis.timeoutMs: 500

hercules.baseUrl=http://hercules.production.cure.fit.internal
hercules.apiKey=MFM4kAiMG0yfpW9x3q9xo6sPT4zX792W

#userFitnessService
ufs.baseUrl: http://user-fitness-service.production.cure.fit.internal
ufs.apiKey: random-replace-once-provided
ufs.timeout: 10000


#redis
redis.host=cult-prod-social-service-cache.y66lea.clustercfg.aps1.cache.amazonaws.com
redis.port=6379
redis.clusterEnabled=true
redis.timeoutMs=30000
redis.maxSize=50
redis.ttl=172800
redis.locks.namespace=user-activity-service

redis.default.host = curefit-prod-default-cache.production.cure.fit.internal
redis.default.port = 6379
redis.default.clusterEnabled = true
redis.default.timeoutMs = 200
redis.default.maxSize = 500
redis.default.minIdle = 20
redis.default.maxIdle = 50

#rashi
services.rashi.baseUrl=http://rashi.production.cure.fit.internal

#Rashi
curefit.rashi.redis.host=platforms-cache.production.cure.fit.internal
curefit.rashi.redis.port=6379
curefit.rashi.redis.clusterEnabled=true
curefit.rashi.userEventsQueue=arn:aws:sns:ap-south-1:035243212545:production-rashi-user-events

# Athena
athena-service.base.url=http://athena-service.production.cure.fit.internal
athena-service.queryClient=TRINO

# Hamlet
external.hamlet.apiKey=f7e2f93d-c089-4132-84c7-a5b54bad57e9
external.hamlet.baseUrl=http://hamlet.production.cure.fit.internal
external.hamlet.baseUrlV2=http://hamlet-v2.production.cure.fit.internal

activity-logging.baseUrl=http://user-activity-service.production.cure.fit.internal

#iris
iris.requestQueue: production-iris-campaign
iris.baseUrl: http://iris.production.cure.fit.internal
#trino
trino-api.baseUrl=http://bigquery-reports.production.cure.fit.internal
trino-api.apiKey=${TRINO_API_KEY}
# social-service
redis.social-service.redis.host=cult-prod-social-service-cache-rw.production.cure.fit.internal
redis.social-service.redis.clusterEnabled=true
