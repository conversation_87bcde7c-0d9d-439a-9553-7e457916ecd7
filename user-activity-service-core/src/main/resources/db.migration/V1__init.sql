CREATE TABLE `states` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `name` varchar(256) NOT NULL,
      `created_date` datetime DEFAULT CURRENT_TIMESTAMP,
      `updated_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      `created_by` varchar(256) DEFAULT 'system',
      `version` INT(11) DEFAULT 0
      PRIMARY KEY (`id`),
      UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `tasks` (
     `id` int(11) NOT NULL AUTO_INCREMENT,
     `task_key` varchar(256) DEFAULT NULL,
     `title` varchar(256) DEFAULT '',
     `subtitle` varchar(256) DEFAULT '',
     `state` varchar(256) NOT NULL,
     `priority` int DEFAULT 999,
     `metadata` JSON DEFAULT NULL,
     `default_visibility` TINYINT(1) DEFAULT 0,
     `agent` varchar(256) DEFAULT '',
     `created_date` datetime DEFAULT CURRENT_TIMESTAMP,
     `updated_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
     `created_by` varchar(256) DEFAULT 'system',
     `version` INT(11) DEFAULT 1,
     `visibility_conditions` JSON DEFAULT NULL,
     PRIMARY KEY (`id`),
     KEY `state` (`state`),
     KEY `task_key` (`task_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE `user_task_mapping` (
     `id` int(11) NOT NULL AUTO_INCREMENT,
     `task_id` int NOT NULL,
     `user_id` int NOT NULL,
     `state` varchar(256) NOT NULL,
     `status` varchar(32) NOT NULL,
     `is_visible` TINYINT(1) DEFAULT 1,
     `created_date` datetime DEFAULT CURRENT_TIMESTAMP,
     `updated_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
     `created_by` varchar(256) DEFAULT 'system',
     `version` INT(11) DEFAULT 0,
     PRIMARY KEY (`id`),
     KEY `userId` (`user_id`),
     KEY `state` (`state`),
     KEY `taskId` (`task_id`),
     KEY `userId_state` (`user_id`,`state`),
     KEY `userId_taskId` (`user_id`, `task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;