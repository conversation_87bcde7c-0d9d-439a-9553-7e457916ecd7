<?xml version="1.0" encoding="UTF-8"?>
<project
        xmlns="http://maven.apache.org/POM/4.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.curefit</groupId>
    <artifactId>user-activity-service</artifactId>
    <version>2.4.14-PT5</version>
  </parent>
  <properties>
    <curefit.segmentation.version>4.7.10</curefit.segmentation.version>
    <user.service.version>3.0.1</user.service.version>
    <hercules.version>2.0.18</hercules.version>
  </properties>
  <artifactId>user-activity-service-core</artifactId>

  <dependencies>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-mongodb</artifactId>
      <version>2.7.9</version>
    </dependency>

    <dependency>
      <groupId>com.curefit</groupId>
      <artifactId>user-service-client</artifactId>
      <version>${user.service.version}</version>
    </dependency>
    <dependency>
      <groupId>com.curefit</groupId>
      <artifactId>user-service-common</artifactId>
      <version>${user.service.version}</version>
    </dependency>

    <dependency>
      <groupId>com.curefit</groupId>
      <artifactId>hamlet-common</artifactId>
      <version>0.14.6</version>
    </dependency>
    <dependency>
      <groupId>com.curefit</groupId>
      <artifactId>hamlet-client</artifactId>
      <version>0.14.6</version>
    </dependency>
    <dependency>
      <groupId>com.curefit</groupId>
      <artifactId>hercules-client</artifactId>
      <version>${hercules.version}</version>
    </dependency>
    <dependency>
      <groupId>com.curefit</groupId>
      <artifactId>hercules-common</artifactId>
      <version>${hercules.version}</version>
    </dependency>

    <dependency>
      <groupId>com.curefit.cult</groupId>
      <artifactId>cult-client</artifactId>
      <version>${cult.version}</version>
    </dependency>
    <dependency>
      <groupId>com.curefit.cult</groupId>
      <artifactId>cult-commons</artifactId>
      <version>${cult.version}</version>
    </dependency>
    <dependency>
      <groupId>org.flywaydb</groupId>
      <artifactId>flyway-mysql</artifactId>
      <version>8.4.4</version>
    </dependency>
    <dependency>
      <groupId>com.curefit</groupId>
      <artifactId>cf-commons</artifactId>
      <version>${curefit.common-sf.version}</version>
      <exclusions>
        <exclusion>
          <groupId>io.springfox</groupId>
          <artifactId>springfox-swagger2</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>${org.projectlombok.lombok.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springdoc</groupId>
      <artifactId>springdoc-openapi-ui</artifactId>
      <version>1.7.0</version>
    </dependency>
    <dependency>
      <groupId>com.curefit</groupId>
      <artifactId>user-activity-service-commons</artifactId>
      <version>${project.parent.version}</version>
    </dependency>
    <dependency>
      <groupId>com.curefit</groupId>
      <artifactId>base-common</artifactId>
      <version>0.0.35</version>
      <exclusions>
        <exclusion>
          <groupId>com.curefit.commons</groupId>
          <artifactId>common-integrations</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>com.curefit</groupId>
      <artifactId>user-activity-service-commons</artifactId>
      <version>${project.parent.version}</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.curefit</groupId>
      <artifactId>segmentation-client</artifactId>
      <version>${curefit.segmentation.version}</version>
    </dependency>

    <dependency>
      <groupId>com.curefit</groupId>
      <artifactId>segmentation-common</artifactId>
      <version>${curefit.segmentation.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.curefit</groupId>
          <artifactId>rashi-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.curefit</groupId>
          <artifactId>rashi-client</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct</artifactId>
      <version>1.5.3.Final</version>
    </dependency>
    <dependency>
      <artifactId>membership-commons</artifactId>
      <groupId>com.curefit</groupId>
      <version>${membership.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.curefit</groupId>
          <artifactId>rashi-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.curefit</groupId>
          <artifactId>rashi-client</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <artifactId>membership-client</artifactId>
      <groupId>com.curefit</groupId>
      <version>${membership.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.curefit</groupId>
          <artifactId>rashi-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.curefit</groupId>
          <artifactId>rashi-client</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.curefit</groupId>
      <artifactId>rashi-client</artifactId>
      <version>${rashi.version}</version>
    </dependency>
    <dependency>
      <groupId>com.curefit</groupId>
      <artifactId>rashi-common</artifactId>
      <version>${rashi.version}</version>
    </dependency>

    <dependency>
      <groupId>com.curefit.commons</groupId>
      <artifactId>curefit-commons-redis</artifactId>
      <version>${curefit-commons.version}</version>
    </dependency>

    <dependency>
      <groupId>com.curefit.commons</groupId>
      <artifactId>curefit-commons-lock</artifactId>
      <version>${curefit-commons.version}</version>
    </dependency>

    <dependency>
      <groupId>com.vladmihalcea</groupId>
      <artifactId>hibernate-types-52</artifactId>
      <version>2.9.7</version>
    </dependency>

    <dependency>
      <groupId>com.fasterxml.jackson.datatype</groupId>
      <artifactId>jackson-datatype-jsr310</artifactId>
      <version>${jackson.datatype.version}</version>
    </dependency>

    <dependency>
      <groupId>com.curefit</groupId>
      <artifactId>activity-logging-core</artifactId>
      <version>${project.parent.version}</version>
    </dependency>

    <dependency>
      <groupId>com.curefit.iris</groupId>
      <artifactId>iris-client</artifactId>
      <version>2.0.21</version>
    </dependency>

    <dependency>
      <groupId>com.curefit</groupId>
      <artifactId>metabase-client</artifactId>
      <version>1.5.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <groupId>com.curefit.commons</groupId>
          <artifactId>common-data-mongo</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.curefit.commons</groupId>
          <artifactId>common-test</artifactId>
        </exclusion>
      </exclusions>
    </dependency>


    <dependency>
      <groupId>com.curefit</groupId>
      <artifactId>athena-service-client</artifactId>
      <version>0.4.0</version>
    </dependency>

    <dependency>
      <groupId>com.curefit</groupId>
      <artifactId>logging-client</artifactId>
      <version>${logging-service.version}</version>
    </dependency>

    <dependency>
      <groupId>com.curefit</groupId>
      <artifactId>social-service-client</artifactId>
      <version>2.0.76</version>
    </dependency>

    <dependency>
      <groupId>com.curefit</groupId>
      <artifactId>ufs-client</artifactId>
      <version>${ufs.version}</version>
    </dependency>

    <dependency>
      <groupId>com.curefit</groupId>
      <artifactId>ufs-common</artifactId>
      <version>${ufs.version}</version>
    </dependency>

    <dependency>
      <groupId>com.curefit</groupId>
      <artifactId>athena-service-client</artifactId>
      <exclusions>
        <exclusion>
          <groupId>com.curefit</groupId>
          <artifactId>cf-commons-utils</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.curefit</groupId>
          <artifactId>hamlet-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.curefit</groupId>
          <artifactId>hamlet-client</artifactId>
        </exclusion>
      </exclusions>
      <version>0.4.0</version>
    </dependency>

    <!-- START -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
    </dependency>


    <!-- JUnit 5 (Jupiter) Dependencies -->
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <version>5.8.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <version>5.8.1</version>
      <scope>test</scope>
    </dependency>

    <!-- Mockito Dependencies -->
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>4.0.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-junit-jupiter</artifactId>
      <version>4.0.0</version>
      <scope>test</scope>
    </dependency>

    <!-- END -->
  </dependencies>
  <build>
    <plugins>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version>
        <configuration>
          <source>${maven.compiler.source}</source>
          <target>${maven.compiler.target}</target>
          <annotationProcessorPaths>
            <path>
              <groupId>org.mapstruct</groupId>
              <artifactId>mapstruct-processor</artifactId>
              <version>1.5.3.Final</version>
            </path>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
              <version>${org.projectlombok.lombok.version}</version>
            </path>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok-mapstruct-binding</artifactId>
              <version>0.2.0</version>
            </path>
          </annotationProcessorPaths>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version>
        <configuration>
          <source>${maven.compiler.source}</source>
          <target>${maven.compiler.target}</target>
          <annotationProcessorPaths>
            <path>
              <groupId>org.mapstruct</groupId>
              <artifactId>mapstruct-processor</artifactId>
              <version>1.5.3.Final</version>
            </path>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
              <version>${org.projectlombok.lombok.version}</version>
            </path>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok-mapstruct-binding</artifactId>
              <version>0.2.0</version>
            </path>
          </annotationProcessorPaths>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-deploy-plugin</artifactId>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-pmd-plugin</artifactId>
        <version>3.9.0</version>
      </plugin>
    </plugins>
  </reporting>


</project>
