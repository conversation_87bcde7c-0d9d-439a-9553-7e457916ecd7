package com.curefit.uas.pojo.entries;

import com.curefit.uas.enums.UserImageTag;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class UserImageEntry {

    @NotNull
    Long userId;

    @NotNull
    String imgUrl;

    @NotNull
    String date;

    @NotNull
    UserImageTag tag;

    String caption;

}