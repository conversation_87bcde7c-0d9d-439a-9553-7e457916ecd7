<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>user-activity-service</artifactId>
        <groupId>com.curefit</groupId>
        <version>2.4.14-PT5</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>activity-logging-core</artifactId>

    <properties>
        <maven.compiler.source>20</maven.compiler.source>
        <maven.compiler.target>20</maven.compiler.target>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-sts</artifactId>
        </dependency>

        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>sts</artifactId>
        </dependency>

        <dependency>
            <groupId>com.curefit</groupId>
            <artifactId>cf-commons-messaging</artifactId>
            <version>${curefit.common-sf.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <version>${jackson.datatype.version}</version>
        </dependency>
        <dependency>
            <groupId>com.curefit</groupId>
            <artifactId>ufs-client</artifactId>
            <version>${ufs.version}</version>
        </dependency>
        <dependency>
            <groupId>com.curefit</groupId>
            <artifactId>rashi-client</artifactId>
            <version>${rashi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.curefit</groupId>
            <artifactId>logging-client</artifactId>
            <version>${logging-service.version}</version>
        </dependency>
        <dependency>
            <groupId>com.curefit</groupId>
            <artifactId>hercules-common</artifactId>
            <version>2.0.17</version>
        </dependency>
        <dependency>
            <groupId>com.curefit</groupId>
            <artifactId>gymfit-common</artifactId>
            <version>2.0.4</version>
        </dependency>

        <dependency>
            <groupId>com.curefit</groupId>
            <artifactId>hercules-client</artifactId>
            <version>2.0.17</version>
        </dependency>
        <dependency>
            <groupId>com.curefit.cult</groupId>
            <artifactId>cult-client</artifactId>
            <version>${cult.version}</version>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20231013</version>
        </dependency>
        <dependency>
            <groupId>com.curefit</groupId>
            <artifactId>metric-client</artifactId>
            <version>1.10.6</version>
        </dependency>
        <dependency>
            <groupId>com.curefit.cult</groupId>
            <artifactId>cult-commons</artifactId>
            <version>${cult.version}</version>
        </dependency>
        <dependency>
            <groupId>com.curefit</groupId>
            <artifactId>activity-logging-commons</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.curefit.commons</groupId>
            <artifactId>common-integrations</artifactId>
            <version>1.2.26</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.curefit</groupId>
            <artifactId>user-activity-service-client</artifactId>
            <version>${project.parent.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.curefit</groupId>
            <artifactId>gymfit-common</artifactId>
            <version>${gymfit.version}</version>
        </dependency>
        <dependency>
            <groupId>com.curefit</groupId>
            <artifactId>gymfit-client</artifactId>
            <version>${gymfit.version}</version>
        </dependency>
        <dependency>
            <groupId>com.curefit.commons</groupId>
            <artifactId>curefit-commons-queue</artifactId>
            <version>${curefit.commons.version}</version>
        </dependency>
    </dependencies>

</project>