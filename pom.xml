<?xml version="1.0" encoding="UTF-8"?>
<project
		xmlns="http://maven.apache.org/POM/4.0.0"
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.curefit</groupId>
	<artifactId>user-activity-service</artifactId>
	<version>2.4.14-PT5</version>
	<name>user-activity-service</name>
	<packaging>pom</packaging>
	<modules>
		<module>user-activity-service-core</module>
		<module>user-activity-service-client</module>
		<module>user-activity-service-commons</module>
		<module>activity-logging-core</module>
		<module>activity-logging-client</module>
		<module>activity-logging-commons</module>
	</modules>
	<description>Java project based on CF Commons Serviceframework</description>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.7.16</version>
	</parent>
	<properties>
		<java.version>20</java.version>
		<maven.compiler.source>20</maven.compiler.source>
		<maven.compiler.target>20</maven.compiler.target>
		<curefit.common-sf.version>3.0.9-ALPHA</curefit.common-sf.version>
		<org.projectlombok.lombok.version>1.18.30</org.projectlombok.lombok.version>
		<spring.cloud-version>2021.0.8</spring.cloud-version>
		<aws-java-sdk.version>1.12.780</aws-java-sdk.version>
		<resilience4j.version>1.7.1</resilience4j.version>
		<cult.version>2.6.6</cult.version>
		<ufs.version>2.2.16</ufs.version>
		<rashi.version>3.9.6</rashi.version>
		<curefit-commons.version>2.3.10</curefit-commons.version>
		<jackson.datatype.version>2.15.3</jackson.datatype.version>
		<gymfit.version>2.0.12</gymfit.version>
		<logging-service.version>0.0.60</logging-service.version>
		<membership.version>3.5.2</membership.version>
		<curefit.commons.version>3.0.4</curefit.commons.version>
	</properties>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>io.netty</groupId>
				<artifactId>netty-bom</artifactId>
				<version>4.1.118.Final</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>org.flywaydb</groupId>
				<artifactId>flyway-mysql</artifactId>
				<version>8.4.4</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>${spring.cloud-version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>com.amazonaws</groupId>
				<artifactId>aws-java-sdk-bom</artifactId>
				<version>${aws-java-sdk.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>software.amazon.awssdk</groupId>
				<artifactId>bom</artifactId>
				<version>2.28.7</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>
	<build>
		<extensions>
			<extension>
				<groupId>org.springframework.build</groupId>
				<artifactId>aws-maven</artifactId>
				<version>5.0.0.RELEASE</version>
			</extension>
		</extensions>

		<plugins>
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<version>0.7.9</version>
				<executions>
					<execution>
						<id>prepare-unit-tests</id>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
					</execution>

					<execution>
						<id>prepare-agent</id>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
						<phase>pre-integration-test</phase>
						<configuration>
							<propertyName>itCoverageAgent</propertyName>
						</configuration>
					</execution>
				</executions>
			</plugin>

		</plugins>

	</build>
	<repositories>
		<repository>
			<id>github</id>
			<url>https://maven.pkg.github.com/curefit/*</url>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
		<repository>
			<id>curefit-s3-release-repo</id>
			<name>S3 Release Repository for Curefit</name>
			<url>s3://repo.mvn.com.curefit/release</url>
			<releases>
			</releases>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
		<repository>
			<id>curefit-s3-snapshot-repo</id>
			<name>S3 Snapshot Repository for Curefit</name>
			<url>s3://repo.mvn.com.curefit/snapshot</url>
			<releases>
				<enabled>false</enabled>
			</releases>
			<snapshots>
			</snapshots>
		</repository>
	</repositories>
	<scm>
		<developerConnection>scm:git:ssh://github.com/curefit/cf-commons-serviceframework.git</developerConnection>
		<tag>HEAD</tag>

	</scm>
	<!-- <developers><EMAIL></developers> -->
	<distributionManagement>
    		<repository>
      			<id>github</id>
      			<name>GitHub curefit Maven Registry</name>
      			<url>https://maven.pkg.github.com/curefit/user-activity-service</url>
    		</repository>
	</distributionManagement>

</project>
