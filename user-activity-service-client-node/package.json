{"name": "@curefit/user-activity-service-client", "version": "1.0.3", "types": "dist/index.d.ts", "description": "Http client for the user activity service", "main": "dist/index.js", "repository": {"type": "git", "url": "git+https://github.com/curefit/user-activity-service.git"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/curefit/user-activity-service/issues"}, "homepage": "https://github.com/curefit/user-activity-service#readme", "scripts": {"generate-barrels": "rm -rf dist; barrelsby --delete -e node_modules/*", "build": "yarn run generate-barrels && yarn run tslint-fix && yarn run tslint && tsc", "tslint": "tslint --project tsconfig.json", "tslint-fix": "tslint --fix --project tsconfig.json", "prepublishOnly": "yarn run build", "postversion": "git push origin master && git push origin master --tags && npm publish"}, "devDependencies": {"@types/node": "^16.11.38", "@types/node-fetch": "^2.5.8", "barrelsby": "^1.1.0", "tslint": "^5.16.0", "typescript": "5.1.3"}, "dependencies": {"@curefit/base": "7.11.0", "@curefit/base-common": "1.3.2", "@curefit/user-activity-service-commons": "1.0.2", "inversify": "5.0.1", "lodash": "^4.17.21", "moment": "^2.25.3", "node-fetch": "2.6.7"}, "resolutions": {"@curefit/base": "7.11.0"}}