import {M1JourneyTask} from "@curefit/user-activity-service-commons";

export interface M1JourneyResponse {
    expanded?: boolean,
    showProfile?: boolean,
    tasks: M1JourneyTask[]
}

export interface AchievementShowcaseData {
    id?: string;
    version?: number;
    createdOn?: number;
    lastModifiedOn?: number;
    createdBy?: string;
    userId: number;
    classesAttended?: number;
    calories?: number;
    totalMinutesWorked?: number;
    weeksActive?: number;
    favorateFormat1?: string;
    favorateFormat1Classes?: number;
    formats?: number;
    favorateFormat2?: string;
    favorateFormat3?: string;
    squadFriendsCount?: number;
    trainerName?: string;
    trainerClasses?: number;
    packUtilisationCount?: number;
    countryPercentile?: number;
    citiesCount?: number;
    favouriteCity1?: string;
    favouriteCity2?: string;
    favouriteCity3?: string;
    centersVisited?: number;
    reportDate?: string;
    athenaTaskId?: string;
    querySource?: string;
    processedAt?: number;
}

export interface AchievementShowcaseResponse {
    data: AchievementShowcaseData[];
    total?: number;
}