import {M1JourneyTask} from "@curefit/user-activity-service-commons";

export interface M1JourneyResponse {
    expanded?: boolean,
    showProfile?: boolean,
    tasks: M1JourneyTask[]
}

export interface AchievementShowcaseData {
    id?: string;
    version?: number;
    createdOn?: number;
    lastModifiedOn?: number;
    createdBy?: string;
    user_id: number;
    classes_attended?: number;
    calories?: number;
    total_minutes_worked?: number;
    weeks_active?: number;
    favorate_format_1?: string;
    favorate_format_1_classes?: number;
    formats?: number;
    favorate_format_2?: string;
    favorate_format_3?: string;
    squad_friends_count?: number;
    trainer_name?: string;
    trainer_classes?: number;
    pack_utilisation_count?: number;
    country_percentile?: number;
    cities_count?: number;
    favourite_city_1?: string;
    favourite_city_2?: string;
    favourite_city_3?: string;
    centers_visited?: number;
    reportDate?: string;
    athenaTaskId?: string;
    querySource?: string;
    processedAt?: number;
}

export interface AchievementShowcaseResponse {
    data: AchievementShowcaseData[];
    total?: number;
}