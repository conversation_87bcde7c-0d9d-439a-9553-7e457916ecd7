import {BackendConf} from "@curefit/base"
import {M1JourneyResponse, AchievementShowcaseData} from "./dtos";
import {FitnessReportResponse, FitnessReportSummaryResponse} from "./dto/fitnessReport/fitnessReport";

export interface UserActivityServiceBackendConf extends BackendConf {
}

export interface IUserActivityService {

    getM1JourneyWidgetInfo(userId: number): Promise<M1JourneyResponse>

    changeUserState(userId: number, state: string): Promise<boolean>

    getWeeklyAugmentedResponseForUserAndWeekDate(userId: number, weekDate: string): Promise<FitnessReportResponse>

    getFitnessReportSummary(userId: number, startDate: string, limit: number, offset: number): Promise<FitnessReportSummaryResponse>

    getAchievementShowcaseDataForUser(userId: number): Promise<AchievementShowcaseData | null>

}
