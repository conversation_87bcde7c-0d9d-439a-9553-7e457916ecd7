import {Container, inject, injectable} from "inversify"
import fetch from "node-fetch"

import {BASE_TYPES, DataErrorV2, FetchErrorV2, FetchUtilV2, <PERSON>og<PERSON>, <PERSON>rlParam, UrlUtil} from "@curefit/base"
import {IUserActivityService, UserActivityServiceBackendConf,} from "./IUserActivityService"
import {USER_ACTIVITY_SERVICE_CLIENT_TYPES} from "./ioc/UserActivityServiceClientTypes"
import {FetchAgentService} from "./FetchAgentService"
import {M1JourneyResponse, AchievementShowcaseData} from "./dtos";
import {FitnessReportResponse, FitnessReportSummaryResponse} from "./dto/fitnessReport/fitnessReport";

@injectable()
export class UserActivityService implements IUserActivityService {

    constructor(
        @inject(BASE_TYPES.FetchUtilV2) protected fetchUtilV2: FetchUtilV2,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
        @inject(BASE_TYPES.UrlUtil) private urlUtil: UrlUtil,
        @inject(USER_ACTIVITY_SERVICE_CLIENT_TYPES.UserActivityServiceBackendConf) private backendConf: UserActivityServiceBackendConf,
        @inject(USER_ACTIVITY_SERVICE_CLIENT_TYPES.FetchAgentService) protected fetchAgentService: FetchAgentService,
    ) {}

    private createUrl(path: string, params?: UrlParam[]) {
        return this.urlUtil.constructUrl(this.backendConf.url, path, params)
    }

    private parseEmptyResponse(response, remoteMethod: string): void {
        if (response.status === 200) {
            // ok
        } else if (response.status >= 400 && response.status < 500) {
            throw new DataErrorV2({
                title: `Bad request to ${remoteMethod}`,
                statusCode: response.status,
                message: response.statusText
            })
        } else {
            throw new FetchErrorV2({
                title: `Failed to call ${remoteMethod}`,
                statusCode: response.status,
                message: response.statusText
            })
        }
    }

    getM1JourneyWidgetInfo(userId: number): Promise<M1JourneyResponse> {
        const params = [{key: "userId", value: userId.toString()}]

        return fetch(this.createUrl(`/m1Journey/getWidgetInfo`, params), this.fetchUtilV2.get({})).then((response) => {
            return this.fetchUtilV2.parseResponse<M1JourneyResponse>(response)
        }).catch(err => {
            throw err
        })
        return
    }

    changeUserState(userId: number, state: string): Promise<boolean> {
        const params = [{key: "userId", value: userId.toString()} , {key: "state", value: state.toString()}]

        return fetch(this.createUrl(`/changeUserState`, params), this.fetchUtilV2.post({
            body: {} as any})).then((response) => {
            return this.fetchUtilV2.parseResponse<boolean>(response)
        }).catch(err => {
            throw err
        })
    }

    getWeeklyAugmentedResponseForUserAndWeekDate(userId: number, weekDate: string): Promise<FitnessReportResponse> {
        return fetch(this.createUrl(`/fitnessReport/getWeeklyAugmentedResponseForUserAndWeekDate/${userId}/${weekDate}`), this.fetchUtilV2.get()).then((response) => {
            return this.fetchUtilV2.parseResponse<FitnessReportResponse>(response)
        }).catch(err => {
            throw err
        })
    }

    getFitnessReportSummary(userId: number, startDate: string, limit: number, offset: number): Promise<FitnessReportSummaryResponse> {
        const params = [{key: "startDate", value: startDate.toString()} , {key: "limit", value: limit.toString()}, {key: "offset", value: offset.toString()}]
        return fetch(this.createUrl(`/fitnessReport/summary/${userId}`, params), this.fetchUtilV2.get()).then((response) => {
            return this.fetchUtilV2.parseResponse<FitnessReportSummaryResponse>(response)
        }).catch(err => {
            throw err
        })
    }

    getAchievementShowcaseDataForUser(userId: number): Promise<AchievementShowcaseData | null> {
        return fetch(this.createUrl(`/achievement-showcase/user/${userId}`), this.fetchUtilV2.get()).then((response) => {
            const data = this.fetchUtilV2.parseResponse<AchievementShowcaseData[]>(response)
            // Since each user has only one Achievement Showcase document, return the first one or null
            return data && data.length > 0 ? data[0] : null
        }).catch(err => {
            this.logger.error(`Failed to fetch achievement showcase data for user ${userId}`, err)
            throw err
        })
    }

}

export function UserActivityServiceFactory(kernel: Container) {
    return UserActivityService
}
