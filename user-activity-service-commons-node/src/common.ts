export type Status = "PURCHASED" | "CANCELLED" | "PAUSED" | "SUSPENDED"

export type BenefitName = "CF_LIVE" | "LIVE_CULT_PT_5_GRP" | "LIVE_CULT_SGT_3_GRP" | "LIVE_PERSONAL_TRAINING" |
    "LIVE_CULT_PT_20_PLUS_2_GRP" | "LIVE_CULT_SGT_6_GRP" | "LIVE_CULT_SGT_12_GRP" | "LIVE_SGT" | "LIVE_CULT_PT_20_GRP" |
    "LIVE_CULT_SGT_1_GRP" | "LIVE_CULT_PT_COMPLEMENTARY_GRP" | "LIVE_CULT_PT_5_PLUS_1_GRP" | "LIVE_CULT_SGT_12_PLUS_2_MONTHS_GRP" |
    "LIVE_CULT_SGT_6_MONTHS_PLUS_15_DAYS_GRP" | "LIVE_CULT_PT_20_PLUS_1_GRP" | "LIVE_CULT_SGT_6_PLUS_1_MONTHS_GRP" | "CULT"

export interface M1JourneyTask {
    taskType: TaskType,
    taskStatus: TaskStatus,
    taskName: TaskName
}

export enum TaskType {
    INFO = "INFO",
    TASK = "TASK"
}

export enum TaskStatus {
    COMPLETED = "COMPLETED",
    INCOMPLETE = "INCOMPLETE",
    UPCOMING = "UPCOMING"
}

export enum TaskName {
    PURCHASED = "PURCHASED",
    BOOKED = "BOOKED",
    PREPARED_FOR_FIRST_SESSION = "PREPARED_FOR_FIRST_SESSION",
    WHAT_TO_EXPECT = "WHAT_TO_EXPECT",
    INTENSITY = "INTENSITY",
    CANCELLED = "CANCELLED",
    REBOOKED = "REBOOKED",
    FITNESS_JOURNEY = "FITNESS_JOURNEY",
    ACCOUNTABILITY_PARTNER = "ACCOUNTABILITY_PARTNER",
    SORENESS = "SORENESS",
}