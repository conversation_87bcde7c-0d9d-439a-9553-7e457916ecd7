{"name": "@curefit/user-activity-service-commons", "version": "1.0.3-alpha.0", "types": "dist/index.d.ts", "description": "Types for the user activity service", "main": "dist/index.js", "repository": {"type": "git", "url": "git+https://github.com/curefit/user-activity-service.git"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/curefit/user-activity-service/issues"}, "homepage": "https://github.com/curefit/user-activity-service#readme", "scripts": {"generate-barrels": "rm -rf dist; barrelsby --delete -e node_modules/*", "build": "yarn run generate-barrels && yarn run tslint-fix && yarn run tslint && tsc", "tslint": "tslint --project tsconfig.json", "tslint-fix": "tslint --fix --project tsconfig.json", "prepublish": "yarn run tslint && tsc", "postversion": "git push origin master && git push origin master --tags && npm publish"}, "devDependencies": {"@types/node": "^16.11.38", "@types/node-fetch": "^2.5.8", "barrelsby": "^1.0.2", "tslint": "^5.16.0", "typescript": "5.1.3"}, "dependencies": {}, "resolutions": {}}